# MarketKing React Dashboard

Bu klasör MarketKing eklentisinin React tabanlı satıcı dashboard'unu içerir.

## Klasör Yapısı

```
react-dashboard/
├── src/
│   ├── components/
│   │   ├── layout/
│   │   ├── dashboard/
│   │   ├── charts/
│   │   └── common/
│   ├── hooks/
│   ├── utils/
│   ├── styles/
│   ├── types/
│   └── App.tsx
├── public/
├── package.json
├── webpack.config.js
└── tsconfig.json
```

## Kurulum

1. `npm install` - Bağımlılıkları yükle
2. `npm run dev` - Geliştirme modunda çalıştır
3. `npm run build` - Production build oluştur

## Özellikler

- TypeScript desteği
- Modern React hooks
- Responsive tasarım
- Chart.js entegrasyonu
- WordPress REST API entegrasyonu
