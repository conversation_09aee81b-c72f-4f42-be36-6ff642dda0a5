import { ApiResponse } from '../types';

// WordPress REST API base URL'ini al
const getApiBaseUrl = (): string => {
  // WordPress'te global olarak tanımlanan değişkenleri kullan
  const wpData = (window as any).marketkingReactData;
  return wpData?.apiUrl || '/wp-json/marketking/v1';
};

// WordPress nonce'ını al
const getNonce = (): string => {
  const wpData = (window as any).marketkingReactData;
  return wpData?.nonce || '';
};

class ApiService {
  private baseUrl: string;
  private nonce: string;

  constructor() {
    this.baseUrl = getApiBaseUrl();
    this.nonce = getNonce();
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      'X-WP-Nonce': this.nonce,
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'GET',
    });
  }

  async post<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }
}

export const apiService = new ApiService();
