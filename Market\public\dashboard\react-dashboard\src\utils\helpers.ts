// Para formatı
export const formatCurrency = (amount: number, currency: string = 'TRY'): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

// Tarih formatı
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(dateObj);
};

// Kısa tarih formatı
export const formatShortDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(dateObj);
};

// Sayı formatı (binlik ayırıcı ile)
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('tr-TR').format(num);
};

// Sipariş durumu rengi
export const getOrderStatusColor = (status: string): string => {
  const statusColors: { [key: string]: string } = {
    pending: '#ffa726',
    processing: '#42a5f5',
    completed: '#66bb6a',
    cancelled: '#ef5350',
    refunded: '#ab47bc',
    'on-hold': '#ff7043',
  };
  return statusColors[status] || '#9e9e9e';
};

// Sipariş durumu metni
export const getOrderStatusText = (status: string): string => {
  const statusTexts: { [key: string]: string } = {
    pending: 'Beklemede',
    processing: 'İşleniyor',
    completed: 'Tamamlandı',
    cancelled: 'İptal Edildi',
    refunded: 'İade Edildi',
    'on-hold': 'Bekletiliyor',
  };
  return statusTexts[status] || status;
};

// Ürün durumu rengi
export const getProductStatusColor = (status: string): string => {
  const statusColors: { [key: string]: string } = {
    publish: '#66bb6a',
    draft: '#ffa726',
    pending: '#42a5f5',
    private: '#ab47bc',
  };
  return statusColors[status] || '#9e9e9e';
};

// Ürün durumu metni
export const getProductStatusText = (status: string): string => {
  const statusTexts: { [key: string]: string } = {
    publish: 'Yayında',
    draft: 'Taslak',
    pending: 'Onay Bekliyor',
    private: 'Özel',
  };
  return statusTexts[status] || status;
};

// Metin kısaltma
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// URL slug oluşturma
export const createSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

// Dosya boyutu formatı
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Yüzde hesaplama
export const calculatePercentage = (value: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((value / total) * 100);
};

// Debounce fonksiyonu
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
