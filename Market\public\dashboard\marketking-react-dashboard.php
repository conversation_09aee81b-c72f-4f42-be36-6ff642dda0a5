<?php

if (!defined('ABSPATH')) { exit; }

/*

React Dashboard Page
* @version 1.0.0

Bu template dosyası React tabanlı dashboard'u yükler.
Mevcut PHP dashboard'un yerine kullanılabilir.

*/

// React dashboard loader'ı dahil et
require_once(MARKETKINGCORE_DIR . 'public/dashboard/react-dashboard-loader.php');

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title('|', true, 'right'); ?></title>
    <?php wp_head(); ?>
</head>

<body class="nk-body bg-lighter npc-general has-sidebar marketking-react-dashboard <?php
    $page = get_query_var('dashpage');
    if ($page === 'edit-product' || 'edit-booking-product'){
        echo 'post-type-product wc-wp-version-gte-55';
    }

    if (apply_filters('marketking_dashboard_rtl', false)){ 
        echo ' has-rtl';
    }
?>" <?php if (apply_filters('marketking_dashboard_rtl', false)){ echo 'dir="rtl"'; }?>>

    <?php
    // Kullanıcı kontrolü
    $user_id = get_current_user_id();
    if (marketking()->is_vendor_team_member()){
        $user_id = marketking()->get_team_member_parent();
    }

    // Vendor kontrolü
    if (!marketking()->is_vendor($user_id)) {
        ?>
        <div class="nk-app-root">
            <div class="nk-main">
                <div class="nk-wrap">
                    <div class="nk-content">
                        <div class="container-fluid">
                            <div class="nk-content-inner">
                                <div class="nk-content-body">
                                    <div class="alert alert-warning">
                                        <h4><?php esc_html_e('Erişim Reddedildi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                        <p><?php esc_html_e('Bu sayfaya erişim için satıcı hesabınız olması gerekiyor.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        wp_footer();
        echo '</body></html>';
        exit;
    }

    // Dashboard panel kontrolü
    if (!marketking()->vendor_has_panel('dashboard')){
        ?>
        <div class="nk-app-root">
            <div class="nk-main">
                <div class="nk-wrap">
                    <div class="nk-content">
                        <div class="container-fluid">
                            <div class="nk-content-inner">
                                <div class="nk-content-body">
                                    <div class="alert alert-info">
                                        <h4><?php esc_html_e('Panel Erişimi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                        <p><?php esc_html_e('Bu hesabın dashboard paneline erişim yetkisi bulunmuyor. Ana satıcı hesabını kullanın veya bu bölüm için izin talep edin.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        wp_footer();
        echo '</body></html>';
        exit;
    }

    // Tatil modu kontrolü
    if (marketking()->is_on_vacation($user_id)){
        ?>
        <div class="alert alert-fill alert-danger alert-icon" style="margin: 20px;">
            <em class="icon ni ni-sun-fill"></em> 
            <strong>
                <?php esc_html_e('Mağazanız tatil modunda ve ürünler satın alınamıyor. Bunu Ayarlar -> Tatil bölümünden değiştirebilirsiniz.','marketking-multivendor-marketplace-for-woocommerce'); ?>
            </strong>
        </div>
        <?php 
    }
    ?>

    <!-- React Dashboard Container -->
    <div id="marketking-react-dashboard" class="marketking-react-dashboard">
        <!-- React uygulaması buraya yüklenecek -->
        <div class="nk-app-root">
            <div class="nk-loading-container">
                <div class="nk-spinner"></div>
                <p><?php esc_html_e('Dashboard yükleniyor...', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
            </div>
        </div>
    </div>

    <!-- Fallback: React yüklenemezse PHP dashboard'a yönlendir -->
    <noscript>
        <div class="nk-app-root">
            <div class="nk-main">
                <div class="nk-wrap">
                    <div class="nk-content">
                        <div class="container-fluid">
                            <div class="nk-content-inner">
                                <div class="nk-content-body">
                                    <div class="alert alert-warning">
                                        <h4><?php esc_html_e('JavaScript Gerekli', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                        <p>
                                            <?php esc_html_e('Bu dashboard JavaScript gerektirir. Lütfen tarayıcınızda JavaScript\'i etkinleştirin.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                        </p>
                                        <a href="<?php echo esc_url(add_query_arg('use_legacy', '1')); ?>" class="btn btn-primary">
                                            <?php esc_html_e('Klasik Dashboard\'u Kullan', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </noscript>

    <?php
    // Footer scriptleri - sadece gerekli sayfalarda
    $page = get_query_var('dashpage');
    if ($page === 'edit-booking-order' || $page === 'edit-booking-product' || $page === 'edit-product' || $page === 'manage-order' || $page === 'edit-coupon' || $page === 1) {
        ?>
        <div id="marketking_footer_hidden" style="display: none;">
            <?php
            if (apply_filters('marketking_display_footer_scripts', true)){
                wp_footer();
            }
            ?>
        </div>
        <?php
    } else {
        wp_footer();
    }
    ?>

</body>
</html>
