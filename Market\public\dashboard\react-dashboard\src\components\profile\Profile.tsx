import React from 'react';

const Profile: React.FC = () => {
  return (
    <div className="nk-content">
      <div className="nk-content-inner">
        <div className="nk-content-body">
          <div className="nk-block-head nk-block-head-sm">
            <div className="nk-block-between">
              <div className="nk-block-head-content">
                <h4 className="nk-block-title page-title">Profil A<PERSON>ları</h4>
                <div className="nk-block-des text-soft">
                  <p>Profil bilgilerinizi ve mağaza ayarlarınızı yönetin.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="nk-block">
            <div className="card">
              <div className="card-aside-wrap">
                <div className="card-inner card-inner-lg">
                  <div className="nk-block-head nk-block-head-lg">
                    <div className="nk-block-between">
                      <div className="nk-block-head-content">
                        <h4 className="nk-block-title">Ki<PERSON><PERSON><PERSON> Bilgiler</h4>
                        <div className="nk-block-des">
                          <p>Temel profil bilgileriniz ve mağaza ayarları.</p>
                        </div>
                      </div>
                      <div className="nk-block-head-content align-self-start d-lg-none">
                        <a href="#" className="toggle btn btn-icon btn-trigger mt-n1">
                          <em className="icon ni ni-menu-alt-r"></em>
                        </a>
                      </div>
                    </div>
                  </div>
                  
                  <div className="nk-block">
                    <div className="nk-data data-list">
                      <div className="data-head">
                        <h6 className="overline-title">Temel Bilgiler</h6>
                      </div>
                      <div className="data-item">
                        <div className="data-col">
                          <span className="data-label">Ad Soyad</span>
                          <span className="data-value">-</span>
                        </div>
                        <div className="data-col data-col-end">
                          <span className="data-more">
                            <em className="icon ni ni-forward-ios"></em>
                          </span>
                        </div>
                      </div>
                      <div className="data-item">
                        <div className="data-col">
                          <span className="data-label">E-posta</span>
                          <span className="data-value">-</span>
                        </div>
                        <div className="data-col data-col-end">
                          <span className="data-more">
                            <em className="icon ni ni-forward-ios"></em>
                          </span>
                        </div>
                      </div>
                      <div className="data-item">
                        <div className="data-col">
                          <span className="data-label">Telefon</span>
                          <span className="data-value text-soft">Belirtilmemiş</span>
                        </div>
                        <div className="data-col data-col-end">
                          <span className="data-more">
                            <em className="icon ni ni-forward-ios"></em>
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="nk-data data-list">
                      <div className="data-head">
                        <h6 className="overline-title">Mağaza Bilgileri</h6>
                      </div>
                      <div className="data-item">
                        <div className="data-col">
                          <span className="data-label">Mağaza Adı</span>
                          <span className="data-value">-</span>
                        </div>
                        <div className="data-col data-col-end">
                          <span className="data-more">
                            <em className="icon ni ni-forward-ios"></em>
                          </span>
                        </div>
                      </div>
                      <div className="data-item">
                        <div className="data-col">
                          <span className="data-label">Mağaza URL</span>
                          <span className="data-value">-</span>
                        </div>
                        <div className="data-col data-col-end">
                          <span className="data-more">
                            <em className="icon ni ni-forward-ios"></em>
                          </span>
                        </div>
                      </div>
                      <div className="data-item">
                        <div className="data-col">
                          <span className="data-label">Açıklama</span>
                          <span className="data-value text-soft">Belirtilmemiş</span>
                        </div>
                        <div className="data-col data-col-end">
                          <span className="data-more">
                            <em className="icon ni ni-forward-ios"></em>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg">
                  <div className="card-inner-group">
                    <div className="card-inner">
                      <div className="user-card">
                        <div className="user-avatar bg-primary">
                          <span>U</span>
                        </div>
                        <div className="user-info">
                          <span className="lead-text">Kullanıcı</span>
                          <span className="sub-text"><EMAIL></span>
                        </div>
                        <div className="user-action">
                          <div className="dropdown">
                            <a className="btn btn-icon btn-trigger mr-n2" href="#">
                              <em className="icon ni ni-more-v"></em>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="card-inner">
                      <div className="user-account-info py-0">
                        <h6 className="overline-title-alt">Hesap Durumu</h6>
                        <div className="user-balance">
                          <div className="user-balance-sub">
                            <span className="sub-text">Mevcut Bakiye</span>
                            <span className="lead-text">₺0,00</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="card-inner p-0">
                      <ul className="link-list-menu">
                        <li>
                          <a href="#" className="active">
                            <em className="icon ni ni-user-fill-c"></em>
                            <span>Kişisel Bilgiler</span>
                          </a>
                        </li>
                        <li>
                          <a href="#">
                            <em className="icon ni ni-lock-alt-fill"></em>
                            <span>Güvenlik Ayarları</span>
                          </a>
                        </li>
                        <li>
                          <a href="#">
                            <em className="icon ni ni-activity-round-fill"></em>
                            <span>Hesap Aktivitesi</span>
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
