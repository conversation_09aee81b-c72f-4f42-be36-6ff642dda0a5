import React from 'react';
import { DashboardStats } from '../../types';
import { formatCurrency, formatNumber } from '../../utils/helpers';

interface StatsCardsProps {
  stats: DashboardStats | null;
}

const StatsCards: React.FC<StatsCardsProps> = ({ stats }) => {
  if (!stats) {
    return (
      <div className="col-12">
        <div className="card">
          <div className="card-inner">
            <div className="nk-spinner"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Mevcut Bakiye */}
      <div className="col-xxl-4 col-md-6">
        <div className="card is-dark h-100">
          <div className="nk-ecwg nk-ecwg1">
            <div className="card-inner">
              <div className="card-title-group">
                <div className="card-title">
                  <h6 className="title">Mevcut Bakiye</h6>
                </div>
                <div className="card-tools">
                  <a href="/earnings" className="link">
                    Kazançları Görüntüle
                  </a>
                </div>
              </div>
              <div className="data">
                <div className="amount">
                  {formatCurrency(stats.total_earnings)}
                </div>
                <div className="info">
                  <strong>{formatCurrency(stats.monthly_earnings)}</strong> 
                  {' '}bu ayki kazanç
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sipariş İstatistikleri */}
      <div className="col-xxl-4 col-md-6">
        <div className="card h-100">
          <div className="card-inner">
            <div className="card-title-group mb-2">
              <div className="card-title">
                <h6 className="title">Sipariş İstatistikleri</h6>
              </div>
            </div>
            <ul className="nk-store-statistics">
              <li className="item">
                <div className="info">
                  <div className="title">Toplam Siparişler</div>
                  <div className="count">{formatNumber(stats.total_orders)}</div>
                </div>
                <em className="icon bg-primary-dim ni ni-bag"></em>
              </li>
              <li className="item">
                <div className="info">
                  <div className="title">Bekleyen Siparişler</div>
                  <div className="count">{formatNumber(stats.pending_orders)}</div>
                </div>
                <em className="icon bg-warning-dim ni ni-clock"></em>
              </li>
              <li className="item">
                <div className="info">
                  <div className="title">Tamamlanan</div>
                  <div className="count">{formatNumber(stats.completed_orders)}</div>
                </div>
                <em className="icon bg-success-dim ni ni-check-circle"></em>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Genel İstatistikler */}
      <div className="col-xxl-4 col-md-6">
        <div className="card h-100">
          <div className="card-inner">
            <div className="card-title-group mb-2">
              <div className="card-title">
                <h6 className="title">Genel İstatistikler</h6>
              </div>
            </div>
            <ul className="nk-store-statistics">
              <li className="item">
                <div className="info">
                  <div className="title">Toplam Ürünler</div>
                  <div className="count">{formatNumber(stats.total_products)}</div>
                </div>
                <em className="icon bg-info-dim ni ni-package"></em>
              </li>
              <li className="item">
                <div className="info">
                  <div className="title">Toplam Müşteriler</div>
                  <div className="count">{formatNumber(stats.total_customers)}</div>
                </div>
                <em className="icon bg-purple-dim ni ni-users"></em>
              </li>
              <li className="item">
                <div className="info">
                  <div className="title">İptal Edilen</div>
                  <div className="count">{formatNumber(stats.cancelled_orders)}</div>
                </div>
                <em className="icon bg-danger-dim ni ni-cross-circle"></em>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default StatsCards;
