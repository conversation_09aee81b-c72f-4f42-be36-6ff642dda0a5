// MarketKing React Dashboard Ana Stil Do<PERSON>ası

// Mevcut DashLite CSS'lerini import et
@import url('../../../assets/css/dashlite.css');
@import url('../../../assets/css/nioicons.css');

// React Dashboard özel stilleri
.marketking-react-dashboard {
  // Loading spinner
  .nk-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    
    .nk-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #816bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }
    
    p {
      color: #8094ae;
      margin: 0;
    }
  }

  // Error container
  .nk-error-container {
    padding: 2rem;
    
    .alert {
      border-radius: 8px;
      
      h4 {
        margin-bottom: 0.5rem;
      }
      
      p {
        margin: 0;
      }
    }
  }

  // Sidebar overlay for mobile
  .nk-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    display: block;
    
    @media (min-width: 1200px) {
      display: none;
    }
  }

  // Sidebar active state
  .nk-sidebar.nk-sidebar-active {
    transform: translateX(0);
    
    @media (max-width: 1199px) {
      position: fixed;
      z-index: 1050;
    }
  }

  // Chart containers
  .nk-ecwg-ck {
    position: relative;
    
    canvas {
      max-height: 100% !important;
    }
  }

  // Stats cards
  .nk-store-statistics {
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem 0;
      border-bottom: 1px solid #e5ecf8;
      
      &:last-child {
        border-bottom: none;
      }
      
      .info {
        .title {
          font-size: 0.875rem;
          color: #8094ae;
          margin-bottom: 0.25rem;
        }
        
        .count {
          font-size: 1.25rem;
          font-weight: 600;
          color: #364a63;
        }
      }
      
      .icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        
        &.bg-primary-dim {
          background: rgba(129, 107, 255, 0.1);
          color: #816bff;
        }
        
        &.bg-warning-dim {
          background: rgba(255, 167, 38, 0.1);
          color: #ffa726;
        }
        
        &.bg-success-dim {
          background: rgba(102, 187, 106, 0.1);
          color: #66bb6a;
        }
        
        &.bg-info-dim {
          background: rgba(66, 165, 245, 0.1);
          color: #42a5f5;
        }
        
        &.bg-purple-dim {
          background: rgba(171, 71, 188, 0.1);
          color: #ab47bc;
        }
        
        &.bg-danger-dim {
          background: rgba(239, 83, 80, 0.1);
          color: #ef5350;
        }
      }
    }
  }

  // Table improvements
  .nk-tb-list {
    .nk-tb-item {
      &:hover {
        background-color: #f8f9fa;
      }
    }
    
    .nk-tb-col {
      a {
        color: inherit;
        text-decoration: none;
        
        &:hover {
          color: #816bff;
        }
      }
    }
  }

  // Badge improvements
  .badge {
    &.badge-dot-xs {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
    }
  }

  // User avatar improvements
  .user-avatar {
    &.sm {
      width: 32px;
      height: 32px;
      font-size: 0.875rem;
    }
    
    &.bg-purple-dim {
      background: rgba(171, 71, 188, 0.1);
      color: #ab47bc;
    }
  }

  // Menu badge
  .nk-menu-badge {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    
    &.badge-danger {
      background: #ef5350;
      color: white;
    }
  }

  // Responsive improvements
  @media (max-width: 768px) {
    .nk-tb-col {
      &.tb-col-md,
      &.tb-col-lg {
        display: none;
      }
    }
    
    .card-tools {
      .form-inline {
        flex-direction: column;
        gap: 0.5rem;
        
        .form-wrap {
          width: 100% !important;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .nk-tb-col {
      &.tb-col-sm,
      &.tb-col-mb {
        display: none;
      }
    }
    
    .nk-block-head {
      .nk-block-between {
        flex-direction: column;
        gap: 1rem;
      }
    }
  }
}

// Keyframes
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Dark mode support (opsiyonel)
@media (prefers-color-scheme: dark) {
  .marketking-react-dashboard {
    // Dark mode stilleri buraya eklenebilir
  }
}
