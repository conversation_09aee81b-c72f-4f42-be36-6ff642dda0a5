#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: MarketKing Core\n"
"POT-Creation-Date: 2025-05-19 18:05+0200\n"
"PO-Revision-Date: 2025-05-19 18:05+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: Poedit 3.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: marketking-core.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"

#. translators: 1: Deprecated property name, 2: Version number, 3: Alternative property name.
#: admin/assets/js/common.js:37
#, javascript-format
msgid "%1$s is deprecated since version %2$s! Use %3$s instead."
msgstr ""

#. translators: 1: Deprecated property name, 2: Version number.
#: admin/assets/js/common.js:45
#, javascript-format
msgid "%1$s is deprecated since version %2$s with no alternative available."
msgstr ""

#: admin/assets/js/common.js:527
msgid ""
"You are about to permanently delete these items from your site.\n"
"This action cannot be undone.\n"
"'Cancel' to stop, 'OK' to delete."
msgstr ""

#: admin/assets/js/common.js:1102
msgid "Dismiss this notice."
msgstr ""

#: admin/assets/js/common.js:1922
msgid "Expand Main menu"
msgstr ""

#: admin/assets/js/common.js:1925
msgid "Collapse Main menu"
msgstr ""

#: admin/class-marketking-core-admin.php:147
#: admin/class-marketking-core-admin.php:156
msgid "Vendor Commissions"
msgstr ""

#: admin/class-marketking-core-admin.php:246
#: includes/class-marketking-core-helper.php:2290
#: includes/class-marketking-core.php:2173
#: includes/class-marketking-core.php:2174
#: includes/class-marketking-core.php:2199
msgid "Earning"
msgstr ""

#: admin/class-marketking-core-admin.php:333
#: includes/class-marketking-core.php:5165
msgid "Vendor:"
msgstr ""

#: admin/class-marketking-core-admin.php:343
msgid "Commission:"
msgstr ""

#: admin/class-marketking-core-admin.php:367
msgid "Add New Commission"
msgstr ""

#: admin/class-marketking-core-admin.php:373
msgid "- Choose Vendor -"
msgstr ""

#: admin/class-marketking-core-admin.php:382
#: admin/class-marketking-core-admin.php:2975
#: admin/class-marketking-core-admin.php:5266
#: admin/class-marketking-core-admin.php:5267
#: includes/class-marketking-core-activator.php:36
msgid "Vendors"
msgstr ""

#: admin/class-marketking-core-admin.php:394
msgid "Enter commission value..."
msgstr ""

#: admin/class-marketking-core-admin.php:414
msgid "star"
msgstr ""

#: admin/class-marketking-core-admin.php:416
msgid "stars"
msgstr ""

#: admin/class-marketking-core-admin.php:447
msgid "Composite Order"
msgstr ""

#: admin/class-marketking-core-admin.php:525
#: admin/class-marketking-core-admin.php:563
msgid "Filter by vendor..."
msgstr ""

#: admin/class-marketking-core-admin.php:688
msgid "Here you can change the vendor of this product."
msgstr ""

#: admin/class-marketking-core-admin.php:690
msgid "Product Vendor"
msgstr ""

#: admin/class-marketking-core-admin.php:693
#: public/dashboard/edit-product.php:646
msgid "Product Advertising"
msgstr ""

#: admin/class-marketking-core-admin.php:701
msgid "Here you can change the vendor of this order."
msgstr ""

#: admin/class-marketking-core-admin.php:703
msgid "Order Vendor"
msgstr ""

#: admin/class-marketking-core-admin.php:712
#: admin/class-marketking-core-settings.php:1432
#: admin/class-marketking-core-settings.php:2012
#: public/dashboard/manage-order.php:445
msgid "Shipping Tracking"
msgstr ""

#: admin/class-marketking-core-admin.php:752
#: public/dashboard/manage-order.php:475
msgid "Shipment via "
msgstr ""

#: admin/class-marketking-core-admin.php:764
#: public/dashboard/manage-order.php:487
msgid "Add another"
msgstr ""

#: admin/class-marketking-core-admin.php:772
#: public/dashboard/manage-order.php:495
msgid "Create Shipment"
msgstr ""

#: admin/class-marketking-core-admin.php:792
#: public/dashboard/manage-order.php:515
msgid "Provider Name"
msgstr ""

#: admin/class-marketking-core-admin.php:794
#: public/dashboard/manage-order.php:517
msgid "Enter the shipping provider name"
msgstr ""

#: admin/class-marketking-core-admin.php:800
#: public/dashboard/manage-order.php:523
msgid "Tracking Number"
msgstr ""

#: admin/class-marketking-core-admin.php:802
#: public/dashboard/manage-order.php:525
msgid "Enter the tracking number"
msgstr ""

#: admin/class-marketking-core-admin.php:808
#: public/dashboard/manage-order.php:531
msgid "Tracking URL"
msgstr ""

#: admin/class-marketking-core-admin.php:810
#: public/dashboard/manage-order.php:533
msgid "Enter the tracking URL"
msgstr ""

#: admin/class-marketking-core-admin.php:815
#: public/dashboard/manage-order.php:538
msgid "Create shipment"
msgstr ""

#: admin/class-marketking-core-admin.php:829
#: public/dashboard/edit-product.php:657
msgid "This product is already advertised:"
msgstr ""

#: admin/class-marketking-core-admin.php:829
#: admin/class-marketking-core-admin.php:928
#: includes/class-marketking-core.php:3544
#: public/dashboard/edit-product.php:657 public/dashboard/products.php:365
msgid "days left"
msgstr ""

#: admin/class-marketking-core-admin.php:832
msgid "This product is not advertised"
msgstr ""

#: admin/class-marketking-core-admin.php:837
#: admin/class-marketking-core-admin.php:839
msgid "Advertise"
msgstr ""

#: admin/class-marketking-core-admin.php:838
#: public/dashboard/edit-product.php:739
msgid "Number of advertising days"
msgstr ""

#: admin/class-marketking-core-admin.php:840
msgid "Remove advertisement"
msgstr ""

#: admin/class-marketking-core-admin.php:855
#: admin/class-marketking-core-admin.php:1075
#: admin/class-marketking-core-admin.php:1099
#: admin/class-marketking-core-admin.php:1136
#: admin/class-marketking-core-admin.php:1191
#: admin/class-marketking-core-admin.php:1624
#: admin/class-marketking-core-admin.php:2983
#: admin/class-marketking-core-admin.php:3088
#: admin/class-marketking-core-admin.php:3121
#: admin/class-marketking-core-admin.php:3338
#: admin/class-marketking-core-settings.php:1234
#: admin/class-marketking-core-settings.php:1250
#: includes/class-marketking-core-activator.php:189
#: public/class-marketking-core-public.php:266
#: public/class-marketking-core-public.php:968
#: public/class-marketking-core-public.php:1765
#: public/dashboard/templates/header-bar.php:339
msgid "Vendor"
msgstr ""

#: admin/class-marketking-core-admin.php:880
msgid "Other Vendors Selling This Product"
msgstr ""

#: admin/class-marketking-core-admin.php:1006
#: admin/class-marketking-core-admin.php:1027
msgid "Composite order "
msgstr ""

#: admin/class-marketking-core-admin.php:1072 public/dashboard/products.php:148
#: public/dashboard/products.php:187
msgid "Advertisement"
msgstr ""

#: admin/class-marketking-core-admin.php:1094
msgid "This represents the admin commission (earnings) for the order."
msgstr ""

#: admin/class-marketking-core-admin.php:1098
msgid "Admin Commission"
msgstr ""

#: admin/class-marketking-core-admin.php:1100
msgid "Sub-order of"
msgstr ""

#: admin/class-marketking-core-admin.php:1113
msgid "The customer has confirmed that they received this order."
msgstr ""

#: admin/class-marketking-core-admin.php:1117
msgid "Received"
msgstr ""

#: admin/class-marketking-core-admin.php:1222
msgid "This account is a subaccount. Its parent account is: "
msgstr ""

#: admin/class-marketking-core-admin.php:1325
msgid "Vendor Registration Data"
msgstr ""

#: admin/class-marketking-core-admin.php:1340
msgid "Data Collected at Registration"
msgstr ""

#: admin/class-marketking-core-admin.php:1349
msgid "Registration Option"
msgstr ""

#: admin/class-marketking-core-admin.php:1422
msgid "Download file"
msgstr ""

#: admin/class-marketking-core-admin.php:1428
msgid "The file did not upload correctly"
msgstr ""

#: admin/class-marketking-core-admin.php:1438
#: admin/class-marketking-core-admin.php:2276
#: admin/class-marketking-core-admin.php:3265 public/dashboard/payouts.php:532
msgid "State"
msgstr ""

#: admin/class-marketking-core-admin.php:1462
#: admin/class-marketking-core-admin.php:1519
#: admin/class-marketking-core-admin.php:1565
msgid "Registration Approval"
msgstr ""

#: admin/class-marketking-core-admin.php:1482
msgid "No group is set up. Please create a customer group"
msgstr ""

#: admin/class-marketking-core-admin.php:1486
msgid "Approve as regular customer"
msgstr ""

#: admin/class-marketking-core-admin.php:1500
msgid "Approve user"
msgstr ""

#: admin/class-marketking-core-admin.php:1506
msgid "Reject and delete user"
msgstr ""

#: admin/class-marketking-core-admin.php:1528
#: admin/class-marketking-core-admin.php:1574
msgid "Deactivate / unapprove user"
msgstr ""

#: admin/class-marketking-core-admin.php:1537
msgid "Update Registration Data"
msgstr ""

#: admin/class-marketking-core-admin.php:1549
#: admin/class-marketking-core-admin.php:5685
#: public/class-marketking-core-public.php:2484
msgid "Print"
msgstr ""

#: admin/class-marketking-core-admin.php:1559
#: admin/class-marketking-core-settings.php:1531
msgid "Vendor Registration"
msgstr ""

#: admin/class-marketking-core-admin.php:1605
msgid "Vendor Settings (MarketKing)"
msgstr ""

#: admin/class-marketking-core-admin.php:1619
msgid "This user is a"
msgstr ""

#: admin/class-marketking-core-admin.php:1622
#: admin/class-marketking-reviews-page.php:15
#: includes/class-marketking-core-activator.php:169
#: public/dashboard/dashboard-content.php:315 public/dashboard/orders.php:79
msgid "Customer"
msgstr ""

#: admin/class-marketking-core-admin.php:1631
#: admin/class-marketking-core-admin.php:1709
msgid "Vendor Settings"
msgstr ""

#: admin/class-marketking-core-admin.php:1640
#: admin/class-marketking-core-admin.php:2079
#: admin/class-marketking-core-admin.php:2984
#: admin/class-marketking-core-admin.php:3089
#: admin/class-marketking-core-admin.php:3122
#: admin/class-marketking-core-admin.php:3339
msgid "Vendor Group"
msgstr ""

#: admin/class-marketking-core-admin.php:1645
msgid "- No group (inactive) -"
msgstr ""

#: admin/class-marketking-core-admin.php:1647
#: admin/class-marketking-core-admin.php:2587
#: admin/class-marketking-core-admin.php:5254
#: admin/class-marketking-core-admin.php:5255
msgid "Vendor Groups"
msgstr ""

#: admin/class-marketking-core-admin.php:1669
msgid "Vendor Profile"
msgstr ""

#: admin/class-marketking-core-admin.php:1671
#: admin/class-marketking-core-admin.php:1690 public/dashboard/profile.php:282
#: public/dashboard/profile.php:299
msgid "clear"
msgstr ""

#: admin/class-marketking-core-admin.php:1671 public/dashboard/profile.php:282
msgid "Vendor Picture"
msgstr ""

#: admin/class-marketking-core-admin.php:1679
msgid "Click to select / upload a profile picture for the vendor."
msgstr ""

#: admin/class-marketking-core-admin.php:1691 public/dashboard/profile.php:300
msgid "Upload Banner"
msgstr ""

#: admin/class-marketking-core-admin.php:1692 public/dashboard/profile.php:301
msgid "Click to select / upload a banner for the store."
msgstr ""

#: admin/class-marketking-core-admin.php:1692
#: includes/class-marketking-core-activator.php:329
#: includes/class-marketking-core-activator.php:345
#: public/dashboard/profile.php:91 public/dashboard/profile.php:202
msgid "Store Name"
msgstr ""

#: admin/class-marketking-core-admin.php:1692
#: includes/class-marketking-core-activator.php:359
#: includes/class-marketking-core-activator.php:375
msgid "Store URL"
msgstr ""

#: admin/class-marketking-core-admin.php:1704
#: includes/class-marketking-core-activator.php:300
#: includes/class-marketking-core-activator.php:316
msgid "Phone Number"
msgstr ""

#: admin/class-marketking-core-admin.php:1704 public/dashboard/profile.php:98
#: public/dashboard/profile.php:210
msgid "Email"
msgstr ""

#: admin/class-marketking-core-admin.php:1715
msgid "Vendor can publish products directly"
msgstr ""

#: admin/class-marketking-core-admin.php:1735
msgid ""
"Vendor can bypass the \"pending / review\" status, and directly publish "
"products in the shop."
msgstr ""

#: admin/class-marketking-core-admin.php:1737
#: admin/class-marketking-core-admin.php:1768
msgid ""
" This setting cannot be disabled, because it is enabled globally in "
"MarketKing -> Settings, or at the group level."
msgstr ""

#: admin/class-marketking-core-admin.php:1746
msgid "Vendor can change order statuses"
msgstr ""

#: admin/class-marketking-core-admin.php:1766
msgid "Vendor can directly change order status for their own orders."
msgstr ""

#: admin/class-marketking-core-admin.php:1809
#: admin/class-marketking-core-admin.php:5465
#: admin/class-marketking-core-admin.php:5466
#: admin/class-marketking-core-settings.php:1693
#: includes/class-marketking-core-helper.php:304
#: includes/elementor/classes.php:948
#: public/dashboard/templates/profile-sidebar.php:137
#: public/templates/stores-list.php:86 public/templates/stores-list.php:253
msgid "Store Categories"
msgstr ""

#: admin/class-marketking-core-admin.php:1827
msgid "Advanced Settings"
msgstr ""

#: admin/class-marketking-core-admin.php:1833
msgid "Enable base site URL for this store"
msgstr ""

#: admin/class-marketking-core-admin.php:1843
msgid ""
"This vendor store URL will be added to the website base URL, and can be "
"accessed directly e.g. yoursite.com/storeurl"
msgstr ""

#: admin/class-marketking-core-admin.php:1854
msgid "Vendor Subscription ID"
msgstr ""

#: admin/class-marketking-core-admin.php:1873
#: admin/class-marketking-core-admin.php:1880
#: public/dashboard/edit-product.php:669
msgid "Advertising Credits"
msgstr ""

#: admin/class-marketking-core-admin.php:1882
msgid ""
"The vendor can use credits to purchase advertising for their own products."
msgstr ""

#: admin/class-marketking-core-admin.php:1894
msgid "Credit History Log"
msgstr ""

#: admin/class-marketking-core-admin.php:1896
msgid "Download a log of all credit changes for this vendor."
msgstr ""

#: admin/class-marketking-core-admin.php:1902
msgid "Download Vendor Credits History"
msgstr ""

#: admin/class-marketking-core-admin.php:1928
msgid "This user is a team member of the following vendor: "
msgstr ""

#: admin/class-marketking-core-admin.php:2026
msgid "Modified by admin for user profile (backend)"
msgstr ""

#: admin/class-marketking-core-admin.php:2090
msgid "Requires Approval"
msgstr ""

#: admin/class-marketking-core-admin.php:2102
msgid "Team Member: "
msgstr ""

#: admin/class-marketking-core-admin.php:2154
msgid "Vendor Payouts"
msgstr ""

#: admin/class-marketking-core-admin.php:2157
msgid "Go Back"
msgstr ""

#: admin/class-marketking-core-admin.php:2163
msgid "Payouts for"
msgstr ""

#: admin/class-marketking-core-admin.php:2173
msgid "Current Vendor Payout Information"
msgstr ""

#: admin/class-marketking-core-admin.php:2177
msgid "Chosen payout method"
msgstr ""

#: admin/class-marketking-core-admin.php:2213
msgid "connected successfully"
msgstr ""

#: admin/class-marketking-core-admin.php:2215 public/dashboard/payouts.php:202
msgid "not connected yet"
msgstr ""

#: admin/class-marketking-core-admin.php:2221
#: admin/class-marketking-core-admin.php:3228
msgid "The vendor has not configured a payout method yet"
msgstr ""

#: admin/class-marketking-core-admin.php:2231
msgid "PayPal email address"
msgstr ""

#: admin/class-marketking-core-admin.php:2243
msgid "Details"
msgstr ""

#: admin/class-marketking-core-admin.php:2260
#: admin/class-marketking-core-admin.php:3249 public/dashboard/payouts.php:508
msgid "Full Name"
msgstr ""

#: admin/class-marketking-core-admin.php:2264
#: admin/class-marketking-core-admin.php:3253 public/dashboard/payouts.php:514
msgid "Billing Address Line 1"
msgstr ""

#: admin/class-marketking-core-admin.php:2268
#: admin/class-marketking-core-admin.php:3257 public/dashboard/payouts.php:520
msgid "Billing Address Line 2"
msgstr ""

#: admin/class-marketking-core-admin.php:2272
#: admin/class-marketking-core-admin.php:3261 public/dashboard/payouts.php:526
#: public/dashboard/profile.php:337
msgid "City"
msgstr ""

#: admin/class-marketking-core-admin.php:2280
#: admin/class-marketking-core-admin.php:3269 public/dashboard/payouts.php:538
msgid "Postcode"
msgstr ""

#: admin/class-marketking-core-admin.php:2284
#: admin/class-marketking-core-admin.php:3273 public/dashboard/payouts.php:544
msgid "Country"
msgstr ""

#: admin/class-marketking-core-admin.php:2288
#: admin/class-marketking-core-admin.php:3277
msgid "Bank Account Holder Name"
msgstr ""

#: admin/class-marketking-core-admin.php:2292
#: admin/class-marketking-core-admin.php:3281 public/dashboard/payouts.php:570
msgid "Bank Account Number/IBAN"
msgstr ""

#: admin/class-marketking-core-admin.php:2296
#: admin/class-marketking-core-admin.php:3285 public/dashboard/payouts.php:576
msgid "Bank Branch City"
msgstr ""

#: admin/class-marketking-core-admin.php:2300
#: admin/class-marketking-core-admin.php:3289 public/dashboard/payouts.php:582
msgid "Bank Branch Country"
msgstr ""

#: admin/class-marketking-core-admin.php:2304
#: admin/class-marketking-core-admin.php:3293 public/dashboard/payouts.php:588
msgid "Intermediary Bank - Bank Code"
msgstr ""

#: admin/class-marketking-core-admin.php:2308
#: admin/class-marketking-core-admin.php:3297 public/dashboard/payouts.php:594
msgid "Intermediary Bank - Name"
msgstr ""

#: admin/class-marketking-core-admin.php:2312
#: admin/class-marketking-core-admin.php:3301 public/dashboard/payouts.php:600
msgid "Intermediary Bank - City"
msgstr ""

#: admin/class-marketking-core-admin.php:2316
#: admin/class-marketking-core-admin.php:3305 public/dashboard/payouts.php:606
msgid "Intermediary Bank - Country"
msgstr ""

#: admin/class-marketking-core-admin.php:2320
#: admin/class-marketking-core-admin.php:3309 public/dashboard/payouts.php:558
msgid "Bank Name"
msgstr ""

#: admin/class-marketking-core-admin.php:2324
#: admin/class-marketking-core-admin.php:3313
msgid "Bank BIC / SWIFT"
msgstr ""

#: admin/class-marketking-core-admin.php:2352
msgid "Manage Payments"
msgstr ""

#: admin/class-marketking-core-admin.php:2361
msgid "Active Withdrawal Request: "
msgstr ""

#: admin/class-marketking-core-admin.php:2370
msgid "Payment Amount"
msgstr ""

#: admin/class-marketking-core-admin.php:2372
msgid "Enter the amount that has been sent..."
msgstr ""

#: admin/class-marketking-core-admin.php:2380
#: admin/class-marketking-core-admin.php:2444 public/dashboard/payouts.php:247
msgid "Payment Method"
msgstr ""

#: admin/class-marketking-core-admin.php:2382
msgid "Enter payment method used here..."
msgstr ""

#: admin/class-marketking-core-admin.php:2405
#: admin/class-marketking-core-admin.php:2521
msgid "Note / Details"
msgstr ""

#: admin/class-marketking-core-admin.php:2407
msgid "Enter note / details here..."
msgstr ""

#: admin/class-marketking-core-admin.php:2411
msgid "Bonus / Extra Payment (is not deducted from outstanding balance)"
msgstr ""

#: admin/class-marketking-core-admin.php:2415
msgid "Save Payment and Notify Vendor"
msgstr ""

#: admin/class-marketking-core-admin.php:2423
msgid "Payouts History"
msgstr ""

#: admin/class-marketking-core-admin.php:2427
#: admin/class-marketking-core-admin.php:2502
msgid "Current oustanding balance (unpaid earnings)"
msgstr ""

#: admin/class-marketking-core-admin.php:2442
#: includes/class-marketking-core.php:4997
#: includes/class-marketking-core.php:5060
#: public/dashboard/dashboard-content.php:316
#: public/dashboard/integrations/fooevents.php:194
#: public/dashboard/orders.php:76
msgid "Date"
msgstr ""

#: admin/class-marketking-core-admin.php:2443
#: includes/class-marketking-core.php:4997
#: includes/class-marketking-core.php:5060
#: public/dashboard/dashboard-content.php:317 public/dashboard/payouts.php:246
msgid "Amount"
msgstr ""

#: admin/class-marketking-core-admin.php:2445
msgid "Outstanding (Unpaid) Balance"
msgstr ""

#: admin/class-marketking-core-admin.php:2446
#: includes/class-marketking-core.php:4997
#: includes/class-marketking-core.php:5060
msgid "Note"
msgstr ""

#: admin/class-marketking-core-admin.php:2478 public/dashboard/payouts.php:279
msgid "(bonus)"
msgstr ""

#: admin/class-marketking-core-admin.php:2498
msgid "Vendor Balance History & Manual Adjustments"
msgstr ""

#: admin/class-marketking-core-admin.php:2515
msgid "Manual Adjustment Amount"
msgstr ""

#: admin/class-marketking-core-admin.php:2517
msgid ""
"Enter the adjustment amount (you can enter a positive / negative value to "
"increase / reduce balance)."
msgstr ""

#: admin/class-marketking-core-admin.php:2523
msgid "Enter note / explanation here..."
msgstr ""

#: admin/class-marketking-core-admin.php:2526
msgid "Save Adjustment"
msgstr ""

#: admin/class-marketking-core-admin.php:2528
msgid "Download Vendor Balance History"
msgstr ""

#: admin/class-marketking-core-admin.php:2550
msgid "Welcome to MarketKing Pro!"
msgstr ""

#: admin/class-marketking-core-admin.php:2551
msgid ""
"Please activate your license to get important plugin updates and premium "
"support."
msgstr ""

#: admin/class-marketking-core-admin.php:2552
#: admin/class-marketking-core-settings.php:2098
msgid "Activate License"
msgstr ""

#: admin/class-marketking-core-admin.php:2582
msgid "Groups"
msgstr ""

#: admin/class-marketking-core-admin.php:2590
msgid "Create, edit and manage vendor user groups & permissions."
msgstr ""

#: admin/class-marketking-core-admin.php:2599
msgid "Go to Vendor Groups"
msgstr ""

#: admin/class-marketking-core-admin.php:2606
msgid "Group Transfer Rules"
msgstr ""

#: admin/class-marketking-core-admin.php:2609
msgid ""
"Transfer vendors across groups automatically (e.g. group ranks or tiers)."
msgstr ""

#: admin/class-marketking-core-admin.php:2619
msgid "Go to Group Rules"
msgstr ""

#. translators: %s: Comments count.
#: admin/class-marketking-core-admin.php:2796
#, php-format
msgid "Reviews (%s)"
msgstr ""

#: admin/class-marketking-core-admin.php:2801
#: admin/class-marketking-core-admin.php:2821
#: public/dashboard/templates/sidebar.php:299
msgid "Reviews"
msgstr ""

#: admin/class-marketking-core-admin.php:2809
msgid "Filter reviews list"
msgstr ""

#: admin/class-marketking-core-admin.php:2810
msgid "Reviews list navigation"
msgstr ""

#: admin/class-marketking-core-admin.php:2811
msgid "Reviews list"
msgstr ""

#. translators: %s: Search query.
#: admin/class-marketking-core-admin.php:2832
#: includes/class-marketking-core.php:1913
#, php-format
msgid "Search results for: %s"
msgstr ""

#: admin/class-marketking-core-admin.php:2847
msgid "Invalid review ID."
msgstr ""

#: admin/class-marketking-core-admin.php:2850
msgid "Sorry, you are not allowed to edit reviews on this post."
msgstr ""

#. translators: %s: Number of comments.
#: admin/class-marketking-core-admin.php:2870
#, php-format
msgid "%s review approved."
msgid_plural "%s reviews approved."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Number of comments.
#: admin/class-marketking-core-admin.php:2876
#, php-format
msgid "%s review marked as spam."
msgid_plural "%s reviews marked as spam."
msgstr[0] ""
msgstr[1] ""

#: admin/class-marketking-core-admin.php:2876
#: admin/class-marketking-core-admin.php:2887
#: includes/class-marketking-core.php:1936
msgid "Undo"
msgstr ""

#. translators: %s: Number of comments.
#: admin/class-marketking-core-admin.php:2881
#, php-format
msgid "%s comment restored from the spam."
msgid_plural "%s comments restored from the spam."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Number of comments.
#: admin/class-marketking-core-admin.php:2887
#, php-format
msgid "%s comment moved to the Trash."
msgid_plural "%s comments moved to the Trash."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Number of comments.
#: admin/class-marketking-core-admin.php:2892
#, php-format
msgid "%s review restored from the Trash."
msgid_plural "%s reviews restored from the Trash."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Number of comments.
#: admin/class-marketking-core-admin.php:2897
#, php-format
msgid "%s review permanently deleted."
msgid_plural "%s reviews permanently deleted."
msgstr[0] ""
msgstr[1] ""

#: admin/class-marketking-core-admin.php:2905
msgid "This review is already approved."
msgstr ""

#: admin/class-marketking-core-admin.php:2905
#: admin/class-marketking-core-admin.php:2911
msgid "Edit review"
msgstr ""

#: admin/class-marketking-core-admin.php:2908
msgid "This review is already in the Trash."
msgstr ""

#: admin/class-marketking-core-admin.php:2908
msgid "View Trash"
msgstr ""

#: admin/class-marketking-core-admin.php:2911
msgid "This review is already marked as spam."
msgstr ""

#: admin/class-marketking-core-admin.php:2976
msgid "Add New"
msgstr ""

#: admin/class-marketking-core-admin.php:2985
#: admin/class-marketking-core-admin.php:3090
msgid "Contact Info"
msgstr ""

#: admin/class-marketking-core-admin.php:2990
#: admin/class-marketking-core-admin.php:3094
#: admin/class-marketking-core-admin.php:4178
#: admin/class-marketking-core-admin.php:4342
msgid "Total Sales Value"
msgstr ""

#: admin/class-marketking-core-admin.php:2995
#: admin/class-marketking-core-admin.php:3098
#: admin/class-marketking-core-admin.php:3134
#: admin/class-marketking-core-admin.php:3351 public/dashboard/orders.php:86
#: public/dashboard/products.php:171 public/templates/stores-list.php:99
#: public/templates/stores-list.php:266
msgid "Actions"
msgstr ""

#: admin/class-marketking-core-admin.php:3014
#: includes/class-marketking-core.php:3790
msgid "Inactive Vendor - No Group"
msgstr ""

#: admin/class-marketking-core-admin.php:3046
#: includes/class-marketking-core.php:3822
msgid "(on vacation)"
msgstr ""

#: admin/class-marketking-core-admin.php:3063
#: includes/class-marketking-core.php:3853 includes/elementor/classes.php:515
#: public/dashboard/marketking-dashboard-login.php:1003
#: public/dashboard/products.php:35 public/dashboard/templates/sidebar.php:132
#: public/templates/store-page.php:221
msgid "Products"
msgstr ""

#: admin/class-marketking-core-admin.php:3067
#: admin/class-marketking-core-admin.php:4235
#: admin/class-marketking-core-admin.php:4368
#: includes/class-marketking-core.php:3857
#: public/dashboard/dashboard-content.php:191 public/dashboard/orders.php:26
#: public/dashboard/templates/sidebar.php:163
msgid "Orders"
msgstr ""

#: admin/class-marketking-core-admin.php:3071
#: admin/class-marketking-core-admin.php:3115
#: admin/class-marketking-core-admin.php:5292
#: admin/class-marketking-core-admin.php:5293
#: admin/class-marketking-core-settings.php:1361
#: admin/class-marketking-core-settings.php:1781
#: includes/class-marketking-core.php:3861 public/dashboard/payouts.php:132
#: public/dashboard/templates/sidebar.php:286
msgid "Payouts"
msgstr ""

#: admin/class-marketking-core-admin.php:3072
#: includes/class-marketking-core.php:3862
msgid "Profile"
msgstr ""

#: admin/class-marketking-core-admin.php:3123
#: admin/class-marketking-core-admin.php:3340
msgid "Last Payment"
msgstr ""

#: admin/class-marketking-core-admin.php:3124
#: admin/class-marketking-core-admin.php:3341
msgid "Outstanding Balance"
msgstr ""

#: admin/class-marketking-core-admin.php:3129
#: admin/class-marketking-core-admin.php:3346
msgid "Withdrawal Requests"
msgstr ""

#: admin/class-marketking-core-admin.php:3138
#: admin/class-marketking-core-admin.php:3355
msgid "Payout Information"
msgstr ""

#: admin/class-marketking-core-admin.php:3181
msgid "No payment yet"
msgstr ""

#: admin/class-marketking-core-admin.php:3210
#: admin/class-marketking-core-admin.php:5410
#: admin/class-marketking-core-admin.php:5411
msgid "View Payouts"
msgstr ""

#: admin/class-marketking-core-admin.php:3231
#: public/emails/templates/new-payout-email-template.php:16
#: public/emails/templates/plain-new-payout-email-template.php:13
msgid "Method: "
msgstr ""

#: admin/class-marketking-core-admin.php:3236
msgid "Details: "
msgstr ""

#: admin/class-marketking-core-admin.php:3374
msgid "Pro Modules"
msgstr ""

#: admin/class-marketking-core-admin.php:3381
#: admin/class-marketking-core-admin.php:3414
msgid "(Unlock all with a Premium License! - 35% OFF TODAY)"
msgstr ""

#: admin/class-marketking-core-admin.php:3388
#: admin/class-marketking-core-admin.php:3421
msgid "Disable all"
msgstr ""

#: admin/class-marketking-core-admin.php:3389
#: admin/class-marketking-core-admin.php:3422
msgid "Enable all"
msgstr ""

#: admin/class-marketking-core-admin.php:3390
#: admin/class-marketking-core-admin.php:3423
#: public/dashboard/profile-settings.php:165
msgid "Save Settings"
msgstr ""

#: admin/class-marketking-core-admin.php:3411
msgid "Pro Plugin Integrations"
msgstr ""

#: admin/class-marketking-core-admin.php:3571
msgid "PRO"
msgstr ""

#: admin/class-marketking-core-admin.php:3573
msgid "OFF"
msgstr ""

#: admin/class-marketking-core-admin.php:3577
msgid "ON"
msgstr ""

#: admin/class-marketking-core-admin.php:3595
msgid "View Documentation"
msgstr ""

#: admin/class-marketking-core-admin.php:3601
msgid "Unlock now"
msgstr ""

#: admin/class-marketking-core-admin.php:3601
msgid " with a Premium License"
msgstr ""

#: admin/class-marketking-core-admin.php:3607
msgid "Compatible"
msgstr ""

#: admin/class-marketking-core-admin.php:3607
msgid " with your version of MarketKing"
msgstr ""

#: admin/class-marketking-core-admin.php:3648
msgid "Unlock 137+ Pro Features"
msgstr ""

#: admin/class-marketking-core-admin.php:3652
msgid "with a "
msgstr ""

#: admin/class-marketking-core-admin.php:3652
msgid "Premium License"
msgstr ""

#: admin/class-marketking-core-admin.php:3655
msgid ""
"Get full lifetime access to 25+ powerful modules, as well as hundreds of "
"features & integrations. Pay once, get lifetime updates."
msgstr ""

#: admin/class-marketking-core-admin.php:3658
msgid "UPGRADE NOW"
msgstr ""

#: admin/class-marketking-core-admin.php:3685
#: admin/class-marketking-core-settings.php:2138
msgid "Documentation"
msgstr ""

#: admin/class-marketking-core-admin.php:3686
#: public/dashboard/templates/profile-sidebar.php:99
msgid "Support"
msgstr ""

#: admin/class-marketking-core-admin.php:3701
msgid "Upgrade to Premium"
msgstr ""

#: admin/class-marketking-core-admin.php:3716
#: admin/class-marketking-core-admin.php:5423
#: admin/class-marketking-core-admin.php:5424
#: admin/class-marketking-core-settings.php:1343
msgid "Registration"
msgstr ""

#: admin/class-marketking-core-admin.php:3721
msgid "Fields"
msgstr ""

#: admin/class-marketking-core-admin.php:3724
msgid "Create & Edit Registration Fields. Choose from 9+ Custom Field Types."
msgstr ""

#: admin/class-marketking-core-admin.php:3732
msgid "Manage Registration Fields"
msgstr ""

#: admin/class-marketking-core-admin.php:3739
msgid "Options"
msgstr ""

#: admin/class-marketking-core-admin.php:3742
msgid "Manage Options (e.g. \"Customer\" / \"Vendor\")"
msgstr ""

#: admin/class-marketking-core-admin.php:3751
msgid "Manage Registration Options"
msgstr ""

#: admin/class-marketking-core-admin.php:4177
msgid "Sales Reports"
msgstr ""

#: admin/class-marketking-core-admin.php:4182
#: admin/class-marketking-core-admin.php:4289
#: admin/class-marketking-core-admin.php:4429
msgid "Commission"
msgstr ""

#: admin/class-marketking-core-admin.php:4183
#: admin/class-marketking-core-admin.php:4268
#: admin/class-marketking-core-admin.php:4387
msgid "Total Sales"
msgstr ""

#: admin/class-marketking-core-admin.php:4184
#: admin/class-marketking-core-admin.php:4279
msgid "Number of Orders"
msgstr ""

#: admin/class-marketking-core-admin.php:4190
msgid "All Vendors (Market)"
msgstr ""

#: admin/class-marketking-core-admin.php:4191
msgid "Vendor Stores"
msgstr ""

#: admin/class-marketking-core-admin.php:4192
msgid "Admin Store (Self)"
msgstr ""

#: admin/class-marketking-core-admin.php:4209
msgid "From:"
msgstr ""

#: admin/class-marketking-core-admin.php:4213
msgid "To:"
msgstr ""

#: admin/class-marketking-core-admin.php:4218
msgid "Quick Select:"
msgstr ""

#: admin/class-marketking-core-admin.php:4219
msgid "This Month"
msgstr ""

#: admin/class-marketking-core-admin.php:4220
msgid "Last Month"
msgstr ""

#: admin/class-marketking-core-admin.php:4221
msgid "This Year"
msgstr ""

#: admin/class-marketking-core-admin.php:4222
msgid "Last Year"
msgstr ""

#: admin/class-marketking-core-admin.php:4233
#: admin/class-marketking-core-admin.php:4346
#: admin/class-marketking-core-admin.php:4364
msgid "Sales"
msgstr ""

#: admin/class-marketking-core-admin.php:4259
#: admin/class-marketking-core-admin.php:4417
msgid "New Vendors"
msgstr ""

#: admin/class-marketking-core-admin.php:4341
msgid "Sales Summary"
msgstr ""

#: admin/class-marketking-core-admin.php:4351
#: public/dashboard/integrations/fooevents.php:21
#: public/dashboard/integrations/fooevents.php:84
msgid "Today"
msgstr ""

#: admin/class-marketking-core-admin.php:4352
msgid "Last 7 Days"
msgstr ""

#: admin/class-marketking-core-admin.php:4353
msgid "Last 31 Days"
msgstr ""

#: admin/class-marketking-core-admin.php:4405
msgid "Orders Nr."
msgstr ""

#: admin/class-marketking-core-admin.php:4455
msgid "Nothing here..."
msgstr ""

#: admin/class-marketking-core-admin.php:4455
msgid "No registrations need approval!"
msgstr ""

#: admin/class-marketking-core-admin.php:4459
msgid " Vendor Registrations - Approval Needed"
msgstr ""

#: admin/class-marketking-core-admin.php:4464
msgid "Name and Email"
msgstr ""

#: admin/class-marketking-core-admin.php:4465
msgid "Reg. Role"
msgstr ""

#: admin/class-marketking-core-admin.php:4466
msgid "Reg. Date"
msgstr ""

#: admin/class-marketking-core-admin.php:4467
msgid "Approval"
msgstr ""

#: admin/class-marketking-core-admin.php:4497
msgid "Review"
msgstr ""

#: admin/class-marketking-core-admin.php:4531
msgid "You Have"
msgstr ""

#: admin/class-marketking-core-admin.php:4533
msgid "Get Premium"
msgstr ""

#: admin/class-marketking-core-admin.php:4570
msgid " New Messages"
msgstr ""

#: admin/class-marketking-core-admin.php:4572
msgid "Messages (Premium)"
msgstr ""

#: admin/class-marketking-core-admin.php:4585
msgid "You have"
msgstr ""

#: admin/class-marketking-core-admin.php:4599
msgid " Products Pending Review"
msgstr ""

#: admin/class-marketking-core-admin.php:4625
msgid "messages"
msgstr ""

#: admin/class-marketking-core-admin.php:4720
#: admin/class-marketking-core-admin.php:5280
#: admin/class-marketking-core-admin.php:5281
#: public/dashboard/templates/sidebar.php:102
msgid "Messages"
msgstr ""

#: admin/class-marketking-core-admin.php:4726
#: admin/class-marketking-core-admin.php:4733
#: public/emails/class-marketking-new-message-email.php:20
#: public/emails/class-marketking-new-message-email.php:21
#: public/emails/class-marketking-new-message-email.php:49
#: public/emails/class-marketking-new-message-email.php:50
msgid "New message"
msgstr ""

#: admin/class-marketking-core-admin.php:4734
msgid "Close X"
msgstr ""

#: admin/class-marketking-core-admin.php:4739
#: admin/class-marketking-core-admin.php:4806 public/dashboard/products.php:156
msgid "Type"
msgstr ""

#: admin/class-marketking-core-admin.php:4744
#: includes/class-marketking-core.php:4697
#: public/class-marketking-core-public.php:3071
msgid "Inquiry"
msgstr ""

#: admin/class-marketking-core-admin.php:4745
#: admin/class-marketking-core-admin.php:4759
msgid "Message"
msgstr ""

#: admin/class-marketking-core-admin.php:4746
msgid "Quote Request"
msgstr ""

#: admin/class-marketking-core-admin.php:4755
#: admin/class-marketking-core-admin.php:4805
msgid "Title"
msgstr ""

#: admin/class-marketking-core-admin.php:4756
msgid "Enter the title here..."
msgstr ""

#: admin/class-marketking-core-admin.php:4760
msgid "Enter your message here..."
msgstr ""

#: admin/class-marketking-core-admin.php:4767
msgid "Start message"
msgstr ""

#: admin/class-marketking-core-admin.php:4807
msgid "User"
msgstr ""

#: admin/class-marketking-core-admin.php:4809
msgid "Last Reply"
msgstr ""

#: admin/class-marketking-core-admin.php:4816
msgid "inquiry"
msgstr ""

#: admin/class-marketking-core-admin.php:4819
msgid "message"
msgstr ""

#: admin/class-marketking-core-admin.php:4822
msgid "support"
msgstr ""

#: admin/class-marketking-core-admin.php:4825
msgid "quote"
msgstr ""

#: admin/class-marketking-core-admin.php:4839
msgid "View message"
msgstr ""

#: admin/class-marketking-core-admin.php:4849
msgid "No messages exist."
msgstr ""

#: admin/class-marketking-core-admin.php:4863
msgid "← Newer messages"
msgstr ""

#: admin/class-marketking-core-admin.php:4866
msgid "Older messages →"
msgstr ""

#: admin/class-marketking-core-admin.php:4910
msgid "←  Go Back"
msgstr ""

#: admin/class-marketking-core-admin.php:4915
msgid "Type:"
msgstr ""

#: admin/class-marketking-core-admin.php:4916
msgid "Date Started:"
msgstr ""

#: admin/class-marketking-core-admin.php:5012
#: includes/class-marketking-core-helper.php:1407
#: includes/class-marketking-core.php:4724
msgid "System Message"
msgstr ""

#: admin/class-marketking-core-admin.php:5033
msgid "Send Message"
msgstr ""

#: admin/class-marketking-core-admin.php:5038
msgid "message does not exist!"
msgstr ""

#: admin/class-marketking-core-admin.php:5107
msgctxt "Admin menu name"
msgid "Dashboard"
msgstr ""

#: admin/class-marketking-core-admin.php:5129
msgctxt "Admin menu name"
msgid "Payouts"
msgstr ""

#: admin/class-marketking-core-admin.php:5152
msgctxt "Admin menu name"
msgid "Refunds"
msgstr ""

#: admin/class-marketking-core-admin.php:5176
msgctxt "Admin menu name"
msgid "Verifications"
msgstr ""

#: admin/class-marketking-core-admin.php:5204
#: admin/class-marketking-core-admin.php:5205
msgid "MarketKing"
msgstr ""

#: admin/class-marketking-core-admin.php:5226
#: admin/class-marketking-core-admin.php:5227
#: public/dashboard/dashboard-content.php:38
#: public/dashboard/templates/sidebar.php:88
msgid "Dashboard"
msgstr ""

#: admin/class-marketking-core-admin.php:5241
#: admin/class-marketking-core-admin.php:5242
#: public/dashboard/templates/sidebar.php:53
#: public/dashboard/templates/sidebar.php:70
msgid "Announcements"
msgstr ""

#: admin/class-marketking-core-admin.php:5303
#: admin/class-marketking-core-admin.php:5304
msgid "Reports"
msgstr ""

#: admin/class-marketking-core-admin.php:5316
#: admin/class-marketking-core-admin.php:5317
#: admin/class-marketking-core-settings.php:1743
msgid "Commission Rules"
msgstr ""

#: admin/class-marketking-core-admin.php:5330
#: admin/class-marketking-core-admin.php:5331
msgid "Abuse Reports"
msgstr ""

#: admin/class-marketking-core-admin.php:5344
#: admin/class-marketking-core-admin.php:5345
#: admin/class-marketking-core-settings.php:1468
#: admin/class-marketking-core-settings.php:1910
#: admin/class-marketking-core-settings.php:1920
msgid "Memberships"
msgstr ""

#: admin/class-marketking-core-admin.php:5358
#: admin/class-marketking-core-admin.php:5359
msgid "Verifications"
msgstr ""

#: admin/class-marketking-core-admin.php:5372
#: admin/class-marketking-core-admin.php:5373
msgid "Badges"
msgstr ""

#: admin/class-marketking-core-admin.php:5386
#: admin/class-marketking-core-admin.php:5387
#: public/dashboard/templates/sidebar.php:311
msgid "Refunds"
msgstr ""

#: admin/class-marketking-core-admin.php:5399
#: admin/class-marketking-core-admin.php:5400
msgid "Modules"
msgstr ""

#: admin/class-marketking-core-admin.php:5438
#: admin/class-marketking-core-admin.php:5439
msgid "Seller Docs"
msgstr ""

#: admin/class-marketking-core-admin.php:5452
#: admin/class-marketking-core-admin.php:5453
msgid "Store Reviews"
msgstr ""

#: admin/class-marketking-core-admin.php:5480
#: admin/class-marketking-core-admin.php:5481
#: admin/class-marketking-core-admin.php:5555
#: public/dashboard/templates/sidebar.php:404
msgid "Settings"
msgstr ""

#: admin/class-marketking-core-admin.php:5490
msgid "Upgrade Now"
msgstr ""

#: admin/class-marketking-core-admin.php:5531
#: admin/class-marketking-core-settings.php:1503
msgid "License"
msgstr ""

#: admin/class-marketking-core-admin.php:5531
msgid "ACTIVE"
msgstr ""

#: admin/class-marketking-core-admin.php:5652
msgid "Are you sure you want to save this payment?"
msgstr ""

#: admin/class-marketking-core-admin.php:5653
msgid "Are you sure you want to add an ad for this product?"
msgstr ""

#: admin/class-marketking-core-admin.php:5654
msgid "Are you sure you want to remove all ads for this product?"
msgstr ""

#: admin/class-marketking-core-admin.php:5655
msgid "Are you sure you want to make this manual adjustment?"
msgstr ""

#: admin/class-marketking-core-admin.php:5656
msgid "Are you sure you want to update this user's data?"
msgstr ""

#: admin/class-marketking-core-admin.php:5657
msgid "The user's data has been updated."
msgstr ""

#: admin/class-marketking-core-admin.php:5658
msgid "Are you sure you want to approve this vendor account?"
msgstr ""

#: admin/class-marketking-core-admin.php:5659
msgid ""
"Are you sure you want to REJECT and DELETE this user? This is irreversible."
msgstr ""

#: admin/class-marketking-core-admin.php:5660
msgid ""
"Are you sure you want to DEACTIVATE this user? The user will no longer be "
"approved and they will be unable to login."
msgstr ""

#: admin/class-marketking-core-admin.php:5661
#: public/class-marketking-core-public.php:3061
msgid "This URL is available!"
msgstr ""

#: admin/class-marketking-core-admin.php:5662
msgid "This URL is unavailable!"
msgstr ""

#: admin/class-marketking-core-admin.php:5664
msgid "Username already in the list!"
msgstr ""

#: admin/class-marketking-core-admin.php:5665
msgid "Add user"
msgstr ""

#: admin/class-marketking-core-admin.php:5666
msgid "Message regarding product submission #"
msgstr ""

#: admin/class-marketking-core-admin.php:5668
msgid "Set up group rules (optional)"
msgstr ""

#: admin/class-marketking-core-admin.php:5669
msgid "Go back"
msgstr ""

#: admin/class-marketking-core-admin.php:5670
msgid "Configure Verification Items"
msgstr ""

#: admin/class-marketking-core-admin.php:5673
msgid "Enter a reason for the rejection - this will be shown to the vendor."
msgstr ""

#: admin/class-marketking-core-admin.php:5681
msgid "Processing activation request..."
msgstr ""

#: admin/class-marketking-core-admin.php:5684
#: public/class-marketking-core-public.php:2496
msgid "Are you sure you want to create this shipment?"
msgstr ""

#: admin/class-marketking-core-admin.php:5686
#: public/class-marketking-core-public.php:2485
msgid "Edit Columns"
msgstr ""

#: admin/class-marketking-core-admin.php:5756
msgid ""
"Warning: The plugin \"MarketKing\" requires WooCommerce to be installed and "
"activated."
msgstr ""

#: admin/class-marketking-core-settings.php:30
msgid "Vendor Registration Page"
msgstr ""

#: admin/class-marketking-core-settings.php:34
msgid "Existing Users Can Apply"
msgstr ""

#: admin/class-marketking-core-settings.php:34
msgid ""
"Existing users / customers will be able to apply to convert their account to "
"a vendor account."
msgstr ""

#: admin/class-marketking-core-settings.php:40
#: includes/class-marketking-core.php:1167
msgid "Vendor Stores Page"
msgstr ""

#: admin/class-marketking-core-settings.php:49
#: includes/class-marketking-core.php:1159
msgid "Vendor Dashboard Page"
msgstr ""

#: admin/class-marketking-core-settings.php:57
msgid "Store Page Style"
msgstr ""

#: admin/class-marketking-core-settings.php:62
msgid "Vendor Dashboard Logo"
msgstr ""

#: admin/class-marketking-core-settings.php:66
msgid "Vendor Dashboard Favicon"
msgstr ""

#: admin/class-marketking-core-settings.php:71
msgid "Change Color Scheme"
msgstr ""

#: admin/class-marketking-core-settings.php:83
msgid "Dashboard Color"
msgstr ""

#: admin/class-marketking-core-settings.php:95
msgid "Dashboard Color Hover"
msgstr ""

#: admin/class-marketking-core-settings.php:107
msgid "Pre-Built Color Schemes"
msgstr ""

#: admin/class-marketking-core-settings.php:121
msgid "Enable PayPal Payouts"
msgstr ""

#: admin/class-marketking-core-settings.php:128
msgid "Enable Stripe Payouts"
msgstr ""

#: admin/class-marketking-core-settings.php:134
msgid "Enable Bank Transfer Payouts"
msgstr ""

#: admin/class-marketking-core-settings.php:138
msgid "Enable Custom Payout Method"
msgstr ""

#: admin/class-marketking-core-settings.php:146
msgid "Min. Withdrawal Threshold"
msgstr ""

#: admin/class-marketking-core-settings.php:154
msgid "License email"
msgstr ""

#: admin/class-marketking-core-settings.php:157
msgid "License key"
msgstr ""

#: admin/class-marketking-core-settings.php:168
msgid "Dashboard Page Name"
msgstr ""

#: admin/class-marketking-core-settings.php:171
msgid "Dashboard Page Title"
msgstr ""

#: admin/class-marketking-core-settings.php:174
msgid "Dashboard Page Description"
msgstr ""

#: admin/class-marketking-core-settings.php:177
msgid "Default Group"
msgstr ""

#: admin/class-marketking-core-settings.php:180
msgid "Group Assignment"
msgstr ""

#: admin/class-marketking-core-settings.php:191
msgid "Cost Per 1 Credit"
msgstr ""

#: admin/class-marketking-core-settings.php:191
msgid ""
"How much it costs vendors to purchase 1 credit point (in real currency)."
msgstr ""

#: admin/class-marketking-core-settings.php:196
msgid "Credit Cost Per Day"
msgstr ""

#: admin/class-marketking-core-settings.php:196
msgid "How many credits are needed to advertise a product for 1 day"
msgstr ""

#: admin/class-marketking-core-settings.php:201
msgid "Mark Advertised Products as Featured"
msgstr ""

#: admin/class-marketking-core-settings.php:201
msgid ""
"Advertised productss will be marked as featured. Products will be "
"automatically removed from featured list after advertisement is expired."
msgstr ""

#: admin/class-marketking-core-settings.php:206
msgid "Display Advertised Products on Top"
msgstr ""

#: admin/class-marketking-core-settings.php:206
msgid ""
"Advertised products will be displayed on top of the catalog listing pages: "
"Shop page, Single Store Page etc."
msgstr ""

#: admin/class-marketking-core-settings.php:216
msgid "Commission Type"
msgstr ""

#: admin/class-marketking-core-settings.php:220
msgid "Commission Value"
msgstr ""

#: admin/class-marketking-core-settings.php:224
msgid "Shipping Fee Recipient"
msgstr ""

#: admin/class-marketking-core-settings.php:228
msgid "Tax Fee Recipient"
msgstr ""

#: admin/class-marketking-core-settings.php:232
msgid "COD Orders"
msgstr ""

#: admin/class-marketking-core-settings.php:238
msgid "Rules Set Vendor Commission"
msgstr ""

#: admin/class-marketking-core-settings.php:238
msgid ""
"By default, rules set the admin commission. Enabling this reverses it, and "
"rules set the vendor commission."
msgstr ""

#: admin/class-marketking-core-settings.php:248
msgid "Social Media Sites"
msgstr ""

#: admin/class-marketking-core-settings.php:248
msgid "Which social sites should be available to vendors?"
msgstr ""

#: admin/class-marketking-core-settings.php:254
msgid "Grayscale Icons"
msgstr ""

#: admin/class-marketking-core-settings.php:254
msgid "Turns frontend icons from color to black and white"
msgstr ""

#: admin/class-marketking-core-settings.php:263
msgid "Shipping Providers"
msgstr ""

#: admin/class-marketking-core-settings.php:263
msgid "Which providers should be available to vendors for shipping tracking?"
msgstr ""

#: admin/class-marketking-core-settings.php:268
msgid "Require shipment to complete order"
msgstr ""

#: admin/class-marketking-core-settings.php:268
msgid ""
"Vendors can mark order as completed only if there is an existing shipment"
msgstr ""

#: admin/class-marketking-core-settings.php:273
msgid "Customers mark orders as received"
msgstr ""

#: admin/class-marketking-core-settings.php:273
msgid ""
"Customers have a button to mark that they have received a particular order"
msgstr ""

#: admin/class-marketking-core-settings.php:281
msgid "Enable commission invoices"
msgstr ""

#: admin/class-marketking-core-settings.php:281
msgid "Commission invoices between sellers and marketplace are auto-generated."
msgstr ""

#: admin/class-marketking-core-settings.php:289
msgid "Vendor Priority"
msgstr ""

#: admin/class-marketking-core-settings.php:289
msgid ""
"When multiple offers are available, which vendor offers should be shown "
"first?"
msgstr ""

#: admin/class-marketking-core-settings.php:295
msgid "Stock Priority"
msgstr ""

#: admin/class-marketking-core-settings.php:295
msgid ""
"By setting this to \"in stock\", the plugin will deprioritize out of stock "
"offers and show them at the bottom of the offers list."
msgstr ""

#: admin/class-marketking-core-settings.php:301
msgid "Other Offers Position"
msgstr ""

#: admin/class-marketking-core-settings.php:301
msgid ""
"Control the location where the \"other offers\" tab is displayed on the "
"product page."
msgstr ""

#: admin/class-marketking-core-settings.php:307
msgid "Default Offers Number"
msgstr ""

#: admin/class-marketking-core-settings.php:307
msgid ""
"This controls how many offers are shown by default. All offers will become "
"visible after the user clicks on \"show more\"."
msgstr ""

#: admin/class-marketking-core-settings.php:317
msgid "Vendors Publish Products Directly"
msgstr ""

#: admin/class-marketking-core-settings.php:317
msgid ""
"Vendors can bypass the \"pending / review\" status, and directly publish "
"products in the shop. This is a global setting that applies to all vendors."
msgstr ""

#: admin/class-marketking-core-settings.php:322
msgid "Vendors Change Order Status"
msgstr ""

#: admin/class-marketking-core-settings.php:322
msgid ""
"Vendors can directly control and change the status of orders (processing, "
"completed, on-hold, etc). This is a global setting that applies to all "
"vendors."
msgstr ""

#: admin/class-marketking-core-settings.php:332
msgid "Add New Products"
msgstr ""

#: admin/class-marketking-core-settings.php:332
msgid ""
"With this disabled, only the admin can set products for the vendor. Vendors "
"can still add existing products through the Single Product Multiple Vendors "
"module."
msgstr ""

#: admin/class-marketking-core-settings.php:338
msgid "Edit Product Tags"
msgstr ""

#: admin/class-marketking-core-settings.php:338
msgid "Vendors can set/modify product tags for their own products."
msgstr ""

#: admin/class-marketking-core-settings.php:344
msgid "Add Linked Products"
msgstr ""

#: admin/class-marketking-core-settings.php:344
msgid ""
"Vendors can add upsell/cross-sell products to be promoted throughout the "
"shop via the \"Linked Products\" tab."
msgstr ""

#: admin/class-marketking-core-settings.php:350
msgid "Add Purchase Notes"
msgstr ""

#: admin/class-marketking-core-settings.php:350
msgid ""
"Vendors can set purchase notes for products, to be sent to customers after "
"purchase."
msgstr ""

#: admin/class-marketking-core-settings.php:356
msgid "Turn Reviews On/Off"
msgstr ""

#: admin/class-marketking-core-settings.php:356
msgid "Vendors can enable or disable reviews for their own products."
msgstr ""

#: admin/class-marketking-core-settings.php:371
msgid "Admin-Only Shipping Methods"
msgstr ""

#: admin/class-marketking-core-settings.php:371
msgid "If you are selling as the admin, which shipping methods are admin-only?"
msgstr ""

#: admin/class-marketking-core-settings.php:384
msgid "Refund Request Time Limit"
msgstr ""

#: admin/class-marketking-core-settings.php:401
msgid "Enable Product Page Inquiries"
msgstr ""

#: admin/class-marketking-core-settings.php:405
msgid "Enable Vendor Page Inquiries"
msgstr ""

#: admin/class-marketking-core-settings.php:409
msgid "Inquiries Use Messaging System"
msgstr ""

#: admin/class-marketking-core-settings.php:409
msgid ""
"When this is enabled, inquiries use the messaging module (must be enabled in "
"MarketKing -> Modules). Otherwise, they use email. Recommended."
msgstr ""

#: admin/class-marketking-core-settings.php:423
msgid "Allow Support Through Messaging Module"
msgstr ""

#: admin/class-marketking-core-settings.php:427
msgid "Allow Support Through External URL"
msgstr ""

#: admin/class-marketking-core-settings.php:431
msgid "Allow Support Through Dedicated Support Email"
msgstr ""

#: admin/class-marketking-core-settings.php:435
msgid "Show Support Option On Order Details Page"
msgstr ""

#: admin/class-marketking-core-settings.php:439
msgid "Show Support Option On Single Product Pages"
msgstr ""

#: admin/class-marketking-core-settings.php:450
msgid "Cart Multiple Vendors Text"
msgstr ""

#: admin/class-marketking-core-settings.php:465
msgid "Royal Gold"
msgstr ""

#: admin/class-marketking-core-settings.php:466
msgid "Deep Indigo"
msgstr ""

#: admin/class-marketking-core-settings.php:467
msgid "Bold Jade"
msgstr ""

#: admin/class-marketking-core-settings.php:482
msgid ""
"Number of days after placing an order, during which customers can request a "
"refund."
msgstr ""

#: admin/class-marketking-core-settings.php:493
msgid "Minimum balance required to make a withdrawal."
msgstr ""

#: admin/class-marketking-core-settings.php:504
msgid ""
"Panel name on the vendor dashboard (e.g. \"Member\", \"Upgrade!\", "
"\"Subscription\", etc.)"
msgstr ""

#: admin/class-marketking-core-settings.php:505
#: public/dashboard/templates/sidebar.php:371
msgid "Member"
msgstr ""

#: admin/class-marketking-core-settings.php:514
msgid "Title displayed on the vendor dashboard page."
msgstr ""

#: admin/class-marketking-core-settings.php:515
msgid "Available Options"
msgstr ""

#: admin/class-marketking-core-settings.php:524
msgid "Description displayed on the vendor dashboard page."
msgstr ""

#: admin/class-marketking-core-settings.php:525
msgid "Choose your desired option and start enjoying our service."
msgstr ""

#: admin/class-marketking-core-settings.php:535
msgid ""
"Message shown when multiple vendors's products are in cart. Leave empty to "
"hide it."
msgstr ""

#: admin/class-marketking-core-settings.php:536
#: public/class-marketking-core-public.php:485
#: public/class-marketking-core-public.php:486
msgid ""
"The products in your cart are sold by multiple different vendor partners. "
"The order will be placed simultaneously with all vendors and you will "
"receive a package from each of them."
msgstr ""

#: admin/class-marketking-core-settings.php:551
msgid "Enter method title here..."
msgstr ""

#: admin/class-marketking-core-settings.php:552
msgid "Enter method description / instructions here..."
msgstr ""

#: admin/class-marketking-core-settings.php:689
#: admin/class-marketking-core-settings.php:710
msgid "(messaging module is disabled)"
msgstr ""

#: admin/class-marketking-core-settings.php:806
msgid "What happens with commisssions from Cash on Delivery (COD) orders."
msgstr ""

#: admin/class-marketking-core-settings.php:808
msgid "- None - (COD orders are treated like all other orders)"
msgstr ""

#: admin/class-marketking-core-settings.php:809
msgid "Ignored (COD orders have no effect on commissions or balances)"
msgstr ""

#: admin/class-marketking-core-settings.php:810
msgid "Reversed (Admin commissions are deducted from vendor balance)"
msgstr ""

#: admin/class-marketking-core-settings.php:894
msgid "Light Simple"
msgstr ""

#: admin/class-marketking-core-settings.php:901
msgid "Light Dashed"
msgstr ""

#: admin/class-marketking-core-settings.php:909
msgid "Dark Mode"
msgstr ""

#: admin/class-marketking-core-settings.php:922
msgid "Elementor - Choose An Elementor Page"
msgstr ""

#: admin/class-marketking-core-settings.php:947
msgid "Your Custom Logo"
msgstr ""

#: admin/class-marketking-core-settings.php:948
#: admin/class-marketking-core-settings.php:957
msgid "Select Image"
msgstr ""

#: admin/class-marketking-core-settings.php:956
msgid "Your Vendor Dashboard Favicon"
msgstr ""

#: admin/class-marketking-core-settings.php:1105
msgid "Which offers should be shown first?"
msgstr ""

#: admin/class-marketking-core-settings.php:1107
msgid "Lowest Prices First"
msgstr ""

#: admin/class-marketking-core-settings.php:1108
msgid "Highest Prices First"
msgstr ""

#: admin/class-marketking-core-settings.php:1109
msgid "Top Rated Vendors First"
msgstr ""

#: admin/class-marketking-core-settings.php:1121
msgid ""
"What group should vendors be assigned to, in case payment fails, "
"subscription is cancelled, etc?"
msgstr ""

#: admin/class-marketking-core-settings.php:1130
msgid "Deactivate vendor (disables selling)"
msgstr ""

#: admin/class-marketking-core-settings.php:1148
msgid ""
"When should vendors be assigned to the new group? (after purchasing a "
"membership option)"
msgstr ""

#: admin/class-marketking-core-settings.php:1150
msgid "When order is processed / placed"
msgstr ""

#: admin/class-marketking-core-settings.php:1151
msgid "When order status is marked as completed"
msgstr ""

#: admin/class-marketking-core-settings.php:1163
msgid "Should in stock offers have priority?"
msgstr ""

#: admin/class-marketking-core-settings.php:1165
msgid "In Stock Products First"
msgstr ""

#: admin/class-marketking-core-settings.php:1166
msgid "None"
msgstr ""

#: admin/class-marketking-core-settings.php:1178
msgid "Position of the \"other offers\" tab"
msgstr ""

#: admin/class-marketking-core-settings.php:1180
msgid "Immediately Below Product"
msgstr ""

#: admin/class-marketking-core-settings.php:1181
msgid "Inside Product Tabs"
msgstr ""

#: admin/class-marketking-core-settings.php:1182
msgid "Below Product Tabs"
msgstr ""

#: admin/class-marketking-core-settings.php:1216
msgid "Select a commission type for vendor sales"
msgstr ""

#: admin/class-marketking-core-settings.php:1218
msgid "Percentage"
msgstr ""

#: admin/class-marketking-core-settings.php:1219
msgid "Flat"
msgstr ""

#: admin/class-marketking-core-settings.php:1232
msgid "Who receives the Shipping fees"
msgstr ""

#: admin/class-marketking-core-settings.php:1235
msgid "Admin + Vendor (Shipping included in commission calculation)"
msgstr ""

#: admin/class-marketking-core-settings.php:1236
#: admin/class-marketking-core-settings.php:1252
msgid "Admin"
msgstr ""

#: admin/class-marketking-core-settings.php:1248
msgid "Who receives the Tax fees"
msgstr ""

#: admin/class-marketking-core-settings.php:1251
msgid "Admin + Vendor (Tax included in commission calculation)"
msgstr ""

#: admin/class-marketking-core-settings.php:1264
msgid "Admin commission received for each sale"
msgstr ""

#: admin/class-marketking-core-settings.php:1265
msgid "Admin commission value"
msgstr ""

#: admin/class-marketking-core-settings.php:1347
#: includes/class-marketking-core-activator.php:77
msgid "Vendor Dashboard"
msgstr ""

#: admin/class-marketking-core-settings.php:1351
msgid "Vendor Capabilities"
msgstr ""

#: admin/class-marketking-core-settings.php:1356
#: admin/class-marketking-core-settings.php:1725
msgid "Commissions"
msgstr ""

#: admin/class-marketking-core-settings.php:1365
#: admin/class-marketking-core-settings.php:1801
msgid "Cart"
msgstr ""

#: admin/class-marketking-core-settings.php:1369
#: admin/class-marketking-core-settings.php:1833
msgid "Language and Text"
msgstr ""

#: admin/class-marketking-core-settings.php:1373
msgid "Appearance"
msgstr ""

#: admin/class-marketking-core-settings.php:1383
msgid "Product & Vendor Inquiries"
msgstr ""

#: admin/class-marketking-core-settings.php:1396
msgid "Store Support"
msgstr ""

#: admin/class-marketking-core-settings.php:1408
msgid "Color Scheme"
msgstr ""

#: admin/class-marketking-core-settings.php:1420
msgid "Multiple Product Vendors"
msgstr ""

#: admin/class-marketking-core-settings.php:1444
#: admin/class-marketking-core-settings.php:1875
msgid "Refund Requests"
msgstr ""

#: admin/class-marketking-core-settings.php:1456
#: public/dashboard/templates/profile-sidebar.php:89
msgid "Invoicing"
msgstr ""

#: admin/class-marketking-core-settings.php:1480
#: admin/class-marketking-core-settings.php:2033
msgid "Social Sharing"
msgstr ""

#: admin/class-marketking-core-settings.php:1492
#: admin/class-marketking-core-settings.php:1892
msgid "Advertising"
msgstr ""

#: admin/class-marketking-core-settings.php:1525
msgid "Set up vendor registration."
msgstr ""

#: admin/class-marketking-core-settings.php:1535
#: public/dashboard/marketking-dashboard-login.php:1241
msgid "Disabled"
msgstr ""

#: admin/class-marketking-core-settings.php:1541
msgid "My Account Page"
msgstr ""

#: admin/class-marketking-core-settings.php:1547
msgid "Separate Registration Page"
msgstr ""

#: admin/class-marketking-core-settings.php:1556
msgid ""
"My account registration is not enabled in WooCommerce. You must go to "
"WooCommerce -> Settings -> Accounts & Privacy -> and enable \"Allow "
"customers to create an account on the \"My account\" page\""
msgstr ""

#: admin/class-marketking-core-settings.php:1570
#: admin/class-marketking-core-settings.php:1579
msgid "Manage Registration Form & Fields"
msgstr ""

#: admin/class-marketking-core-settings.php:1572
msgid ""
"Upgrade to MarketKing Premium to customize the registration form. Choose "
"from 9 types of custom fields: text, dropdown, phone nr, file upload, etc."
msgstr ""

#: admin/class-marketking-core-settings.php:1599
msgid "Set up the vendor dashboard."
msgstr ""

#: admin/class-marketking-core-settings.php:1616
msgid "Set up store appearance."
msgstr ""

#: admin/class-marketking-core-settings.php:1632
msgid "Support Settings"
msgstr ""

#: admin/class-marketking-core-settings.php:1650
msgid "Main Capabilities"
msgstr ""

#: admin/class-marketking-core-settings.php:1662
msgid "Product Management"
msgstr ""

#: admin/class-marketking-core-settings.php:1671
msgid "Shipping Management"
msgstr ""

#: admin/class-marketking-core-settings.php:1687
msgid "Store Management"
msgstr ""

#: admin/class-marketking-core-settings.php:1697
msgid "Single Category"
msgstr ""

#: admin/class-marketking-core-settings.php:1703
msgid "Multiple Categories"
msgstr ""

#: admin/class-marketking-core-settings.php:1727
msgid "Set up commission settings"
msgstr ""

#: admin/class-marketking-core-settings.php:1740
msgid "The complex commissions module is active. Please go to "
msgstr ""

#: admin/class-marketking-core-settings.php:1746
msgid "to configure commissions."
msgstr ""

#: admin/class-marketking-core-settings.php:1764
msgid "Documentation & Complex Commissions"
msgstr ""

#: admin/class-marketking-core-settings.php:1767
msgid ""
"Learn how to set up simple or combined commissions by category, product or "
"vendor with the \"complex commissions\" module."
msgstr ""

#: admin/class-marketking-core-settings.php:1783
msgid "Control available payout options"
msgstr ""

#: admin/class-marketking-core-settings.php:1803
msgid "Control cart options"
msgstr ""

#: admin/class-marketking-core-settings.php:1809
msgid "Cart Display Template"
msgstr ""

#: admin/class-marketking-core-settings.php:1813
msgid "MarketKing Cart (Recommended)"
msgstr ""

#: admin/class-marketking-core-settings.php:1819
msgid "Classic Cart"
msgstr ""

#: admin/class-marketking-core-settings.php:1835
msgid "Easily change plugin text. "
msgstr ""

#: admin/class-marketking-core-settings.php:1836
msgid "To fully translate the plugin, click "
msgstr ""

#: admin/class-marketking-core-settings.php:1837
msgid "here"
msgstr ""

#: admin/class-marketking-core-settings.php:1855
msgid "Product and Vendor Inquiries"
msgstr ""

#: admin/class-marketking-core-settings.php:1857
msgid "Allow vendors and customers to communicate via email or messaging. "
msgstr ""

#: admin/class-marketking-core-settings.php:1917
msgid ""
"To configure the membership packages / options available to your vendors, "
"please go to the"
msgstr ""

#: admin/class-marketking-core-settings.php:1923
msgid "section."
msgstr ""

#: admin/class-marketking-core-settings.php:1943
msgid "Color Scheme Customization"
msgstr ""

#: admin/class-marketking-core-settings.php:1968
msgid "Single Product Multiple Vendors"
msgstr ""

#: admin/class-marketking-core-settings.php:1990
msgid "Invoices"
msgstr ""

#: admin/class-marketking-core-settings.php:2054
msgid "License management"
msgstr ""

#: admin/class-marketking-core-settings.php:2056
msgid "Activate the plugin"
msgstr ""

#: admin/class-marketking-core-settings.php:2089
msgid "Your license is valid and active"
msgstr ""

#: admin/class-marketking-core-settings.php:2091
msgid "The plugin is registered to "
msgstr ""

#: admin/class-marketking-core-settings.php:2104
msgid ""
"Your license is not active. Activate now to receive vital plugin updates and "
"features!"
msgstr ""

#: admin/class-marketking-core-settings.php:2107
msgid "Click to learn more about"
msgstr ""

#: admin/class-marketking-core-settings.php:2107
msgid "how to activate the plugin license"
msgstr ""

#: admin/class-marketking-core-settings.php:2107
msgid "contact support"
msgstr ""

#: admin/class-marketking-core-settings.php:2141
msgid "Licensing and Activation FAQ & Guide"
msgstr ""

#: admin/class-marketking-core-settings.php:2142
msgid "How to activate if you purchased on Envato Market"
msgstr ""

#: admin/class-marketking-core-settings.php:2143
msgid "Purchase a new license"
msgstr ""

#: admin/class-marketking-reviews-page.php:16
msgctxt "column name"
msgid "Review"
msgstr ""

#: admin/class-marketking-reviews-page.php:17
msgctxt "column name"
msgid "Rating"
msgstr ""

#: admin/class-marketking-reviews-page.php:19
msgctxt "column name"
msgid "Vendor"
msgstr ""

#. translators: Column name or table row header.
#: admin/class-marketking-reviews-page.php:23 public/templates/cart.php:94
#: public/templates/cart.php:150 public/templates/email-order-details.php:96
#: public/templates/invoices/commission-invoice.php:126
#: public/templates/order-details.php:101
msgid "Product"
msgstr ""

#: admin/class-marketking-reviews-page.php:26
msgctxt "column name"
msgid "Date"
msgstr ""

#: includes/class-marketking-core-activator.php:51
#: public/class-marketking-core-public.php:2745
#: public/dashboard/marketking-dashboard-login.php:2457
msgid "Become a Vendor"
msgstr ""

#: includes/class-marketking-core-activator.php:64
msgid "Stores List"
msgstr ""

#: includes/class-marketking-core-activator.php:89
msgid "Proof of Identity"
msgstr ""

#: includes/class-marketking-core-activator.php:100
msgid "Proof of Address"
msgstr ""

#: includes/class-marketking-core-activator.php:112
msgid "Starter"
msgstr ""

#: includes/class-marketking-core-activator.php:127
msgid "Professional"
msgstr ""

#: includes/class-marketking-core-activator.php:145
msgid "Business"
msgstr ""

#: includes/class-marketking-core-activator.php:211
#: includes/class-marketking-core-activator.php:227
#: public/dashboard/profile.php:63 public/dashboard/profile.php:184
msgid "First Name"
msgstr ""

#: includes/class-marketking-core-activator.php:228
msgid "Enter your first name here..."
msgstr ""

#: includes/class-marketking-core-activator.php:240
#: includes/class-marketking-core-activator.php:256
#: public/dashboard/profile.php:70 public/dashboard/profile.php:190
msgid "Last Name"
msgstr ""

#: includes/class-marketking-core-activator.php:257
msgid "Enter your last name here..."
msgstr ""

#: includes/class-marketking-core-activator.php:270
#: includes/class-marketking-core-activator.php:286
#: public/dashboard/profile.php:77 public/dashboard/profile.php:196
msgid "Company Name"
msgstr ""

#: includes/class-marketking-core-activator.php:287
msgid "Enter your company name here..."
msgstr ""

#: includes/class-marketking-core-activator.php:317
msgid "Enter your phone here..."
msgstr ""

#: includes/class-marketking-core-activator.php:346
msgid "Enter your desired store name here..."
msgstr ""

#: includes/class-marketking-core-activator.php:376
msgid "Enter your desired store URL here..."
msgstr ""

#: includes/class-marketking-core-helper.php:207
#: includes/elementor/classes.php:853
#: public/class-marketking-core-public.php:689
msgid "Vendor Information"
msgstr ""

#: includes/class-marketking-core-helper.php:213
#: includes/elementor/classes.php:859
#: public/class-marketking-core-public.php:700
#: public/class-marketking-core-public.php:946
#: public/emails/templates/new-product-requires-approval-email-template.php:23
#: public/emails/templates/plain-new-product-requires-approval-email-template.php:16
msgid "Vendor: "
msgstr ""

#: includes/class-marketking-core-helper.php:242
#: includes/class-marketking-core.php:5687 includes/elementor/classes.php:797
#: includes/elementor/classes.php:887
#: public/class-marketking-core-public.php:720
#: public/templates/store-page.php:363 public/templates/stores-list.php:144
msgid "review"
msgstr ""

#: includes/class-marketking-core-helper.php:244
#: includes/class-marketking-core.php:5689 includes/elementor/classes.php:799
#: includes/elementor/classes.php:889
#: public/class-marketking-core-public.php:722
#: public/templates/store-page.php:365 public/templates/stores-list.php:146
msgid "reviews"
msgstr ""

#: includes/class-marketking-core-helper.php:246
#: includes/class-marketking-core.php:5691 includes/elementor/classes.php:801
#: includes/elementor/classes.php:891
#: public/class-marketking-core-public.php:724
#: public/templates/store-page.php:367 public/templates/stores-list.php:148
msgid "Rating:"
msgstr ""

#: includes/class-marketking-core-helper.php:246
#: includes/class-marketking-core.php:5691 includes/elementor/classes.php:801
#: includes/elementor/classes.php:891
#: public/class-marketking-core-public.php:724
#: public/templates/store-page.php:367
msgid "rating from"
msgstr ""

#: includes/class-marketking-core-helper.php:257
#: includes/elementor/classes.php:902
#: public/class-marketking-core-public.php:730
msgid "Company:"
msgstr ""

#: includes/class-marketking-core-helper.php:274
#: includes/elementor/classes.php:918
msgid "Address:"
msgstr ""

#: includes/class-marketking-core-helper.php:302
#: includes/elementor/classes.php:946
msgid "Store Category"
msgstr ""

#: includes/class-marketking-core-helper.php:323
#: includes/elementor/classes.php:966
#: public/class-marketking-core-public.php:747
#: public/dashboard/manage-order.php:270
msgid "Phone:"
msgstr ""

#: includes/class-marketking-core-helper.php:328
#: includes/elementor/classes.php:972
#: public/class-marketking-core-public.php:750
#: public/dashboard/manage-order.php:265
msgid "Email:"
msgstr ""

#: includes/class-marketking-core-helper.php:3176
#: includes/class-marketking-core-helper.php:3182
msgid "There is no store here..."
msgstr ""

#: includes/class-marketking-core.php:380
msgid "Simple product"
msgstr ""

#: includes/class-marketking-core.php:585
msgid "Commission Invoice"
msgstr ""

#: includes/class-marketking-core.php:831
msgid "The following vendors have not added their bank details:"
msgstr ""

#: includes/class-marketking-core.php:956
msgid ""
"Removed shipping methods that did not match order item vendors and "
"recalculated totals."
msgstr ""

#: includes/class-marketking-core.php:1031
msgid "This product has been temporarily deactivated and cannot be purchased."
msgstr ""

#: includes/class-marketking-core.php:1112
#: includes/class-marketking-core.php:1126
msgid "Vendor ID"
msgstr ""

#: includes/class-marketking-core.php:1177
msgid "Vendor Store Page Template (Elementor)"
msgstr ""

#: includes/class-marketking-core.php:1227
msgid "The resource has already been linked to this product"
msgstr ""

#: includes/class-marketking-core.php:1243
msgid "Unable to add resource"
msgstr ""

#: includes/class-marketking-core.php:1345
#: includes/class-marketking-core.php:1366
#: public/dashboard/marketking-dashboard-login.php:994
#: public/dashboard/templates/sidebar.php:193
msgid "Bookings"
msgstr ""

#: includes/class-marketking-core.php:1345
msgid "Start date cannot be greater than end date."
msgstr ""

#: includes/class-marketking-core.php:1366
msgid "Date should be of the format yyyy-mm-dd and cannot be empty."
msgstr ""

#: includes/class-marketking-core.php:1448
msgid "Error: Could not create item"
msgstr ""

#: includes/class-marketking-core.php:1576
msgid "MarketKing Multivendor Marketplace"
msgstr ""

#: includes/class-marketking-core.php:1762
#: includes/class-marketking-core.php:1765
msgid "Unable to disconnect your account, please try again"
msgstr ""

#: includes/class-marketking-core.php:1787
msgid "Invalid post type."
msgstr ""

#: includes/class-marketking-core.php:1792
msgid "You need a higher level of permission."
msgstr ""

#: includes/class-marketking-core.php:1793
msgid "Sorry, you are not allowed to edit posts in this post type."
msgstr ""

#: includes/class-marketking-core.php:1866
#, php-format
msgid "%s post updated."
msgid_plural "%s posts updated."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1867
msgid "1 post not updated, somebody is editing it."
msgstr ""

#: includes/class-marketking-core.php:1869
#, php-format
msgid "%s post not updated, somebody is editing it."
msgid_plural "%s posts not updated, somebody is editing them."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1871
#, php-format
msgid "%s post permanently deleted."
msgid_plural "%s posts permanently deleted."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1872
#, php-format
msgid "%s post moved to the Trash."
msgid_plural "%s posts moved to the Trash."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1873
#, php-format
msgid "%s post restored from the Trash."
msgid_plural "%s posts restored from the Trash."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1876
#, php-format
msgid "%s page updated."
msgid_plural "%s pages updated."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1877
msgid "1 page not updated, somebody is editing it."
msgstr ""

#: includes/class-marketking-core.php:1878
#, php-format
msgid "%s page not updated, somebody is editing it."
msgid_plural "%s pages not updated, somebody is editing them."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1879
#, php-format
msgid "%s page permanently deleted."
msgid_plural "%s pages permanently deleted."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1880
#, php-format
msgid "%s page moved to the Trash."
msgid_plural "%s pages moved to the Trash."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1881
#, php-format
msgid "%s page restored from the Trash."
msgid_plural "%s pages restored from the Trash."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1884
#, php-format
msgid "%s block updated."
msgid_plural "%s blocks updated."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1885
msgid "1 block not updated, somebody is editing it."
msgstr ""

#: includes/class-marketking-core.php:1886
#, php-format
msgid "%s block not updated, somebody is editing it."
msgid_plural "%s blocks not updated, somebody is editing them."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1887
#, php-format
msgid "%s block permanently deleted."
msgid_plural "%s blocks permanently deleted."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1888
#, php-format
msgid "%s block moved to the Trash."
msgid_plural "%s blocks moved to the Trash."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:1889
#, php-format
msgid "%s block restored from the Trash."
msgid_plural "%s blocks restored from the Trash."
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:2016
#: includes/class-marketking-core.php:2050
msgid "Parent order completed"
msgstr ""

#: includes/class-marketking-core.php:2035
msgid "Credit order completed"
msgstr ""

#: includes/class-marketking-core.php:2091
#: includes/class-marketking-core.php:2503
#: includes/class-marketking-order-splitter.php:437
#: includes/class-marketking-order-splitter.php:438
msgid "Payment"
msgstr ""

#: includes/class-marketking-core.php:2175
#: includes/class-marketking-core.php:2176 public/dashboard/orders.php:82
#: public/dashboard/templates/sidebar.php:273
msgid "Earnings"
msgstr ""

#: includes/class-marketking-core.php:2177
msgid "Create new earning"
msgstr ""

#: includes/class-marketking-core.php:2178
msgid "Create new customer earning"
msgstr ""

#: includes/class-marketking-core.php:2179
#: public/dashboard/marketking-dashboard-login.php:198
#: public/dashboard/marketking-dashboard-login.php:378
#: public/dashboard/products.php:492
msgid "Edit"
msgstr ""

#: includes/class-marketking-core.php:2180
msgid "Edit earning"
msgstr ""

#: includes/class-marketking-core.php:2181
msgid "New earning"
msgstr ""

#: includes/class-marketking-core.php:2182
msgid "View earning"
msgstr ""

#: includes/class-marketking-core.php:2183
#: public/dashboard/dashboard-content.php:71
msgid "View earnings"
msgstr ""

#: includes/class-marketking-core.php:2184
msgid "Search earnings"
msgstr ""

#: includes/class-marketking-core.php:2185
msgid "No earnings found"
msgstr ""

#: includes/class-marketking-core.php:2186
msgid "No earnings found in trash"
msgstr ""

#: includes/class-marketking-core.php:2187
msgid "Parent earning"
msgstr ""

#: includes/class-marketking-core.php:2188
msgid "Earning image"
msgstr ""

#: includes/class-marketking-core.php:2189
msgid "Set earning image"
msgstr ""

#: includes/class-marketking-core.php:2190
msgid "Remove earning image"
msgstr ""

#: includes/class-marketking-core.php:2191
msgid "Use as earning image"
msgstr ""

#: includes/class-marketking-core.php:2192
msgid "Insert into earning"
msgstr ""

#: includes/class-marketking-core.php:2193
msgid "Uploaded to this earning"
msgstr ""

#: includes/class-marketking-core.php:2194
msgid "Filter earnings"
msgstr ""

#: includes/class-marketking-core.php:2195
msgid "Earnings navigation"
msgstr ""

#: includes/class-marketking-core.php:2196
msgid "Earnings list"
msgstr ""

#: includes/class-marketking-core.php:2200
msgid "Agent earnings"
msgstr ""

#: includes/class-marketking-core.php:2309
#, php-format
msgid "Composite order (%s)"
msgid_plural "Composite order (%s)"
msgstr[0] ""
msgstr[1] ""

#: includes/class-marketking-core.php:3576 public/dashboard/products.php:398
msgid "In stock"
msgstr ""

#: includes/class-marketking-core.php:3579 public/dashboard/products.php:401
msgid "Out of stock"
msgstr ""

#: includes/class-marketking-core.php:3582 public/dashboard/products.php:404
msgid "On backorder"
msgstr ""

#: includes/class-marketking-core.php:3607 public/dashboard/products.php:57
#: public/dashboard/products.php:430
msgid "Published"
msgstr ""

#: includes/class-marketking-core.php:3610 public/dashboard/products.php:58
#: public/dashboard/products.php:433
msgid "Draft"
msgstr ""

#: includes/class-marketking-core.php:3613
#: public/class-marketking-core-public.php:2489
#: public/dashboard/dashboard-content.php:166 public/dashboard/products.php:59
#: public/dashboard/products.php:436
msgid "Pending"
msgstr ""

#: includes/class-marketking-core.php:3642
#: public/dashboard/edit-product.php:140 public/dashboard/products.php:519
msgid "Edit Product"
msgstr ""

#: includes/class-marketking-core.php:3647
#: public/dashboard/edit-product.php:195 public/dashboard/edit-product.php:231
#: public/dashboard/products.php:529
msgid "View Product"
msgstr ""

#: includes/class-marketking-core.php:3655
#: public/dashboard/edit-product.php:204 public/dashboard/products.php:543
msgid "Clone Product"
msgstr ""

#: includes/class-marketking-core.php:3662
#: public/dashboard/edit-product.php:211 public/dashboard/products.php:555
msgid "Delete Product"
msgstr ""

#: includes/class-marketking-core.php:3960 public/dashboard/orders.php:160
msgid "susbcription renewal"
msgstr ""

#: includes/class-marketking-core.php:3991
#: public/dashboard/manage-order.php:154 public/dashboard/orders.php:47
#: public/dashboard/orders.php:200
msgid "Processing"
msgstr ""

#: includes/class-marketking-core.php:3994 public/dashboard/orders.php:48
#: public/dashboard/orders.php:203
msgid "On Hold"
msgstr ""

#: includes/class-marketking-core.php:3997
#: public/class-marketking-core-public.php:2488
#: public/dashboard/dashboard-content.php:160
#: public/dashboard/dashboard-content.php:417
#: public/dashboard/manage-order.php:192 public/dashboard/orders.php:49
#: public/dashboard/orders.php:206
msgid "Completed"
msgstr ""

#: includes/class-marketking-core.php:4000
#: public/dashboard/manage-order.php:218 public/dashboard/orders.php:50
#: public/dashboard/orders.php:209
msgid "Refunded"
msgstr ""

#: includes/class-marketking-core.php:4003
#: public/class-marketking-core-public.php:2490
#: public/dashboard/dashboard-content.php:172
#: public/dashboard/manage-order.php:211 public/dashboard/orders.php:51
#: public/dashboard/orders.php:212
msgid "Cancelled"
msgstr ""

#: includes/class-marketking-core.php:4006 public/dashboard/orders.php:43
#: public/dashboard/orders.php:215
msgid "Pending Payment"
msgstr ""

#: includes/class-marketking-core.php:4009
#: public/dashboard/manage-order.php:225 public/dashboard/orders.php:52
#: public/dashboard/orders.php:218
msgid "Failed"
msgstr ""

#: includes/class-marketking-core.php:4047 public/dashboard/orders.php:263
msgid "Items"
msgstr ""

#: includes/class-marketking-core.php:4083 public/dashboard/orders.php:295
msgid "tax"
msgstr ""

#: includes/class-marketking-core.php:4095 public/dashboard/orders.php:308
#: public/templates/order-details.php:93
msgid "View Order"
msgstr ""

#: includes/class-marketking-core.php:4189
msgid "invoice"
msgstr ""

#: includes/class-marketking-core.php:4460
#: includes/class-marketking-core.php:4644
#: public/emails/templates/new-product-requires-approval-email-template.php:25
#: public/emails/templates/new-rating-email-template.php:18
#: public/emails/templates/plain-new-product-requires-approval-email-template.php:18
#: public/emails/templates/plain-new-rating-email-template.php:15
#: public/emails/templates/plain-product-has-been-approved-email-template.php:14
#: public/emails/templates/product-has-been-approved-email-template.php:28
msgid "Product: "
msgstr ""

#: includes/class-marketking-core.php:4463
msgid "Order: #"
msgstr ""

#: includes/class-marketking-core.php:4473
#: includes/class-marketking-core.php:4662
msgid "User:"
msgstr ""

#: includes/class-marketking-core.php:4484
msgid "The user sent this support request from the order page."
msgstr ""

#: includes/class-marketking-core.php:4488
#: public/class-marketking-core-public.php:3072
msgid "Support Request"
msgstr ""

#: includes/class-marketking-core.php:4571
msgid "A new shipment was created."
msgstr ""

#: includes/class-marketking-core.php:4574
#: includes/class-marketking-core.php:4577
msgid "Provider: "
msgstr ""

#: includes/class-marketking-core.php:4581
msgid "Tracking Number: "
msgstr ""

#: includes/class-marketking-core.php:4582
msgid "Tracking URL: "
msgstr ""

#: includes/class-marketking-core.php:4582
msgid "Click to track shipment"
msgstr ""

#: includes/class-marketking-core.php:4626
msgid "Name: "
msgstr ""

#: includes/class-marketking-core.php:4630
#: public/emails/templates/new-vendor-requires-approval-email-template.php:19
#: public/emails/templates/plain-new-vendor-requires-approval-email-template.php:16
msgid "Email: "
msgstr ""

#: includes/class-marketking-core.php:4635
msgid "Phone: "
msgstr ""

#: includes/class-marketking-core.php:4647
#: public/emails/templates/new-message-email-template.php:25
msgid "Message: "
msgstr ""

#: includes/class-marketking-core.php:4653
msgid "Name:"
msgstr ""

#: includes/class-marketking-core.php:4653
msgid " Email:"
msgstr ""

#: includes/class-marketking-core.php:4677
#: includes/class-marketking-core.php:4678
msgid "The user has not purchased this product."
msgstr ""

#: includes/class-marketking-core.php:4681
#: includes/class-marketking-core.php:4682
msgid "The user has purchased this product."
msgstr ""

#: includes/class-marketking-core.php:4722
msgid ""
"This inquiry was sent by a logged out user, without an account. Please email "
"the user directly!"
msgstr ""

#: includes/class-marketking-core.php:4997
msgid "Outstanding balance"
msgstr ""

#: includes/class-marketking-core.php:4997
msgid "Payment method"
msgstr ""

#: includes/class-marketking-core.php:5060
msgid "Operation"
msgstr ""

#: includes/class-marketking-core.php:5060
msgid "Credit balance"
msgstr ""

#: includes/class-marketking-core.php:5165
msgid "Amount:"
msgstr ""

#: includes/class-marketking-core.php:5676 includes/elementor/classes.php:786
#: public/templates/store-page.php:352
msgid "There are no reviews yet..."
msgstr ""

#: includes/class-marketking-core.php:5770
#: public/class-marketking-core-public.php:994
msgid "Go to the Vendor Dashboard"
msgstr ""

#: includes/class-marketking-core.php:5779
msgid ""
"You have applied for a vendor account. We are currently reviewing your "
"application."
msgstr ""

#: includes/class-marketking-core.php:5794
msgid "Send application"
msgstr ""

#: includes/class-marketking-core.php:5806
msgid ""
"You are already logged in and cannot apply for a new account. To apply for a "
"new Vendor account, please logout first. "
msgstr ""

#: includes/class-marketking-core.php:5808
msgid "Click here to log out"
msgstr ""

#: includes/class-marketking-core.php:5822
#: includes/class-marketking-core.php:5865
msgid "Register"
msgstr ""

#: includes/class-marketking-core.php:5831
msgid "Username"
msgstr ""

#: includes/class-marketking-core.php:5838
msgid "Email address"
msgstr ""

#: includes/class-marketking-core.php:5845
#: public/dashboard/marketking-dashboard-login.php:2427
msgid "Password"
msgstr ""

#: includes/class-marketking-core.php:5851
msgid "A password will be sent to your email address."
msgstr ""

#: includes/class-marketking-core.php:6119
msgid "Split Pay for Order #"
msgstr ""

#: includes/class-marketking-core.php:6120
msgid "Payment for Order #"
msgstr ""

#. translators: %s location.
#: includes/class-marketking-order-splitter.php:95
#: includes/class-marketking-order-splitter.php:706
msgid "(estimated)"
msgstr ""

#: includes/class-marketking-order-splitter.php:495
msgid ""
"This is a composite order that contains products sold by different vendors."
msgstr ""

#: includes/class-marketking-order-splitter.php:495
msgid "Here is a list of its sub-orders:"
msgstr ""

#: includes/class-marketking-order-splitter.php:500
msgid "order "
msgstr ""

#: includes/class-marketking-order-splitter.php:500
msgid "handled by "
msgstr ""

#: includes/class-marketking-order-splitter.php:513
#: includes/class-marketking-order-splitter.php:693
msgid "Fees"
msgstr ""

#: includes/class-marketking-order-splitter.php:686
#: public/templates/cart.php:97 public/templates/cart.php:198
#: public/templates/invoices/commission-invoice.php:216
msgid "Subtotal"
msgstr ""

#: includes/class-marketking-order-splitter.php:689
msgid "Discounts"
msgstr ""

#: includes/class-marketking-order-splitter.php:697
#: public/dashboard/manage-order.php:281
#: public/dashboard/templates/profile-sidebar.php:77
msgid "Shipping"
msgstr ""

#: includes/class-marketking-order-splitter.php:706
#: public/templates/invoices/commission-invoice.php:228
#: public/templates/order-details.php:102
msgid "Total"
msgstr ""

#: includes/elementor/classes.php:12
msgid "Store Title"
msgstr ""

#: includes/elementor/classes.php:32 includes/elementor/classes.php:144
#: includes/elementor/classes.php:254 includes/elementor/classes.php:363
#: includes/elementor/classes.php:470 includes/elementor/classes.php:590
#: includes/elementor/classes.php:692 includes/elementor/classes.php:1077
msgid "Style"
msgstr ""

#: includes/elementor/classes.php:40
msgid "Text Color"
msgstr ""

#: includes/elementor/classes.php:60
msgid "Text Shadow"
msgstr ""

#: includes/elementor/classes.php:68 includes/elementor/classes.php:152
#: includes/elementor/classes.php:275 includes/elementor/classes.php:384
#: includes/elementor/classes.php:478 includes/elementor/classes.php:598
#: includes/elementor/classes.php:700 includes/elementor/classes.php:1085
msgid "Alignment"
msgstr ""

#: includes/elementor/classes.php:72 includes/elementor/classes.php:156
#: includes/elementor/classes.php:279 includes/elementor/classes.php:388
#: includes/elementor/classes.php:482 includes/elementor/classes.php:602
#: includes/elementor/classes.php:704 includes/elementor/classes.php:1089
msgid "Left"
msgstr ""

#: includes/elementor/classes.php:76 includes/elementor/classes.php:160
#: includes/elementor/classes.php:283 includes/elementor/classes.php:392
#: includes/elementor/classes.php:486 includes/elementor/classes.php:606
#: includes/elementor/classes.php:708 includes/elementor/classes.php:1093
msgid "Center"
msgstr ""

#: includes/elementor/classes.php:80 includes/elementor/classes.php:164
#: includes/elementor/classes.php:287 includes/elementor/classes.php:396
#: includes/elementor/classes.php:490 includes/elementor/classes.php:610
#: includes/elementor/classes.php:712 includes/elementor/classes.php:1097
msgid "Right"
msgstr ""

#: includes/elementor/classes.php:124
msgid "Vendor Badges"
msgstr ""

#: includes/elementor/classes.php:177 includes/elementor/classes.php:1110
msgid "Width"
msgstr ""

#: includes/elementor/classes.php:234 public/dashboard/profile.php:119
msgid "Profile Image"
msgstr ""

#: includes/elementor/classes.php:262 includes/elementor/classes.php:371
msgid "Image Dimension"
msgstr ""

#: includes/elementor/classes.php:264 includes/elementor/classes.php:373
msgid ""
"Crop the original image size to any custom size. Set custom width or height "
"to keep the original size ratio."
msgstr ""

#: includes/elementor/classes.php:301 includes/elementor/classes.php:410
msgid "Border"
msgstr ""

#: includes/elementor/classes.php:343 public/dashboard/profile.php:139
msgid "Banner Image"
msgstr ""

#: includes/elementor/classes.php:450
msgid "Store Tabs"
msgstr ""

#: includes/elementor/classes.php:519
#: public/class-marketking-core-public.php:765
#: public/templates/store-page.php:225
msgid "Vendor Details"
msgstr ""

#: includes/elementor/classes.php:525 public/templates/store-page.php:231
msgid "Feedback"
msgstr ""

#: includes/elementor/classes.php:537 public/templates/store-page.php:243
msgid "Policies"
msgstr ""

#: includes/elementor/classes.php:549 public/templates/store-page.php:255
msgid "Contact"
msgstr ""

#: includes/elementor/classes.php:570
msgid "Follow Button"
msgstr ""

#: includes/elementor/classes.php:645
#: public/class-marketking-core-public.php:3080
#: public/templates/store-page.php:274 public/templates/stores-list.php:94
#: public/templates/stores-list.php:196 public/templates/stores-list.php:261
msgid "Follow"
msgstr ""

#: includes/elementor/classes.php:647
#: public/class-marketking-core-public.php:3081
#: public/templates/store-page.php:276 public/templates/stores-list.php:198
msgid "Following"
msgstr ""

#: includes/elementor/classes.php:672
msgid "Store Tabs Content"
msgstr ""

#: includes/elementor/classes.php:1010
#: public/dashboard/templates/profile-sidebar.php:118
msgid "Store Notice"
msgstr ""

#: includes/elementor/classes.php:1057
msgid "Social Media Icons (MarketKing)"
msgstr ""

#: public/class-marketking-core-public.php:220
#: public/class-marketking-core-public.php:230
msgid "You cannot purchase your own products!"
msgstr ""

#: public/class-marketking-core-public.php:277
msgid "Multiple"
msgstr ""

#: public/class-marketking-core-public.php:316
msgid "Invalid file type"
msgstr ""

#: public/class-marketking-core-public.php:318
msgid "Supported file types"
msgstr ""

#: public/class-marketking-core-public.php:322
msgid "File is too large. Max. upload file size is"
msgstr ""

#: public/class-marketking-core-public.php:402
msgid "is a required field."
msgstr ""

#: public/class-marketking-core-public.php:590
msgid "This vendor doesn't have any products yet..."
msgstr ""

#: public/class-marketking-core-public.php:776
msgid "Product Inquiry"
msgstr ""

#: public/class-marketking-core-public.php:790
msgid "Product Support"
msgstr ""

#: public/class-marketking-core-public.php:804
msgid "Other Offers"
msgstr ""

#: public/class-marketking-core-public.php:984
msgid "We are currently reviewing your vendor application and it is pending."
msgstr ""

#: public/class-marketking-core-public.php:1014
msgid "Your account is waiting for approval. Until approved, you cannot login."
msgstr ""

#: public/class-marketking-core-public.php:1030
msgid ""
"Before you can login, your account requires manual approval. Our team will "
"review it as soon as possible. Thank you for understanding."
msgstr ""

#: public/class-marketking-core-public.php:1061
msgid ""
"Your account has been succesfully created. We are now reviewing your "
"application to become a vendor. Please wait to be approved."
msgstr ""

#: public/class-marketking-core-public.php:1068
msgid ""
"Thank you for registering. Your vendor account requires manual approval. "
"Please wait to be approved."
msgstr ""

#: public/class-marketking-core-public.php:1572
#: public/dashboard/templates/header-bar.php:156
msgid "Recent Messages"
msgstr ""

#: public/class-marketking-core-public.php:1675
#: public/class-marketking-core-public.php:1735
#: public/dashboard/templates/header-bar.php:258
#: public/dashboard/templates/header-bar.php:315
msgid "View All"
msgstr ""

#: public/class-marketking-core-public.php:1699
#: public/dashboard/templates/header-bar.php:279
msgid "Unread Announcements"
msgstr ""

#: public/class-marketking-core-public.php:1811
#: public/dashboard/templates/header-bar.php:384
msgid "Go to My Store"
msgstr ""

#: public/class-marketking-core-public.php:1820
#: public/dashboard/templates/header-bar.php:393
msgid "Store Settings"
msgstr ""

#: public/class-marketking-core-public.php:1829
#: public/dashboard/templates/header-bar.php:402
msgid "Sign out"
msgstr ""

#: public/class-marketking-core-public.php:1933
msgid "WooCommerce"
msgstr ""

#: public/class-marketking-core-public.php:1995
#: public/class-marketking-core-public.php:2265
msgctxt "enhanced select"
msgid "No matches found"
msgstr ""

#: public/class-marketking-core-public.php:1996
#: public/class-marketking-core-public.php:2266
msgctxt "enhanced select"
msgid "Loading failed"
msgstr ""

#: public/class-marketking-core-public.php:1997
#: public/class-marketking-core-public.php:2267
msgctxt "enhanced select"
msgid "Please enter 1 or more characters"
msgstr ""

#: public/class-marketking-core-public.php:1998
#: public/class-marketking-core-public.php:2268
msgctxt "enhanced select"
msgid "Please enter %qty% or more characters"
msgstr ""

#: public/class-marketking-core-public.php:1999
#: public/class-marketking-core-public.php:2269
msgctxt "enhanced select"
msgid "Please delete 1 character"
msgstr ""

#: public/class-marketking-core-public.php:2000
#: public/class-marketking-core-public.php:2270
msgctxt "enhanced select"
msgid "Please delete %qty% characters"
msgstr ""

#: public/class-marketking-core-public.php:2001
#: public/class-marketking-core-public.php:2271
msgctxt "enhanced select"
msgid "You can only select 1 item"
msgstr ""

#: public/class-marketking-core-public.php:2002
#: public/class-marketking-core-public.php:2272
msgctxt "enhanced select"
msgid "You can only select %qty% items"
msgstr ""

#: public/class-marketking-core-public.php:2003
#: public/class-marketking-core-public.php:2273
msgctxt "enhanced select"
msgid "Loading more results&hellip;"
msgstr ""

#: public/class-marketking-core-public.php:2004
#: public/class-marketking-core-public.php:2274
msgctxt "enhanced select"
msgid "Searching&hellip;"
msgstr ""

#. translators: %s: decimal
#: public/class-marketking-core-public.php:2046
#, php-format
msgid "Please enter with one decimal point (%s) without thousand separators."
msgstr ""

#. translators: %s: price decimal separator
#: public/class-marketking-core-public.php:2048
#, php-format
msgid ""
"Please enter with one monetary decimal point (%s) without thousand "
"separators and currency symbols."
msgstr ""

#: public/class-marketking-core-public.php:2049
msgid "Please enter in country code with two capital letters."
msgstr ""

#: public/class-marketking-core-public.php:2050
msgid "Please enter in a value less than the regular price."
msgstr ""

#: public/class-marketking-core-public.php:2051
msgid ""
"This product has produced sales and may be linked to existing orders. Are "
"you sure you want to delete it?"
msgstr ""

#: public/class-marketking-core-public.php:2052
msgid ""
"This action cannot be reversed. Are you sure you wish to erase personal data "
"from the selected orders?"
msgstr ""

#: public/class-marketking-core-public.php:2057
#: public/dashboard/products.php:103
msgid "Import"
msgstr ""

#: public/class-marketking-core-public.php:2058
#: public/dashboard/products.php:113
msgid "Export"
msgstr ""

#: public/class-marketking-core-public.php:2078
msgid "Enable reviews"
msgstr ""

#. translators: %d: Number of variations
#: public/class-marketking-core-public.php:2109
#, php-format
msgid ""
"Are you sure you want to link all variations? This will create a new "
"variation for each and every possible combination of variation attributes "
"(max %d per run)."
msgstr ""

#: public/class-marketking-core-public.php:2110
#: public/dashboard/marketking-dashboard-login.php:1745
msgid "Enter a value"
msgstr ""

#: public/class-marketking-core-public.php:2111
msgid "Variation menu order (determines position in the list of variations)"
msgstr ""

#: public/class-marketking-core-public.php:2112
#: public/dashboard/marketking-dashboard-login.php:1746
msgid "Enter a value (fixed or %)"
msgstr ""

#: public/class-marketking-core-public.php:2113
msgid "Are you sure you want to delete all variations? This cannot be undone."
msgstr ""

#: public/class-marketking-core-public.php:2114
msgid "Last warning, are you sure?"
msgstr ""

#: public/class-marketking-core-public.php:2115
msgid "Choose an image"
msgstr ""

#: public/class-marketking-core-public.php:2116
msgid "Set variation image"
msgstr ""

#: public/class-marketking-core-public.php:2117
msgid "variation added"
msgstr ""

#: public/class-marketking-core-public.php:2118
msgid "variations added"
msgstr ""

#: public/class-marketking-core-public.php:2119
msgid "No variations added"
msgstr ""

#: public/class-marketking-core-public.php:2120
msgid "Are you sure you want to remove this variation?"
msgstr ""

#: public/class-marketking-core-public.php:2121
msgid "Sale start date (YYYY-MM-DD format or leave blank)"
msgstr ""

#: public/class-marketking-core-public.php:2122
msgid "Sale end date (YYYY-MM-DD format or leave blank)"
msgstr ""

#: public/class-marketking-core-public.php:2123
msgid "Save changes before changing page?"
msgstr ""

#: public/class-marketking-core-public.php:2124
msgid "%qty% variation"
msgstr ""

#: public/class-marketking-core-public.php:2125
msgid "%qty% variations"
msgstr ""

#: public/class-marketking-core-public.php:2139
#: public/class-marketking-core-public.php:2264
msgid "Select an option&hellip;"
msgstr ""

#: public/class-marketking-core-public.php:2142
msgid "Name (required)"
msgstr ""

#: public/class-marketking-core-public.php:2143
msgid "Value (required)"
msgstr ""

#: public/class-marketking-core-public.php:2152
msgid "Generate coupon code"
msgstr ""

#: public/class-marketking-core-public.php:2161
msgid "Are you sure you want to remove the selected items?"
msgstr ""

#: public/class-marketking-core-public.php:2162
msgid "Are you sure you want to remove the selected fees?"
msgstr ""

#: public/class-marketking-core-public.php:2163
msgid "Are you sure you want to remove the selected shipping?"
msgstr ""

#: public/class-marketking-core-public.php:2171
msgid "You may need to manually restore the item's stock."
msgstr ""

#: public/class-marketking-core-public.php:2178
msgid "Please select some items."
msgstr ""

#: public/class-marketking-core-public.php:2179
msgid ""
"Are you sure you wish to process this refund? This action cannot be undone."
msgstr ""

#: public/class-marketking-core-public.php:2180
msgid ""
"Are you sure you wish to delete this refund? This action cannot be undone."
msgstr ""

#: public/class-marketking-core-public.php:2181
msgid ""
"Are you sure you wish to delete this tax column? This action cannot be "
"undone."
msgstr ""

#: public/class-marketking-core-public.php:2182
msgid "Remove this item meta?"
msgstr ""

#: public/class-marketking-core-public.php:2183
msgid "Remove this attribute?"
msgstr ""

#: public/class-marketking-core-public.php:2184
#: public/dashboard/products.php:141
msgid "Name"
msgstr ""

#: public/class-marketking-core-public.php:2185
#: public/dashboard/marketking-dashboard-login.php:801
msgid "Remove"
msgstr ""

#: public/class-marketking-core-public.php:2186
msgid "Click to toggle"
msgstr ""

#: public/class-marketking-core-public.php:2187
msgid "Value(s)"
msgstr ""

#: public/class-marketking-core-public.php:2188
msgid "Enter some text, or some attributes by pipe (|) separating values."
msgstr ""

#: public/class-marketking-core-public.php:2189
msgid "Visible on the product page"
msgstr ""

#: public/class-marketking-core-public.php:2190
msgid "Used for variations"
msgstr ""

#: public/class-marketking-core-public.php:2191
msgid "Enter a name for the new attribute term:"
msgstr ""

#: public/class-marketking-core-public.php:2192
msgid ""
"Recalculate totals? This will calculate taxes based on the customers country "
"(or the store base country) and update totals."
msgstr ""

#: public/class-marketking-core-public.php:2193
msgid ""
"Copy billing information to shipping information? This will remove any "
"currently entered shipping information."
msgstr ""

#: public/class-marketking-core-public.php:2194
msgid ""
"Load the customer's billing information? This will remove any currently "
"entered billing information."
msgstr ""

#: public/class-marketking-core-public.php:2195
msgid ""
"Load the customer's shipping information? This will remove any currently "
"entered shipping information."
msgstr ""

#: public/class-marketking-core-public.php:2196
msgid "Featured"
msgstr ""

#: public/class-marketking-core-public.php:2200
msgid "No customer selected"
msgstr ""

#: public/class-marketking-core-public.php:2224
msgid ""
"Could not grant access - the user may already have permission for this file "
"or billing email is not set. Ensure the billing email is set, and the order "
"has been saved."
msgstr ""

#: public/class-marketking-core-public.php:2225
msgid "Are you sure you want to revoke access to this download?"
msgstr ""

#: public/class-marketking-core-public.php:2226
msgid "You cannot add the same tax rate twice!"
msgstr ""

#: public/class-marketking-core-public.php:2227
msgid ""
"Are you sure you wish to delete this note? This action cannot be undone."
msgstr ""

#: public/class-marketking-core-public.php:2228
msgid ""
"Enter a coupon code to apply. Discounts are applied to line totals, before "
"taxes."
msgstr ""

#: public/class-marketking-core-public.php:2229
msgid "Enter a fixed amount or percentage to apply as a fee."
msgstr ""

#: public/class-marketking-core-public.php:2230
msgid ""
"<b>Simple –</b> covers the vast majority of any products you may sell. "
"Simple products are shipped and have no options. For example, a book."
msgstr ""

#: public/class-marketking-core-public.php:2231
msgid ""
"<b>Grouped –</b> a collection of related products that can be purchased "
"individually and only consist of simple products. For example, a set of six "
"drinking glasses."
msgstr ""

#: public/class-marketking-core-public.php:2232
msgid ""
"<b>External or Affiliate –</b> one that you list and describe on your "
"website but is sold elsewhere."
msgstr ""

#: public/class-marketking-core-public.php:2233
msgid ""
"<b>Variable –</b> a product with variations, each of which may have a "
"different SKU, price, stock option, etc. For example, a t-shirt available in "
"different colors and/or sizes."
msgstr ""

#: public/class-marketking-core-public.php:2234
msgid ""
"Product types define available product details and attributes, such as "
"downloadable files and variations. They’re also used for analytics and "
"inventory management."
msgstr ""

#: public/class-marketking-core-public.php:2235
msgid ""
"Describe this product. What makes it unique? What are its most important "
"features?"
msgstr ""

#: public/class-marketking-core-public.php:2236
msgid ""
"Summarize this product in 1-2 short sentences. We’ll show it at the top of "
"the page."
msgstr ""

#. translators: %1$s: maximum file size
#: public/class-marketking-core-public.php:2238
#, php-format
msgid ""
"For best results, upload JPEG or PNG files that are 1000 by 1000 pixels or "
"larger. Maximum upload file size: %1$s."
msgstr ""

#: public/class-marketking-core-public.php:2239
msgid ""
"If you remove this attribute, customers will no longer be able to purchase "
"some variations of this product."
msgstr ""

#: public/class-marketking-core-public.php:2240
msgid "Adding new attribute failed."
msgstr ""

#. translators: %s: WC_DELIMITER
#: public/class-marketking-core-public.php:2242
#, php-format
msgid "Enter some descriptive text. Use “%s” to separate different values."
msgstr ""

#: public/class-marketking-core-public.php:2243
#, php-format
msgid ""
"Enter options for customers to choose from, f.e. “Blue” or “Large”. Use “%s” "
"to separate different options."
msgstr ""

#: public/class-marketking-core-public.php:2342
#: public/class-marketking-core-public.php:2528
msgid "Min. Quantity"
msgstr ""

#: public/class-marketking-core-public.php:2343
#: public/class-marketking-core-public.php:2529
msgid "Final Price"
msgstr ""

#: public/class-marketking-core-public.php:2344
#: public/class-marketking-core-public.php:2530
msgid "Label"
msgstr ""

#: public/class-marketking-core-public.php:2345
#: public/class-marketking-core-public.php:2545
msgid "Text"
msgstr ""

#: public/class-marketking-core-public.php:2346
msgid "X"
msgstr ""

#: public/class-marketking-core-public.php:2348
msgid "Are you sure you want to publish this offer?"
msgstr ""

#: public/class-marketking-core-public.php:2349
msgid "Are you sure you want to delete this offer?"
msgstr ""

#: public/class-marketking-core-public.php:2350
msgid "Are you sure you want to publish this rule?"
msgstr ""

#: public/class-marketking-core-public.php:2351
#: public/class-marketking-core-public.php:2379
msgid "Are you sure you want to delete this rule?"
msgstr ""

#: public/class-marketking-core-public.php:2352
msgid "You must enter a title for the rule!"
msgstr ""

#: public/class-marketking-core-public.php:2353
msgid "You must enter a title for the offer!"
msgstr ""

#: public/class-marketking-core-public.php:2354
msgid "You must have at least 1 product with quantity and price!"
msgstr ""

#: public/class-marketking-core-public.php:2355
msgid "Cart Total Quantity"
msgstr ""

#: public/class-marketking-core-public.php:2356
msgid "Cart Total Value"
msgstr ""

#: public/class-marketking-core-public.php:2357
msgid "Category Product Quantity"
msgstr ""

#: public/class-marketking-core-public.php:2358
msgid "Category Product Value"
msgstr ""

#: public/class-marketking-core-public.php:2359
msgid "Product Quantity"
msgstr ""

#: public/class-marketking-core-public.php:2360
msgid "Product Value"
msgstr ""

#: public/class-marketking-core-public.php:2361
msgid "greater (>)"
msgstr ""

#: public/class-marketking-core-public.php:2362
msgid "equal (=)"
msgstr ""

#: public/class-marketking-core-public.php:2363
msgid "smaller (<)"
msgstr ""

#: public/class-marketking-core-public.php:2364
#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:185
msgid "Delete"
msgstr ""

#: public/class-marketking-core-public.php:2365
msgid "Enter the quantity/value"
msgstr ""

#: public/class-marketking-core-public.php:2366
msgid "Add Condition"
msgstr ""

#: public/class-marketking-core-public.php:2367
msgid "Conditions must apply cumulatively."
msgstr ""

#: public/class-marketking-core-public.php:2368
msgid "Each product must meet all product conditions."
msgstr ""

#: public/class-marketking-core-public.php:2372
#: public/class-marketking-core-public.php:2552
msgid ""
"Your cart contains items from multiple vendors. Quote requests can only be "
"sent to 1 vendor at a time. Please adjust cart items."
msgstr ""

#: public/class-marketking-core-public.php:2374
msgid "Email Offer"
msgstr ""

#: public/class-marketking-core-public.php:2375
msgid ""
"This offer will be emailed to ALL users that have visibility. That includes "
"all groups you selected, all users, and all email addresses entered. Make "
"sure to save the offer first if you made changes to it! Are you sure you "
"want to proceed?"
msgstr ""

#: public/class-marketking-core-public.php:2376
msgid "The offer has been emailed successfully."
msgstr ""

#: public/class-marketking-core-public.php:2378
msgid "The offer has been saved. Do you want to also email it to the user?"
msgstr ""

#: public/class-marketking-core-public.php:2461
msgid ""
"Are you sure you want to permanently CANCEL this subscription? This is "
"irreversible."
msgstr ""

#: public/class-marketking-core-public.php:2462
msgid "Are you sure you want to delete this coupon?"
msgstr ""

#: public/class-marketking-core-public.php:2463
msgid "Are you sure you want to save this cart?"
msgstr ""

#: public/class-marketking-core-public.php:2464
#: public/class-marketking-core-public.php:3096
msgid "Are you sure you want to send this refund request?"
msgstr ""

#: public/class-marketking-core-public.php:2465
msgid "Are you sure you want to delete this cart?"
msgstr ""

#: public/class-marketking-core-public.php:2466
msgid "Are you sure you want to add this customer?"
msgstr ""

#: public/class-marketking-core-public.php:2467
msgid "Are you sure you want to add this team member?"
msgstr ""

#: public/class-marketking-core-public.php:2468
msgid "Are you sure you want to save the payout info?"
msgstr ""

#: public/class-marketking-core-public.php:2469
msgid "Are you sure you want to disconnect this Stripe account?"
msgstr ""

#: public/class-marketking-core-public.php:2470
msgid "Are you sure you want to make the withdrawal request?"
msgstr ""

#: public/class-marketking-core-public.php:2471
msgid "Are you sure you want to cancel your current withdrawal request?"
msgstr ""

#: public/class-marketking-core-public.php:2472
msgid "The requested withdrawal amount is below the minimum limit."
msgstr ""

#: public/class-marketking-core-public.php:2473
msgid "You do not have sufficient funds for this withdrawal."
msgstr ""

#: public/class-marketking-core-public.php:2474
msgid "Ready"
msgstr ""

#: public/class-marketking-core-public.php:2475
msgid "Link copied"
msgstr ""

#: public/class-marketking-core-public.php:2476
msgid "Copied"
msgstr ""

#: public/class-marketking-core-public.php:2477
msgid "Search "
msgstr ""

#: public/class-marketking-core-public.php:2478
msgid "Copy"
msgstr ""

#: public/class-marketking-core-public.php:2479
msgid ""
"The customer account has been created. An email has been sent to the "
"customer with account details."
msgstr ""

#: public/class-marketking-core-public.php:2480
msgid ""
"The customer account could not be created. It may be because the username or "
"email already exists. Here are the error details:"
msgstr ""

#: public/class-marketking-core-public.php:2481
msgid ""
"The team member account has been created. An email has been sent to them "
"with account details."
msgstr ""

#: public/class-marketking-core-public.php:2482
msgid "Please fill all required (*) fields / resolve all validation errors."
msgstr ""

#: public/class-marketking-core-public.php:2483
msgid ""
"The team member account could not be created. It may be because the username "
"or email already exists. Here are the error details: "
msgstr ""

#: public/class-marketking-core-public.php:2491
msgid "orders"
msgstr ""

#: public/class-marketking-core-public.php:2492
msgid "Upload Image"
msgstr ""

#: public/class-marketking-core-public.php:2495
msgid "Are you sure you want to delete this product?"
msgstr ""

#: public/class-marketking-core-public.php:2497
msgid "Are you sure you want to advertise this product?"
msgstr ""

#: public/class-marketking-core-public.php:2498
msgid "You have insufficient credits for this advertisement"
msgstr ""

#: public/class-marketking-core-public.php:2499
msgid "You have successfully advertised this product"
msgstr ""

#: public/class-marketking-core-public.php:2500
msgid ""
"Are you sure you want to delete this team member account? This is "
"irreversible."
msgstr ""

#: public/class-marketking-core-public.php:2501
msgid "Are you sure you want to delete this shipping method?"
msgstr ""

#: public/class-marketking-core-public.php:2502
msgid "Are you sure you want to add this shipping method?"
msgstr ""

#: public/class-marketking-core-public.php:2505
msgid "The product must have a name (title)!"
msgstr ""

#: public/class-marketking-core-public.php:2519
msgid "This coupon code is already in use. Please use a different code."
msgstr ""

#: public/class-marketking-core-public.php:2533
msgid "Choose an attribute:"
msgstr ""

#: public/class-marketking-core-public.php:2546
msgid "You must save the product first before you can configure attributes."
msgstr ""

#: public/class-marketking-core-public.php:2547
msgid "You must save the product first before you can configure variations."
msgstr ""

#: public/class-marketking-core-public.php:2553
#: public/class-marketking-core-public.php:3090
msgid ""
"Are you sure you want to submit this reply? Please note that you can only "
"reply once."
msgstr ""

#: public/class-marketking-core-public.php:2554
msgid "Are you sure you want to approve this refund? This is irreversible."
msgstr ""

#: public/class-marketking-core-public.php:2555
msgid ""
"Are you sure you want to save this product as a draft? It will no longer be "
"published."
msgstr ""

#: public/class-marketking-core-public.php:2556
msgid "Are you sure you want to deny this refund? This is irreversible."
msgstr ""

#: public/class-marketking-core-public.php:2557
#: public/class-marketking-core-public.php:3093
msgid "Your reply has been submitted."
msgstr ""

#: public/class-marketking-core-public.php:2558
#: public/class-marketking-core-public.php:3094
msgid "Your report has been submitted."
msgstr ""

#: public/class-marketking-core-public.php:2559
msgid "Are you sure you want to submit this for verification?"
msgstr ""

#: public/class-marketking-core-public.php:2560
msgid "Are you sure?"
msgstr ""

#: public/class-marketking-core-public.php:2561
msgid "Yes, continue"
msgstr ""

#: public/class-marketking-core-public.php:2562
#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:184
#: public/dashboard/profile.php:253 public/dashboard/profile.php:315
#: public/dashboard/profile.php:367 public/dashboard/profile.php:419
msgid "Cancel"
msgstr ""

#: public/class-marketking-core-public.php:2563
msgid "Please select a file."
msgstr ""

#: public/class-marketking-core-public.php:2576
#: public/dashboard/products.php:195
msgid "type"
msgstr ""

#: public/class-marketking-core-public.php:2579
msgid "Loading shipping data..."
msgstr ""

#: public/class-marketking-core-public.php:2792
msgid "User Type"
msgstr ""

#: public/class-marketking-core-public.php:2933
msgid "Supported file types: jpg, jpeg, png, txt, pdf, doc, docx"
msgstr ""

#: public/class-marketking-core-public.php:3014
msgid "State / County"
msgstr ""

#: public/class-marketking-core-public.php:3062
msgid "This URL is not available!"
msgstr ""

#: public/class-marketking-core-public.php:3063
msgid "Searching availability..."
msgstr ""

#: public/class-marketking-core-public.php:3064
msgid "The URL is too short..."
msgstr ""

#: public/class-marketking-core-public.php:3066
msgid "There are no sellers yet..."
msgstr ""

#: public/class-marketking-core-public.php:3069
msgid ""
"Your message has been received. We will get back to you as soon as possible."
msgstr ""

#: public/class-marketking-core-public.php:3070
msgid ""
"Your support request has been received. The vendor will get back to you as "
"soon as possible."
msgstr ""

#: public/class-marketking-core-public.php:3073
msgid "Send inquiry"
msgstr ""

#: public/class-marketking-core-public.php:3074
msgid "Please fill all fields to submit the inquiry"
msgstr ""

#: public/class-marketking-core-public.php:3075
msgid "The email address you entered is invalid"
msgstr ""

#: public/class-marketking-core-public.php:3079
msgid ""
"Your cart contains items from multiple vendors. Quote requests can only be "
"sent to 1 vendor at a time. Please adjust cart items. You may need to reload "
"the cart page."
msgstr ""

#: public/class-marketking-core-public.php:3082
msgid "Are you sure you want to submit this abuse report?"
msgstr ""

#: public/class-marketking-core-public.php:3083
msgid ""
"Are you sure you want to mark this order as Received? This means you are "
"confirming you have correctly received all packages and items within this "
"order."
msgstr ""

#: public/class-marketking-core-public.php:3084
msgid "Thank you for your submission. Your report has been received."
msgstr ""

#: public/class-marketking-core-public.php:3085
msgid "Your abuse report has been received."
msgstr ""

#: public/class-marketking-core-public.php:3086
msgid "The product has been added to your store"
msgstr ""

#: public/class-marketking-core-public.php:3095
msgid "Your request has been sent."
msgstr ""

#: public/class-marketking-core-public.php:3097
msgid "The value requested cannot exceed the order value."
msgstr ""

#: public/class-marketking-core-public.php:3104
#: public/templates/stores-list.php:37
msgid "All Categories"
msgstr ""

#: public/dashboard/dashboard-content.php:28
msgid ""
"Your store is in vacation mode and products cannot be purchased. You can "
"modify this via Settings -> Vacation."
msgstr ""

#: public/dashboard/dashboard-content.php:43
msgid "Welcome to your vendor dashboard"
msgstr ""

#: public/dashboard/dashboard-content.php:49
msgid " Here's everything at a glance..."
msgstr ""

#: public/dashboard/dashboard-content.php:64
msgid "Balance available"
msgstr ""

#: public/dashboard/dashboard-content.php:93
msgid "earnings in the last 30 days"
msgstr ""

#: public/dashboard/dashboard-content.php:96
msgid "Earnings this month so far"
msgstr ""

#: public/dashboard/dashboard-content.php:122
msgid "Order Statistics (last 30 days)"
msgstr ""

#: public/dashboard/dashboard-content.php:146
msgid "There are no orders yet..."
msgstr ""

#: public/dashboard/dashboard-content.php:185
msgid "Store Statistics (last 30 days)"
msgstr ""

#: public/dashboard/dashboard-content.php:255
msgid "Customers (Unique)"
msgstr ""

#: public/dashboard/dashboard-content.php:264
msgid "Items Sold"
msgstr ""

#: public/dashboard/dashboard-content.php:274
msgid "Average Order"
msgstr ""

#: public/dashboard/dashboard-content.php:274
msgid "Average order value, excluding shipping cost."
msgstr ""

#: public/dashboard/dashboard-content.php:308
msgid "Recent Orders"
msgstr ""

#: public/dashboard/dashboard-content.php:314
msgid "Order No."
msgstr ""

#: public/dashboard/dashboard-content.php:318
#: public/dashboard/manage-order.php:125 public/dashboard/orders.php:38
#: public/dashboard/orders.php:77 public/dashboard/products.php:53
#: public/dashboard/products.php:157
msgid "Status"
msgstr ""

#: public/dashboard/dashboard-content.php:369 public/dashboard/orders.php:249
msgid "(guest user)"
msgstr ""

#: public/dashboard/dashboard-content.php:411
#: public/dashboard/dashboard-content.php:414
msgid "Pending Order Completion"
msgstr ""

#: public/dashboard/dashboard-content.php:420
msgid "Order Refunded"
msgstr ""

#: public/dashboard/dashboard-content.php:423
msgid "Order Cancelled"
msgstr ""

#: public/dashboard/dashboard-content.php:426
msgid "Pending Order Payment"
msgstr ""

#: public/dashboard/dashboard-content.php:429
msgid "Order Failed"
msgstr ""

#: public/dashboard/dashboard-content.php:489
#: public/dashboard/edit-product.php:825 public/dashboard/manage-order.php:743
#: public/dashboard/orders.php:335 public/dashboard/payouts.php:751
#: public/dashboard/products.php:593 public/dashboard/profile-settings.php:188
#: public/dashboard/profile.php:446
msgid ""
"This account does not have the permission to visit this section. Please use "
"the main account or request permission for this section from the main vendor "
"account."
msgstr ""

#: public/dashboard/edit-product.php:114
msgid "Send New Product for Review"
msgstr ""

#: public/dashboard/edit-product.php:118
msgid "Save New Product"
msgstr ""

#: public/dashboard/edit-product.php:124
msgid "Send for Review"
msgstr ""

#: public/dashboard/edit-product.php:128
msgid "Publish Product"
msgstr ""

#: public/dashboard/edit-product.php:156
msgid "Published Product"
msgstr ""

#: public/dashboard/edit-product.php:157
msgid "Product Pending Review"
msgstr ""

#: public/dashboard/edit-product.php:158
msgid "Draft Product"
msgstr ""

#: public/dashboard/edit-product.php:159
msgid "New Product"
msgstr ""

#: public/dashboard/edit-product.php:173
msgid ""
"When a product is ready to be published, let us know by clicking on \"Send "
"for Review\". If you are still working on the product, save it as a draft "
"instead."
msgstr ""

#: public/dashboard/edit-product.php:181
msgid "Save as Draft"
msgstr ""

#: public/dashboard/edit-product.php:188
msgid "More"
msgstr ""

#: public/dashboard/edit-product.php:229
msgid "Preview Product"
msgstr ""

#: public/dashboard/edit-product.php:250 public/dashboard/products.php:132
msgid "Your product has been created successfully"
msgstr ""

#: public/dashboard/edit-product.php:250 public/dashboard/products.php:132
msgid "You can now continue to edit it"
msgstr ""

#: public/dashboard/edit-product.php:258
msgid "Your product has been updated successfully"
msgstr ""

#: public/dashboard/edit-product.php:278
msgid "Product Name"
msgstr ""

#: public/dashboard/edit-product.php:286
msgid "Product data"
msgstr ""

#: public/dashboard/edit-product.php:309
msgid "Translations"
msgstr ""

#: public/dashboard/edit-product.php:372
msgid "Add translation"
msgstr ""

#: public/dashboard/edit-product.php:382
msgid "Your are currently editing this translation."
msgstr ""

#: public/dashboard/edit-product.php:388
msgid "Edit translation"
msgstr ""

#: public/dashboard/edit-product.php:407
msgid "Main Product Image"
msgstr ""

#: public/dashboard/edit-product.php:433
msgid "Click the image to edit or update"
msgstr ""

#: public/dashboard/edit-product.php:439
msgid "Image Gallery"
msgstr ""

#: public/dashboard/edit-product.php:453 public/dashboard/products.php:155
msgid "Categories"
msgstr ""

#: public/dashboard/edit-product.php:503 public/dashboard/products.php:158
msgid "Tags"
msgstr ""

#: public/dashboard/edit-product.php:546
msgid "Product 3D Model"
msgstr ""

#: public/dashboard/edit-product.php:566
#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:243
msgid "QR Code"
msgstr ""

#: public/dashboard/edit-product.php:581
msgid "Auction"
msgstr ""

#: public/dashboard/edit-product.php:593
msgid "Automatic relist auction"
msgstr ""

#: public/dashboard/edit-product.php:618
msgid "User & Group Visibility (B2B & Wholesale)"
msgstr ""

#: public/dashboard/edit-product.php:651
msgid "You must save the product first, before being able to advertise it."
msgstr ""

#: public/dashboard/edit-product.php:678
msgid "Available credits:"
msgstr ""

#: public/dashboard/edit-product.php:683
msgid "Buy Credits"
msgstr ""

#: public/dashboard/edit-product.php:686
msgid "View Log"
msgstr ""

#: public/dashboard/edit-product.php:692
msgid "Cost per credit"
msgstr ""

#: public/dashboard/edit-product.php:695
msgid "Number of Credits"
msgstr ""

#: public/dashboard/edit-product.php:698
msgid "Add to Cart"
msgstr ""

#: public/dashboard/edit-product.php:712
msgid "Advertise Product"
msgstr ""

#: public/dashboard/edit-product.php:725
msgid "Cost per day:"
msgstr ""

#: public/dashboard/edit-product.php:725
msgid "credits"
msgstr ""

#: public/dashboard/edit-product.php:730
msgid "Advertise Now"
msgstr ""

#: public/dashboard/edit-product.php:736
msgid "How many days to advertise?"
msgstr ""

#: public/dashboard/edit-product.php:742
msgid "Purchase Ad"
msgstr ""

#: public/dashboard/edit-product.php:767
#: public/dashboard/integrations/fooevents.php:193
#: public/templates/invoices/commission-invoice.php:139
msgid "Description"
msgstr ""

#: public/dashboard/edit-product.php:791
msgid "Short Description"
msgstr ""

#: public/dashboard/integrations/fooevents.php:20
#: public/dashboard/integrations/fooevents.php:83
msgid "Done"
msgstr ""

#: public/dashboard/integrations/fooevents.php:24
#: public/dashboard/integrations/fooevents.php:87
msgid "Show a different month"
msgstr ""

#: public/dashboard/integrations/fooevents.php:35
#: public/dashboard/integrations/fooevents.php:242
msgid "minutes"
msgstr ""

#: public/dashboard/integrations/fooevents.php:36
#: public/dashboard/integrations/fooevents.php:240
msgid "hours"
msgstr ""

#: public/dashboard/integrations/fooevents.php:37
msgid "days"
msgstr ""

#: public/dashboard/integrations/fooevents.php:38
msgid "weeks"
msgstr ""

#: public/dashboard/integrations/fooevents.php:78
msgid "Day"
msgstr ""

#: public/dashboard/integrations/fooevents.php:95
#: public/dashboard/integrations/fooevents.php:197
msgid "Start time"
msgstr ""

#: public/dashboard/integrations/fooevents.php:96
#: public/dashboard/integrations/fooevents.php:198
msgid "End time"
msgstr ""

#: public/dashboard/integrations/fooevents.php:105
msgid "Your stationery settings have been saved."
msgstr ""

#: public/dashboard/integrations/fooevents.php:106
msgid "An error occurred while saving your stationery settings."
msgstr ""

#: public/dashboard/integrations/fooevents.php:160
msgid "Event Overview"
msgstr ""

#: public/dashboard/integrations/fooevents.php:161
msgid "Select an event in the <strong>Event Details</strong> section."
msgstr ""

#: public/dashboard/integrations/fooevents.php:180
msgid "Test Access"
msgstr ""

#: public/dashboard/integrations/fooevents.php:181
msgid "Testing Access..."
msgstr ""

#: public/dashboard/integrations/fooevents.php:182
msgid "Successfully connected to your Zoom account"
msgstr ""

#: public/dashboard/integrations/fooevents.php:183
msgid "Fetch Users"
msgstr ""

#: public/dashboard/integrations/fooevents.php:184
msgid "Fetching Users..."
msgstr ""

#: public/dashboard/integrations/fooevents.php:185
msgid ""
"Show only meetings/webinars for the user that generated the API Key and "
"Secret"
msgstr ""

#: public/dashboard/integrations/fooevents.php:186
msgid "Show all meetings/webinars created by the following users:"
msgstr ""

#: public/dashboard/integrations/fooevents.php:187
msgid ""
"Please note that meeting/webinar load times will increase as more users are "
"selected."
msgstr ""

#: public/dashboard/integrations/fooevents.php:190
msgid "Not set"
msgstr ""

#: public/dashboard/integrations/fooevents.php:191
msgid "Auto-generate"
msgstr ""

#: public/dashboard/integrations/fooevents.php:192
msgid "Topic"
msgstr ""

#: public/dashboard/integrations/fooevents.php:195
msgid "Start date"
msgstr ""

#: public/dashboard/integrations/fooevents.php:196
msgid "End date"
msgstr ""

#: public/dashboard/integrations/fooevents.php:199
msgid "Duration"
msgstr ""

#: public/dashboard/integrations/fooevents.php:200
msgid "Recurrence"
msgstr ""

#: public/dashboard/integrations/fooevents.php:201
msgid "Upcoming occurrences"
msgstr ""

#: public/dashboard/integrations/fooevents.php:202
msgid "Occurrences"
msgstr ""

#: public/dashboard/integrations/fooevents.php:203
msgid "No upcoming occurrences"
msgstr ""

#: public/dashboard/integrations/fooevents.php:204
msgid "Unable to fetch meeting details"
msgstr ""

#: public/dashboard/integrations/fooevents.php:205
msgid "Unable to fetch webinar details"
msgstr ""

#: public/dashboard/integrations/fooevents.php:206
msgid "Note: Automatic attendee registration is required."
msgstr ""

#: public/dashboard/integrations/fooevents.php:207
msgid "Note: Automatic attendee registration is required for all occurrences."
msgstr ""

#: public/dashboard/integrations/fooevents.php:208
msgid "Note: Attendees will be registered automatically."
msgstr ""

#: public/dashboard/integrations/fooevents.php:209
msgid "Note: Attendees will be registered automatically for all occurrences."
msgstr ""

#: public/dashboard/integrations/fooevents.php:210
msgid "Automatic attendee registration is currently enabled for this meeting"
msgstr ""

#: public/dashboard/integrations/fooevents.php:211
msgid "Automatic attendee registration is currently enabled for this webinar"
msgstr ""

#: public/dashboard/integrations/fooevents.php:212
msgid "Automatic attendee registration is currently disabled for this meeting"
msgstr ""

#: public/dashboard/integrations/fooevents.php:213
msgid "Automatic attendee registration is currently disabled for this webinar"
msgstr ""

#: public/dashboard/integrations/fooevents.php:214
msgid "Enable automatic attendee registration for this meeting"
msgstr ""

#: public/dashboard/integrations/fooevents.php:215
msgid "Enable automatic attendee registration for this webinar"
msgstr ""

#: public/dashboard/integrations/fooevents.php:216
msgid ""
"Automatic attendee registration is currently enabled for all occurrences"
msgstr ""

#: public/dashboard/integrations/fooevents.php:217
msgid ""
"Automatic attendee registration is not currently enabled for all occurrences"
msgstr ""

#: public/dashboard/integrations/fooevents.php:218
msgid "Enable automatic attendee registration for all occurrences"
msgstr ""

#: public/dashboard/integrations/fooevents.php:219
msgid "Registrations"
msgstr ""

#: public/dashboard/integrations/fooevents.php:220
msgid "Link the event to these meetings/webinars:"
msgstr ""

#: public/dashboard/integrations/fooevents.php:221
msgid "Show details"
msgstr ""

#: public/dashboard/integrations/fooevents.php:222
msgid "Hide details"
msgstr ""

#: public/dashboard/integrations/fooevents.php:223
msgid "This is not a recurring meeting"
msgstr ""

#: public/dashboard/integrations/fooevents.php:224
msgid "This is not a recurring webinar"
msgstr ""

#: public/dashboard/integrations/fooevents.php:225
msgid ""
"This meeting's recurrence is currently set to 'No Fixed Time' which does not "
"allow attendees to pre-register in advance. Please change the setting for "
"this meeting to have a fixed recurrence (daily/weekly/monthly) in your Zoom "
"account before proceeding."
msgstr ""

#: public/dashboard/integrations/fooevents.php:226
msgid ""
"This webinar's recurrence is currently set to 'No Fixed Time' which does not "
"allow attendees to pre-register in advance. Please change the setting for "
"this webinar to have a fixed recurrence (daily/weekly/monthly) in your Zoom "
"account before proceeding."
msgstr ""

#: public/dashboard/integrations/fooevents.php:227
msgid "Edit meeting"
msgstr ""

#: public/dashboard/integrations/fooevents.php:228
msgid "Edit webinar"
msgstr ""

#: public/dashboard/integrations/fooevents.php:229
msgid "Single"
msgstr ""

#: public/dashboard/integrations/fooevents.php:230
msgid "Sequential days"
msgstr ""

#: public/dashboard/integrations/fooevents.php:231
msgid "Select days"
msgstr ""

#: public/dashboard/integrations/fooevents.php:232
msgid "Bookable"
msgstr ""

#: public/dashboard/integrations/fooevents.php:233
msgid "Seating"
msgstr ""

#: public/dashboard/integrations/fooevents.php:234
msgid "Standard one-day events."
msgstr ""

#: public/dashboard/integrations/fooevents.php:235
msgid ""
"Events that occur over multiple days and repeat for a set number of "
"sequential days."
msgstr ""

#: public/dashboard/integrations/fooevents.php:236
msgid "Events that repeat over multiple calendar days."
msgstr ""

#: public/dashboard/integrations/fooevents.php:237
msgid ""
"Events that require customers to select from available date and time slots "
"(bookings and repeat events)."
msgstr ""

#: public/dashboard/integrations/fooevents.php:238
msgid ""
"Events that include the ability for customers to select row and seat numbers "
"from a seating chart."
msgstr ""

#: public/dashboard/integrations/fooevents.php:239
msgid "Refresh Example Info"
msgstr ""

#: public/dashboard/integrations/fooevents.php:241
msgid "hour"
msgstr ""

#: public/dashboard/integrations/fooevents.php:243
msgid "minute"
msgstr ""

#: public/dashboard/integrations/fooevents.php:244
msgid "Daily"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:51
msgid "You must have at least one file."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:116
msgid "Thank you for your business, voucher code submitted successfully."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:117
msgid "Please enter redeem amount."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:118
msgid "Redeem amount should not be greater than redeemable amount."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:131
#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:149
msgid "Please enter valid url (i.e. http://www.example.com)."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:132
msgid "Please enter valid Email ID"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:133
msgid "Mail sent successfully"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:150
msgid "Please enter Number of Voucher Codes."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:151
msgid "Please enter Pattern to import voucher code(s)."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:152
msgid "Please enter only Numeric values in Number of Voucher Codes."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:153
msgid "Please enter Valid Pattern to import voucher code(s)."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:154
msgid "Please upload csv file."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:155
msgid "File can not be empty, please upload valid file."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:159
msgid "You can't leave this empty."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:162
msgid "WPWeb"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:168
msgid ""
"Please either enter quantity for \"Stock quantity\" or untick \"Manage stock?"
"\" option."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:169
msgid "Please select atleast one delivery method."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:180
msgid "Voucher Builder is On"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:181
msgid "Voucher Builder is Off"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:182
msgid ""
"Default WordPress editor has some content, switching to the Voucher will "
"remove it."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:183
#: public/dashboard/marketking-dashboard-login.php:195
msgid "Save"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:186
msgid "Add More"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:191
#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:192
msgid "Voucher Code"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:197
msgid "Redeem Instruction"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:202
msgid "Voucher Site Logo"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:207
msgid "Voucher Logo"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:212
msgid "Expire Date"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:213
msgid "Expire:"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:217
msgid "Vendor's Address"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:222
msgid "Website URL"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:227
msgid "Voucher Locations"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:232
#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:233
msgid "Blank Block"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:237
#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:238
msgid "Custom Block"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:249
msgid "QR Codes"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:255
msgid "Barcode"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:261
msgid "Barcodes"
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:267
msgid "Please enter valid number."
msgstr ""

#: public/dashboard/integrations/woo_vou_pdf_vouchers.php:272
msgid "Product Image"
msgstr ""

#: public/dashboard/manage-order.php:47
msgid "Order Details"
msgstr ""

#: public/dashboard/manage-order.php:62
msgid "Customer:"
msgstr ""

#: public/dashboard/manage-order.php:65 public/dashboard/manage-order.php:123
#: public/templates/invoices/commission-invoice.php:112
msgid "Date:"
msgstr ""

#: public/dashboard/manage-order.php:83
msgid "Update Order"
msgstr ""

#: public/dashboard/manage-order.php:88
msgid "Back"
msgstr ""

#: public/dashboard/manage-order.php:100
msgid "The order has been updated successfully"
msgstr ""

#: public/dashboard/manage-order.php:112
msgid "Order Information"
msgstr ""

#: public/dashboard/manage-order.php:120
msgid "General"
msgstr ""

#: public/dashboard/manage-order.php:122
msgid "Payment via:"
msgstr ""

#: public/dashboard/manage-order.php:163
msgid "On hold"
msgstr ""

#: public/dashboard/manage-order.php:204
msgid "Pending payment"
msgstr ""

#: public/dashboard/manage-order.php:245
msgid "The customer has marked this order as received."
msgstr ""

#: public/dashboard/manage-order.php:257
msgid "Billing"
msgstr ""

#: public/dashboard/manage-order.php:289
msgid "Customer provided note:"
msgstr ""

#: public/dashboard/manage-order.php:308
msgid "Order Items"
msgstr ""

#: public/dashboard/manage-order.php:322
msgid "Downloadable Product Permissions"
msgstr ""

#: public/dashboard/manage-order.php:341
msgid "Custom Order Fields"
msgstr ""

#: public/dashboard/manage-order.php:368
msgid "Related Orders"
msgstr ""

#: public/dashboard/manage-order.php:406
#: public/templates/email-order-details.php:50
#: public/templates/order-details.php:55
msgid "Order Totals"
msgstr ""

#: public/dashboard/manage-order.php:413
msgid "Order Value"
msgstr ""

#: public/dashboard/manage-order.php:428
msgid "Your Earnings"
msgstr ""

#: public/dashboard/manage-order.php:557
msgid "Invoice & Packing"
msgstr ""

#: public/dashboard/manage-order.php:559
#: public/dashboard/marketking-dashboard-login.php:188
msgid "Invoice"
msgstr ""

#: public/dashboard/manage-order.php:578
msgid "Commission invoice"
msgstr ""

#: public/dashboard/manage-order.php:602
msgid "Packing slip (PDF)"
msgstr ""

#: public/dashboard/manage-order.php:632
msgid "Document details"
msgstr ""

#: public/dashboard/manage-order.php:654
msgid "Print/Download"
msgstr ""

#: public/dashboard/manage-order.php:707
msgid "Order Notes"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:180
msgid "Settings updated."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:181
msgid "All fields are mandatory"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:182
#, php-format
msgid ""
"Unable to update settings due to an internal error. %s To troubleshoot "
"please click %s here. %s"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:183
msgid "You have to select order(s) first!"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:184
msgid "One or more order do not have invoice generated. Generate manually?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:185
#: public/dashboard/marketking-dashboard-login.php:1848
msgid "Error"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:186
msgid "Please wait"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:187
msgid "is required"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:189
msgid ""
"number has not been generated yet. Do you want to manually generate one ?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:190
msgid ""
"‘Generate invoice for free orders’ is disabled in Invoice settings > "
"Advanced. You are attempting to generate invoice for this free order. "
"Proceed?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:191
msgid ""
"Refund in this order seems not having credit number yet. Do you want to "
"manually generate one ?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:192
msgid "Please fill the `from address` in the plugin's general settings."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:193
msgid "Code Copied"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:194
msgid "Close"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:196
msgid "Please enter mandatory fields"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:197
msgid "You can add more than 1 order meta in"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:199
msgid "You can edit an existing item by using its key."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:253
msgid ""
"You can add only simple products with the FREE version of YITH WooCommerce "
"Product Bundles"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:254
msgid "You cannot add a bundle product"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:264
#: public/dashboard/marketking-dashboard-login.php:265
#, php-format
msgid "1 item added"
msgid_plural "%s items added"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Lowest required qty value.
#: public/dashboard/marketking-dashboard-login.php:346
#, php-format
msgid "Please enter an integer higher than %s."
msgstr ""

#. translators: %s: Highest allowed qty value.
#: public/dashboard/marketking-dashboard-login.php:348
#, php-format
msgid "Please enter an integer lower than or equal to %s."
msgstr ""

#. translators: %s: Required step qty value.
#: public/dashboard/marketking-dashboard-login.php:350
#, php-format
msgid "Please enter an integer that is a multiple of %s."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:377
#: public/dashboard/payouts.php:216
msgid "Configure"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:379
msgid ""
"Failed to initialize form. If this issue persists, please reload the page "
"and try again."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:380
msgid ""
"Failed to validate configuration. If this issue persists, please reload the "
"page and try again."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:474
msgid "Product is not selected or email body is empty. Please fill it."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:617
msgid "Back to all"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:696
msgid "No row selected"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:697
msgid "Product ID"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:698
msgid "Country Code"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:699
msgid "State/County Code"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:700
msgid "Zip/Postal Code"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:701
msgid "Cost"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:702
msgid "Item Cost"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:809
msgid "Insert delivery time name, slug or id."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:810
msgid "Insert product units amount."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:880
msgid "No data available in table"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:881
msgid "Showing _START_ to _END_ of _TOTAL_ entries"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:882
msgid "Showing 0 to 0 of 0 entries"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:883
msgid "(filtered from _MAX_ total entries)"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:884
msgid "Show _MENU_ entries"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:885
msgid "Loading..."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:886
msgid "Processing..."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:887
msgid "Search:"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:888
msgid "No matching records found"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:890
msgid "First"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:891
msgid "Last"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:892
msgid "Next"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:893
msgid "Previous"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:896
msgid ": activate to sort column ascending"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:897
msgid ": activate to sort column descending"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1012
msgid "Resources"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1021
msgid "Calendar"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1033
#: public/dashboard/templates/profile-sidebar.php:194
msgid "Google Calendar Integration"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1242
msgid "Inherit from product"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1243
msgid "Inherit from chosen variation"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1244
msgid "Override product"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1245
msgid "Override all variations"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1246
msgid "Discount to apply to the product when this plan is selected."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1247
msgid "Discount to apply to the chosen variation when this plan is selected."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1331
msgid ""
"Trashing this order will also trash the subscriptions purchased with the "
"order."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1342
msgid "The trial period cannot exceed the maximum"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1343
msgid "Enter the new period, either day, week, month or year:"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1344
msgid "Enter a new length (e.g. 5):"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1345
msgid ""
"Enter a new interval as a single number (e.g. to charge every 2nd month, "
"enter 2):"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1346
msgid "Delete all variations without a subscription"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1349
msgid ""
"The product type can not be changed because this product is associated with "
"subscriptions."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1352
msgid ""
"An error occurred determining if that variation can be deleted. Please try "
"again."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1353
msgid ""
"That variation can not be removed because it is associated with active "
"subscriptions. To remove this variation, please cancel and delete the "
"subscriptions for it."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1354
msgid ""
"You are about to trash one or more orders which contain a subscription.\n"
"\n"
"Trashing the orders will also trash the subscriptions purchased with these "
"orders."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1356
msgid ""
"WARNING: Bad things are about to happen!\n"
"\n"
"The payment gateway used to purchase this subscription does not support "
"modifying a subscription's details.\n"
"\n"
"Changes to the billing period, recurring discount, recurring tax or "
"recurring total may not be reflected in the amount charged by the payment "
"gateway."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1357
msgid ""
"You are deleting a subscription item. You will also need to manually cancel "
"and trash the subscription on the Manage Subscriptions screen."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1392
msgid "Please enter a start date in the past."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1393
msgid "Please enter a date at least 2 minutes into the future."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1393
msgid "Please enter a date at least one hour into the future."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1394
msgid "Please enter a date after the trial end."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1395
#: public/dashboard/marketking-dashboard-login.php:1396
msgid "Please enter a date after the start date."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1397
msgid "Please enter a date before the next payment."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1398
msgid "Please enter a date after the next payment."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1399
msgid ""
"Are you sure you want to process a renewal?\n"
"\n"
"This will charge the customer and email them the renewal order (if emails "
"are enabled)."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1412
msgid ""
"Are you sure you want to retry payment for this renewal order?\n"
"\n"
"This will attempt to charge the customer and send renewal order emails (if "
"emails are enabled)."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1466
#: public/dashboard/marketking-dashboard-login.php:1507
msgid "Are you sure you want to delete this zone? This action cannot be undone"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1467
#: public/dashboard/marketking-dashboard-login.php:1508
msgid "Zone"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1484
#: public/dashboard/marketking-dashboard-login.php:1527
#, php-format
msgid ""
"Your server does not include the customer IP in HTTP_X_FORWARDED_FOR. Fix it "
"by adding %s to your config.php."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1485
#: public/dashboard/marketking-dashboard-login.php:1528
msgid ""
"The first IP not empty of your server variables does not match with your "
"real IP."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1486
#: public/dashboard/marketking-dashboard-login.php:1529
msgid "The MaxMind GeoIP database is required."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1651
msgid "Not available in your browser"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1652
msgid "Model not found!"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1653
msgid "Please enable Preview Model in the settings of the plugin"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1654
msgid "Please upload the model first"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1655
msgid "WEBM rendering works only in Chrome browser"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1656
msgid "Please don't switch to other tabs while rendering"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1657
msgid ""
"The amount of data we are going to submit is larger than post_max_size in "
"php.ini ("
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1658
msgid "Repairing.."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1659
msgid "Repairing.. done!"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1660
msgid "Error report:"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1661
msgid "Repairing.. fail!"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1662
msgid "No errors found."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1663
msgid "Degenerate facets"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1664
msgid "Edges fixed"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1665
msgid "Facets removed"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1666
msgid "Facets added"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1667
msgid "Facets reversed"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1668
msgid "Backwards edges"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1670
msgid "Can not repair textured models yet!"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1671
msgid "Can repair only STL and OBJ models"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1672
msgid ""
"The model will be sent to our server for repair.\n"
"Repairing some models with very faulty geometries may result in broken "
"models.\n"
"Click OK if you agree."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1673
msgid "Reducing.."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1674
msgid "Reducing.. done!"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1675
msgid "No reduction needed"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1676
#, no-php-format
msgid "% of triangles to reduce"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1677
msgid "Can not reduce textured models yet!"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1678
msgid "Can reduce only STL and OBJ models"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1679
msgid ""
"The model will be sent to our server for polygon reduction.\n"
" Click OK if you agree."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1842
msgid "There are no saved variations yet."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1843
msgid "You cannot add any more extra options."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1844
msgid "Are you sure you want to remove this option?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1845
msgid ""
"Before adding Extra Product Options, add and save some attributes on the "
"Attributes tab."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1846
msgid "Fixed amount"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1847
msgid "Percent of the original price"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1883
msgid "Clear"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1884
msgid "Default"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1885
msgid "Select Color"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1886
msgid "Current Color"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1902
msgid "Delete this group?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1903
msgid ""
"Delete this field? Deleting this field will also delete any conditions "
"associated with it."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1904
msgid "Delete this option?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1905
msgid "Checked"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1906
msgid ""
"This field is used in a condition. Changing its field type may affect the "
"condition. Continue?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1907
msgid "copy"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1908
msgid " -- Select a field -- "
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1954
msgid ""
"All fields must have a title and/or option name. Please review the settings "
"highlighted in red border."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1955
msgid "Limit price range"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1956
msgid "Limit quantity range"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1957
msgid "Limit character length"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1958
msgid "Restrictions"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1959
msgid "Are you sure you want remove this add-on field?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1960
msgid "Are you sure you want delete this option?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1961
msgid "Add Image Swatch"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:1962
msgid "Add Image"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2023
msgid "Cannot edit price while a pricing table is active"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2048
msgid "One or more emails entered appear to be invalid"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2049
#: public/dashboard/products.php:483
msgid "Add"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2050
msgid "No users selected"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2051
msgid "No action selected"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2052
msgid "View User Profile"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2053
msgid "Go"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2054
msgid "Update Options"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2055
msgid ""
"Waitlists may be appear inaccurate due to an update to variations. Please "
"update the product or refresh the page to update waitlists"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2121
msgid "Do you really want to delete this request?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2326
msgid "Invalid Account"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2327
msgid ""
"Your current account is not a vendor or has been deactivated. To login as a "
"vendor, please logout first. "
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2335
msgid "Log out"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2390
msgid "Username is empty"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2393
msgid "Password is empty"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2396
msgid "Incorrect user or password"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2402
msgid "Login failed"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2409
msgid "Sign-In"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2411
msgid "Access your vendor dashboard and data."
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2421
msgid "Email or Username"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2423
msgid "Enter your email address or username"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2428
msgid "Forgot password?"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2435
msgid "Enter your password"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2441
msgid "Sign in"
msgstr ""

#: public/dashboard/marketking-dashboard-login.php:2448
msgid "New on our platform?"
msgstr ""

#: public/dashboard/marketking-dashboard.php:334
msgid "Connecting to Stripe, please wait..."
msgstr ""

#: public/dashboard/orders.php:28
msgid "Here you can view and manage all orders assigned to you."
msgstr ""

#: public/dashboard/orders.php:62
msgid "Search orders..."
msgstr ""

#: public/dashboard/orders.php:75
msgid "Order"
msgstr ""

#: public/dashboard/orders.php:80
msgid "Purchased"
msgstr ""

#: public/dashboard/orders.php:81
msgid "Order Total"
msgstr ""

#: public/dashboard/orders.php:99
msgid "order"
msgstr ""

#: public/dashboard/orders.php:100 public/dashboard/products.php:198
msgid "date"
msgstr ""

#: public/dashboard/orders.php:101 public/dashboard/products.php:196
msgid "status"
msgstr ""

#: public/dashboard/orders.php:103
msgid "customer"
msgstr ""

#: public/dashboard/orders.php:104
msgid "purchased"
msgstr ""

#: public/dashboard/orders.php:105
msgid "order total"
msgstr ""

#: public/dashboard/orders.php:106
msgid "earnings"
msgstr ""

#: public/dashboard/orders.php:111
msgid "actions"
msgstr ""

#: public/dashboard/payouts.php:109
msgid "You have successfully connected your Stripe account."
msgstr ""

#: public/dashboard/payouts.php:134
msgid "View and keep track of your payouts."
msgstr ""

#: public/dashboard/payouts.php:144
msgid "Available for Payout"
msgstr ""

#: public/dashboard/payouts.php:155
msgid ""
"This is the amount you currently have in earnings, available for your next "
"payout."
msgstr ""

#: public/dashboard/payouts.php:161
msgid "Payout Account"
msgstr ""

#: public/dashboard/payouts.php:167
msgid "Withdrawal request pending"
msgstr ""

#: public/dashboard/payouts.php:187
msgid "Set payout account"
msgstr ""

#: public/dashboard/payouts.php:199
msgid "selected and connected"
msgstr ""

#: public/dashboard/payouts.php:206
msgid "currently selected"
msgstr ""

#: public/dashboard/payouts.php:210
msgid "not yet configured"
msgstr ""

#: public/dashboard/payouts.php:224
msgid "Withdraw"
msgstr ""

#: public/dashboard/payouts.php:240
msgid "Recent Payouts"
msgstr ""

#: public/dashboard/payouts.php:248
msgid "Date Processed"
msgstr ""

#: public/dashboard/payouts.php:249
#: public/templates/invoices/commission-invoice.php:195
msgid "Notes"
msgstr ""

#: public/dashboard/payouts.php:316
msgid "Make Withdrawal Request"
msgstr ""

#: public/dashboard/payouts.php:327
msgid "You must configure a payment method before making a withdrawal."
msgstr ""

#: public/dashboard/payouts.php:329
msgid "Your balance is below the minimum withdrawal threshold: "
msgstr ""

#: public/dashboard/payouts.php:335
msgid "You already have an active request for "
msgstr ""

#: public/dashboard/payouts.php:346
msgid "Withdrawal Amount"
msgstr ""

#: public/dashboard/payouts.php:348
msgid "Available for withdrawal:"
msgstr ""

#: public/dashboard/payouts.php:351
msgid "Enter your withdrawal amount here..."
msgstr ""

#: public/dashboard/payouts.php:361
msgid "Make Request"
msgstr ""

#: public/dashboard/payouts.php:363
msgid "Cancel Current Request"
msgstr ""

#: public/dashboard/payouts.php:372
msgid "Select Max"
msgstr ""

#: public/dashboard/payouts.php:393
msgid "Set Payout Method"
msgstr ""

#: public/dashboard/payouts.php:400
msgid "Select a payment method:"
msgstr ""

#: public/dashboard/payouts.php:427
msgid "PayPal"
msgstr ""

#: public/dashboard/payouts.php:443 public/dashboard/payouts.php:648
#: public/dashboard/payouts.php:706
msgid "Stripe"
msgstr ""

#: public/dashboard/payouts.php:459
msgid "Bank Transfer"
msgstr ""

#: public/dashboard/payouts.php:496
msgid "PayPal Email Address"
msgstr ""

#: public/dashboard/payouts.php:498
msgid "Enter your PayPal email address here..."
msgstr ""

#: public/dashboard/payouts.php:506
msgid "Personal / Business Details"
msgstr ""

#: public/dashboard/payouts.php:550
msgid "Bank / Wire Transfer Details"
msgstr ""

#: public/dashboard/payouts.php:552
msgid "Account Holder Name"
msgstr ""

#: public/dashboard/payouts.php:564
msgid "BIC / SWIFT"
msgstr ""

#: public/dashboard/payouts.php:651
msgid "You are connected with Stripe"
msgstr ""

#: public/dashboard/payouts.php:657
msgid "Disconnect Stripe Account"
msgstr ""

#: public/dashboard/payouts.php:708
msgid "You are not connected with Stripe."
msgstr ""

#: public/dashboard/payouts.php:731
msgid "Save Info"
msgstr ""

#: public/dashboard/products.php:49
msgid "Search products..."
msgstr ""

#: public/dashboard/products.php:59
msgid "Pending Review"
msgstr ""

#: public/dashboard/products.php:74
msgid "Add Product"
msgstr ""

#: public/dashboard/products.php:80
msgid "Add Product (Max Limit Reached)"
msgstr ""

#: public/dashboard/products.php:142 public/dashboard/products.php:181
msgid "SKU"
msgstr ""

#: public/dashboard/products.php:153 public/templates/cart.php:95
#: public/templates/cart.php:170 public/templates/email-order-details.php:98
#: public/templates/invoices/commission-invoice.php:128
msgid "Price"
msgstr ""

#: public/dashboard/products.php:154
msgid "Stock"
msgstr ""

#: public/dashboard/products.php:159
msgid "Date Created"
msgstr ""

#: public/dashboard/products.php:165
msgid "Languages"
msgstr ""

#: public/dashboard/products.php:180
msgid "name"
msgstr ""

#: public/dashboard/products.php:192
msgid "price"
msgstr ""

#: public/dashboard/products.php:193
msgid "stock"
msgstr ""

#: public/dashboard/products.php:194
msgid "categories"
msgstr ""

#: public/dashboard/products.php:197
msgid "tags"
msgstr ""

#: public/dashboard/products.php:204
msgid "languages"
msgstr ""

#: public/dashboard/products.php:439
msgid "Hidden"
msgstr ""

#: public/dashboard/products.php:442 public/dashboard/products.php:445
msgid "On Backorder"
msgstr ""

#: public/dashboard/products.php:532
msgid "Copy URL"
msgstr ""

#: public/dashboard/profile-settings.php:25 public/dashboard/profile.php:32
msgid "Your settings have been saved successfully"
msgstr ""

#: public/dashboard/profile-settings.php:39
#: public/dashboard/templates/profile-sidebar.php:175
msgid "Profile Settings"
msgstr ""

#: public/dashboard/profile-settings.php:43 public/dashboard/profile.php:52
msgid "Menu"
msgstr ""

#: public/dashboard/profile-settings.php:56
msgid "Email Settings"
msgstr ""

#: public/dashboard/profile-settings.php:57
msgid "Choose which email notifications you would like to receive."
msgstr ""

#: public/dashboard/profile-settings.php:99
msgid "Email me when new announcements are published."
msgstr ""

#: public/dashboard/profile-settings.php:105
msgid "Email me when I receive a new message."
msgstr ""

#: public/dashboard/profile-settings.php:114
msgid "Email me when I receive a new refund request."
msgstr ""

#: public/dashboard/profile-settings.php:124
msgid "Email me when I receive a new rating (review)."
msgstr ""

#: public/dashboard/profile-settings.php:146
msgid "Dashboard Settings"
msgstr ""

#: public/dashboard/profile-settings.php:147
msgid "Control how your vendor dashboard works."
msgstr ""

#: public/dashboard/profile-settings.php:155
msgid ""
"Load dashboard tables with AJAX (only enable this if you have a large nr. of "
"products / orders)."
msgstr ""

#: public/dashboard/profile.php:46
#: public/dashboard/templates/profile-sidebar.php:68
msgid "Store Information"
msgstr ""

#: public/dashboard/profile.php:48
msgid "Your store information & data."
msgstr ""

#: public/dashboard/profile.php:59 public/dashboard/profile.php:173
msgid "Store Info"
msgstr ""

#: public/dashboard/profile.php:84 public/dashboard/profile.php:174
msgid "Address"
msgstr ""

#: public/dashboard/profile.php:105 public/dashboard/profile.php:229
msgid "Phone"
msgstr ""

#: public/dashboard/profile.php:112 public/dashboard/profile.php:175
#: public/dashboard/profile.php:379
msgid "About Us"
msgstr ""

#: public/dashboard/profile.php:170
msgid "Update Settings"
msgstr ""

#: public/dashboard/profile.php:176
msgid "Images"
msgstr ""

#: public/dashboard/profile.php:185
msgid "Enter your first name..."
msgstr ""

#: public/dashboard/profile.php:191
msgid "Enter your last name..."
msgstr ""

#: public/dashboard/profile.php:197
msgid "Enter your company name..."
msgstr ""

#: public/dashboard/profile.php:203
msgid "Enter your store name..."
msgstr ""

#: public/dashboard/profile.php:211
msgid "Enter your email..."
msgstr ""

#: public/dashboard/profile.php:220
msgid "Show email on store page"
msgstr ""

#: public/dashboard/profile.php:230
msgid "Enter your phone number..."
msgstr ""

#: public/dashboard/profile.php:239
msgid "Show phone on store page"
msgstr ""

#: public/dashboard/profile.php:250 public/dashboard/profile.php:364
#: public/dashboard/profile.php:416
msgid "Update Profile"
msgstr ""

#: public/dashboard/profile.php:291
msgid "Click to select a profile picture."
msgstr ""

#: public/dashboard/profile.php:312
msgid "Save Images"
msgstr ""

#: public/dashboard/profile.php:325
msgid "Address 1"
msgstr ""

#: public/dashboard/profile.php:331
msgid "Address 2"
msgstr ""

#: public/dashboard/profile.php:343
msgid "Postcode / ZIP"
msgstr ""

#: public/dashboard/profile.php:349
msgid "Country and State"
msgstr ""

#: public/dashboard/profile.php:379
msgid ""
"Enter a description / about us here. Selected HTML tags can be used to "
"format the above text: h2, h3, h4, i, strong"
msgstr ""

#: public/dashboard/templates/profile-sidebar.php:55
msgid "Member since "
msgstr ""

#: public/dashboard/templates/profile-sidebar.php:109
msgid "Vacation"
msgstr ""

#: public/dashboard/templates/profile-sidebar.php:128
msgid "Store Policies"
msgstr ""

#: public/dashboard/templates/profile-sidebar.php:147
msgid "SEO Settings"
msgstr ""

#: public/dashboard/templates/profile-sidebar.php:157
msgid "Social Profiles"
msgstr ""

#: public/dashboard/templates/profile-sidebar.php:167
msgid "Verification"
msgstr ""

#: public/dashboard/templates/sidebar.php:55
#: public/dashboard/templates/sidebar.php:72
#: public/dashboard/templates/sidebar.php:104
#: public/dashboard/templates/sidebar.php:165
#: public/dashboard/templates/sidebar.php:195
msgid " New"
msgstr ""

#: public/dashboard/templates/sidebar.php:119
msgid "Coupons"
msgstr ""

#: public/dashboard/templates/sidebar.php:147
msgid "Subscriptions"
msgstr ""

#: public/dashboard/templates/sidebar.php:215
msgid "Offers"
msgstr ""

#: public/dashboard/templates/sidebar.php:228
msgid "B2B Messages"
msgstr ""

#: public/dashboard/templates/sidebar.php:241
msgid "Dynamic Rules"
msgstr ""

#: public/dashboard/templates/sidebar.php:258
msgid "My Team"
msgstr ""

#: public/dashboard/templates/sidebar.php:334
msgid " Open"
msgstr ""

#: public/dashboard/templates/sidebar.php:345
msgid "Warranty"
msgstr ""

#: public/dashboard/templates/sidebar.php:358
msgid "Docs"
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:14
msgid "New Announcement"
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:17
msgid ""
"This email is sent when a new announcement is released to vendors "
"(marketking)."
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:20
#: public/emails/class-marketking-new-announcement-email.php:21
#: public/emails/class-marketking-new-announcement-email.php:47
#: public/emails/class-marketking-new-announcement-email.php:48
msgid "New announcement"
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:101
#: public/emails/class-marketking-new-message-email.php:123
#: public/emails/class-marketking-new-payout-email.php:97
#: public/emails/class-marketking-new-product-requires-approval-email.php:90
#: public/emails/class-marketking-new-rating-email.php:96
#: public/emails/class-marketking-new-refund-email.php:97
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:98
#: public/emails/class-marketking-new-verification-email.php:99
#: public/emails/class-marketking-product-has-been-approved-email.php:96
#: public/emails/class-marketking-your-account-approved-email.php:100
msgid "Enable/Disable"
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:103
#: public/emails/class-marketking-new-message-email.php:125
#: public/emails/class-marketking-new-payout-email.php:99
#: public/emails/class-marketking-new-product-requires-approval-email.php:92
#: public/emails/class-marketking-new-rating-email.php:98
#: public/emails/class-marketking-new-refund-email.php:99
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:100
#: public/emails/class-marketking-new-verification-email.php:101
#: public/emails/class-marketking-product-has-been-approved-email.php:98
#: public/emails/class-marketking-your-account-approved-email.php:102
msgid "Enable this email notification"
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:109
#: public/emails/class-marketking-new-message-email.php:131
#: public/emails/class-marketking-new-payout-email.php:105
#: public/emails/class-marketking-new-product-requires-approval-email.php:105
#: public/emails/class-marketking-new-rating-email.php:104
#: public/emails/class-marketking-new-refund-email.php:105
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:113
#: public/emails/class-marketking-new-verification-email.php:107
#: public/emails/class-marketking-product-has-been-approved-email.php:104
#: public/emails/class-marketking-your-account-approved-email.php:108
msgid ""
"This controls the email subject line. Leave blank to use the default "
"subject: "
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:114
#: public/emails/class-marketking-new-message-email.php:136
#: public/emails/class-marketking-new-payout-email.php:110
#: public/emails/class-marketking-new-product-requires-approval-email.php:110
#: public/emails/class-marketking-new-rating-email.php:109
#: public/emails/class-marketking-new-refund-email.php:110
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:118
#: public/emails/class-marketking-new-verification-email.php:112
#: public/emails/class-marketking-product-has-been-approved-email.php:109
#: public/emails/class-marketking-your-account-approved-email.php:113
msgid "Email Heading"
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:116
#: public/emails/class-marketking-new-message-email.php:138
#: public/emails/class-marketking-new-payout-email.php:112
#: public/emails/class-marketking-new-product-requires-approval-email.php:112
#: public/emails/class-marketking-new-rating-email.php:111
#: public/emails/class-marketking-new-refund-email.php:112
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:120
#: public/emails/class-marketking-new-verification-email.php:114
#: public/emails/class-marketking-product-has-been-approved-email.php:111
#: public/emails/class-marketking-your-account-approved-email.php:115
msgid ""
"This controls the main heading contained within the email notification. "
"Leave blank to use the default heading: "
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:121
#: public/emails/class-marketking-new-message-email.php:143
#: public/emails/class-marketking-new-payout-email.php:117
#: public/emails/class-marketking-new-product-requires-approval-email.php:117
#: public/emails/class-marketking-new-rating-email.php:116
#: public/emails/class-marketking-new-refund-email.php:117
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:125
#: public/emails/class-marketking-new-verification-email.php:119
#: public/emails/class-marketking-product-has-been-approved-email.php:116
#: public/emails/class-marketking-your-account-approved-email.php:120
msgid "Email type"
msgstr ""

#: public/emails/class-marketking-new-announcement-email.php:123
#: public/emails/class-marketking-new-message-email.php:145
#: public/emails/class-marketking-new-payout-email.php:119
#: public/emails/class-marketking-new-product-requires-approval-email.php:119
#: public/emails/class-marketking-new-rating-email.php:118
#: public/emails/class-marketking-new-refund-email.php:119
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:127
#: public/emails/class-marketking-new-verification-email.php:121
#: public/emails/class-marketking-product-has-been-approved-email.php:118
#: public/emails/class-marketking-your-account-approved-email.php:122
msgid "Choose which format of email to send."
msgstr ""

#: public/emails/class-marketking-new-message-email.php:14
msgid "New Message"
msgstr ""

#: public/emails/class-marketking-new-message-email.php:17
msgid "This email is sent when a new message is sent (marketking)"
msgstr ""

#: public/emails/class-marketking-new-payout-email.php:14
#: public/emails/class-marketking-new-payout-email.php:20
#: public/emails/class-marketking-new-payout-email.php:21
#: public/emails/class-marketking-new-payout-email.php:47
#: public/emails/class-marketking-new-payout-email.php:48
msgid "New Payout"
msgstr ""

#: public/emails/class-marketking-new-payout-email.php:17
msgid "This email is sent when a payout is saved (marketking)"
msgstr ""

#: public/emails/class-marketking-new-product-requires-approval-email.php:14
#: public/emails/class-marketking-new-product-requires-approval-email.php:20
#: public/emails/class-marketking-new-product-requires-approval-email.php:21
msgid "New product requires approval"
msgstr ""

#: public/emails/class-marketking-new-product-requires-approval-email.php:17
msgid ""
"This email is sent to admin when a vendor submits a product which requires "
"manual approval"
msgstr ""

#: public/emails/class-marketking-new-product-requires-approval-email.php:96
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:104
msgid "Recipient(s)"
msgstr ""

#: public/emails/class-marketking-new-product-requires-approval-email.php:98
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:106
msgid "Enter recipients (comma separated) for this email. Defaults to"
msgstr ""

#: public/emails/class-marketking-new-product-requires-approval-email.php:129
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:137
#: public/emails/class-marketking-product-has-been-approved-email.php:128
#: public/emails/class-marketking-your-account-approved-email.php:132
msgid "Additional content"
msgstr ""

#: public/emails/class-marketking-new-product-requires-approval-email.php:130
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:138
#: public/emails/class-marketking-product-has-been-approved-email.php:129
#: public/emails/class-marketking-your-account-approved-email.php:133
msgid "Text to appear below the main email content."
msgstr ""

#: public/emails/class-marketking-new-product-requires-approval-email.php:132
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:140
#: public/emails/class-marketking-product-has-been-approved-email.php:131
#: public/emails/class-marketking-your-account-approved-email.php:135
msgid "N/A"
msgstr ""

#: public/emails/class-marketking-new-rating-email.php:14
#: public/emails/class-marketking-new-rating-email.php:20
#: public/emails/class-marketking-new-rating-email.php:21
#: public/emails/class-marketking-new-rating-email.php:47
#: public/emails/class-marketking-new-rating-email.php:48
msgid "New Rating"
msgstr ""

#: public/emails/class-marketking-new-rating-email.php:17
msgid "This email is sent when a new rating is received (marketking)"
msgstr ""

#: public/emails/class-marketking-new-refund-email.php:14
#: public/emails/class-marketking-new-refund-email.php:20
#: public/emails/class-marketking-new-refund-email.php:21
#: public/emails/class-marketking-new-refund-email.php:48
#: public/emails/class-marketking-new-refund-email.php:49
msgid "New Refund Request"
msgstr ""

#: public/emails/class-marketking-new-refund-email.php:17
msgid ""
"This email is sent to vendors when a new refund request is received "
"(marketking)"
msgstr ""

#: public/emails/class-marketking-new-vendor-requires-approval-email.php:14
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:20
#: public/emails/class-marketking-new-vendor-requires-approval-email.php:21
msgid "New vendor requires approval"
msgstr ""

#: public/emails/class-marketking-new-vendor-requires-approval-email.php:17
msgid ""
"This email is sent to admin when a new vendor registers and requires manual "
"approval"
msgstr ""

#: public/emails/class-marketking-new-verification-email.php:14
#: public/emails/class-marketking-new-verification-email.php:48
#: public/emails/class-marketking-new-verification-email.php:49
msgid "New Verification Status"
msgstr ""

#: public/emails/class-marketking-new-verification-email.php:17
msgid ""
"This email is sent when a vendor verification request is approved or "
"rejected (marketking)"
msgstr ""

#: public/emails/class-marketking-new-verification-email.php:20
#: public/emails/class-marketking-new-verification-email.php:21
msgid "New Verification"
msgstr ""

#: public/emails/class-marketking-product-has-been-approved-email.php:14
#: public/emails/class-marketking-product-has-been-approved-email.php:20
#: public/emails/class-marketking-product-has-been-approved-email.php:21
msgid "Your product has been approved"
msgstr ""

#: public/emails/class-marketking-product-has-been-approved-email.php:17
msgid ""
"This email is sent to the vendor when the admin approves one of their "
"products"
msgstr ""

#: public/emails/class-marketking-your-account-approved-email.php:14
msgid "Vendor account approved"
msgstr ""

#: public/emails/class-marketking-your-account-approved-email.php:20
msgid ""
"This email notifies the vendor when their account has been manually approved"
msgstr ""

#: public/emails/class-marketking-your-account-approved-email.php:23
#: public/emails/class-marketking-your-account-approved-email.php:50
msgid "Your account has been approved"
msgstr ""

#: public/emails/class-marketking-your-account-approved-email.php:24
#: public/emails/class-marketking-your-account-approved-email.php:51
msgid "Your vendor account has been approved"
msgstr ""

#: public/emails/templates/new-announcement-email-template.php:13
#: public/emails/templates/plain-new-announcement-email-template.php:9
msgid "You have a new announcement."
msgstr ""

#: public/emails/templates/new-announcement-email-template.php:15
#: public/emails/templates/plain-new-announcement-email-template.php:11
#: public/emails/templates/plain-new-message-email-template.php:11
msgid "Content: "
msgstr ""

#: public/emails/templates/new-message-email-template.php:21
#: public/emails/templates/plain-new-message-email-template.php:9
msgid "You have a new message."
msgstr ""

#: public/emails/templates/new-message-email-template.php:23
msgid "Sender: "
msgstr ""

#: public/emails/templates/new-message-email-template.php:34
msgid "You have a new inquiry."
msgstr ""

#: public/emails/templates/new-message-email-template.php:36
msgid "You have a new inquiry from a guest (logged out) user."
msgstr ""

#: public/emails/templates/new-message-email-template.php:56
msgid "You have a new support request."
msgstr ""

#: public/emails/templates/new-message-email-template.php:75
msgid "You have a new withdrawal request."
msgstr ""

#: public/emails/templates/new-payout-email-template.php:12
#: public/emails/templates/plain-new-payout-email-template.php:9
msgid "Your payout has been processed! Happy Spending!"
msgstr ""

#: public/emails/templates/new-payout-email-template.php:14
#: public/emails/templates/plain-new-payout-email-template.php:11
msgid "Amount: "
msgstr ""

#: public/emails/templates/new-payout-email-template.php:18
#: public/emails/templates/plain-new-payout-email-template.php:15
msgid "Notes: "
msgstr ""

#: public/emails/templates/new-product-requires-approval-email-template.php:21
msgid "A vendor has submitted the following product for approval."
msgstr ""

#: public/emails/templates/new-product-requires-approval-email-template.php:27
msgid "Click to Review Product"
msgstr ""

#: public/emails/templates/new-rating-email-template.php:12
msgid "You have received a new rating!"
msgstr ""

#: public/emails/templates/new-rating-email-template.php:14
#: public/emails/templates/plain-new-rating-email-template.php:11
msgid "Rating: "
msgstr ""

#: public/emails/templates/new-rating-email-template.php:16
#: public/emails/templates/new-verification-email-template.php:23
#: public/emails/templates/plain-new-rating-email-template.php:13
#: public/emails/templates/plain-new-verification-email-template.php:16
msgid "Comment: "
msgstr ""

#: public/emails/templates/new-refund-email-template.php:15
msgid "You have received a new refund request."
msgstr ""

#: public/emails/templates/new-refund-email-template.php:17
msgid "Go to request: "
msgstr ""

#: public/emails/templates/new-refund-email-template.php:17
msgid "View Request"
msgstr ""

#: public/emails/templates/new-refund-email-template.php:19
#: public/emails/templates/plain-new-refund-email-template.php:11
msgid "Reason: "
msgstr ""

#: public/emails/templates/new-refund-email-template.php:21
#: public/emails/templates/plain-new-refund-email-template.php:13
msgid "User: "
msgstr ""

#: public/emails/templates/new-vendor-requires-approval-email-template.php:15
#: public/emails/templates/plain-new-product-requires-approval-email-template.php:14
#: public/emails/templates/plain-new-vendor-requires-approval-email-template.php:12
msgid "You have a new vendor registration that requires approval."
msgstr ""

#: public/emails/templates/new-vendor-requires-approval-email-template.php:17
#: public/emails/templates/plain-new-vendor-requires-approval-email-template.php:14
msgid "Username: "
msgstr ""

#: public/emails/templates/new-vendor-requires-approval-email-template.php:21
msgid "Click to Review User"
msgstr ""

#: public/emails/templates/new-verification-email-template.php:14
#: public/emails/templates/plain-new-verification-email-template.php:9
msgid "There is an update regarding your recent verification request."
msgstr ""

#: public/emails/templates/new-verification-email-template.php:16
#: public/emails/templates/plain-new-verification-email-template.php:11
msgid "Verification for: "
msgstr ""

#: public/emails/templates/new-verification-email-template.php:18
#: public/emails/templates/plain-new-verification-email-template.php:13
msgid "New Status: "
msgstr ""

#: public/emails/templates/plain-new-rating-email-template.php:9
msgid "Your have received a new rating!"
msgstr ""

#: public/emails/templates/plain-new-refund-email-template.php:9
msgid "Your have received a new refund request."
msgstr ""

#: public/emails/templates/plain-product-has-been-approved-email-template.php:12
#: public/emails/templates/product-has-been-approved-email-template.php:26
msgid "Your product has been approved."
msgstr ""

#: public/emails/templates/plain-your-account-approved-email-template.php:9
#: public/emails/templates/your-account-approved-email-template.php:12
msgid ""
"You're all set and ready to start selling! Your account has been approved."
msgstr ""

#: public/emails/templates/product-has-been-approved-email-template.php:30
msgid "Click to View Product"
msgstr ""

#. translators: %s: Customer username
#: public/emails/templates/vendor-new-account.php:8
#, php-format
msgid "Hi %s,"
msgstr ""

#. translators: %1$s: Site title, %2$s: Username, %3$s: My account link
#: public/emails/templates/vendor-new-account.php:10
#, php-format
msgid ""
"Thank you for creating a vendor account on %1$s. Your username is %2$s. You "
"can access your vendor dashboard to manage products, orders, earnings and "
"more at: %3$s"
msgstr ""

#. translators: %s: Auto generated password
#: public/emails/templates/vendor-new-account.php:13
#, php-format
msgid "Your password has been automatically generated: %s"
msgstr ""

#: public/emails/templates/your-account-approved-email-template.php:14
msgid ""
"You can access your vendor dashboard to manage products, orders, earnings "
"and more at: "
msgstr ""

#: public/templates/cart.php:39
msgid "Coupon:"
msgstr ""

#: public/templates/cart.php:39
msgid "Coupon code"
msgstr ""

#: public/templates/cart.php:39
msgid "Apply coupon"
msgstr ""

#: public/templates/cart.php:44
msgid "Update cart"
msgstr ""

#: public/templates/cart.php:83 public/templates/order-details.php:90
msgid "Products sold by "
msgstr ""

#: public/templates/cart.php:96 public/templates/cart.php:176
#: public/templates/email-order-details.php:97
#: public/templates/invoices/commission-invoice.php:127
msgid "Quantity"
msgstr ""

#: public/templates/cart.php:129
msgid "Remove this item"
msgstr ""

#: public/templates/cart.php:165
msgid "Available on backorder"
msgstr ""

#: public/templates/email-order-details.php:27
#: public/templates/order-details.php:45
msgid ""
"Since your order contains products sold by different vendors, it has been "
"split into multiple sub-orders. Each sub-order will be handled by their "
"respective vendor independently."
msgstr ""

#. translators: %s: Order ID.
#: public/templates/email-order-details.php:47
#, php-format
msgid "[Order Totals #%s]"
msgstr ""

#: public/templates/email-order-details.php:72
#: public/templates/email-order-details.php:134
#: public/templates/order-details.php:71 public/templates/order-details.php:163
msgid "Note:"
msgstr ""

#: public/templates/email-order-details.php:91
msgid "Products sold by"
msgstr ""

#: public/templates/invoices/commission-invoice.php:108
msgid "Order Number:"
msgstr ""

#: public/templates/invoices/commission-invoice.php:140
msgid "Commission - Order"
msgstr ""

#: public/templates/invoices/commission-invoice.php:203
msgid "Customer Notes"
msgstr ""

#: public/templates/invoices/commission-invoice.php:224
msgid "VAT"
msgstr ""

#: public/templates/order-details.php:43
msgid "Order details"
msgstr ""

#: public/templates/store-page.php:16
msgid "Store: "
msgstr ""

#: public/templates/stores-list.php:77 public/templates/stores-list.php:244
msgid "Vendor Pic"
msgstr ""

#: public/templates/stores-list.php:78 public/templates/stores-list.php:245
msgid "Vendor Name"
msgstr ""

#: public/templates/stores-list.php:79 public/templates/stores-list.php:246
msgid "Vendor Rating"
msgstr ""

#: public/templates/stores-list.php:148
msgid "out of 5"
msgstr ""

#: public/templates/stores-list.php:219
msgid "This is your store."
msgstr ""

#. translators: 1: order number 2: order date 3: order status
#: public/templates/view-order.php:25 public/templates/view-order.php:45
#, php-format
msgid "Order #%1$s was placed on %2$s and is currently %3$s."
msgstr ""

#: public/templates/view-order.php:50
msgid "This is a sub-order of order"
msgstr ""

#: public/templates/view-order.php:52
msgid "Products are sold by "
msgstr ""

#: public/templates/view-order.php:61
msgid "Order updates"
msgstr ""

#: public/templates/view-order.php:67
msgid "l jS \\o\\f F Y, h:ia"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "MarketKing Core"
msgstr ""

#. Plugin URI of the plugin/theme
msgid ""
"https://wordpress.org/plugins/marketking-multivendor-marketplace-for-"
"woocommerce"
msgstr ""

#. Description of the plugin/theme
msgid ""
"MarketKing is the complete solution for turning WooCommerce into a powerful "
"multivendor marketplace. Core plugin."
msgstr ""

#. Author of the plugin/theme
msgid "WebWizards"
msgstr ""

#. Author URI of the plugin/theme
msgid "webwizards.dev"
msgstr ""
