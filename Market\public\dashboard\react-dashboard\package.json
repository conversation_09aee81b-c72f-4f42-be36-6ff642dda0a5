{"name": "marketking-react-dashboard", "version": "1.0.0", "description": "MarketKing React Dashboard for WordPress", "main": "src/index.tsx", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "build:dev": "webpack --mode development", "watch": "webpack --mode development --watch", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "chart.js": "^4.2.1", "react-chartjs-2": "^5.2.0", "date-fns": "^2.29.3", "classnames": "^2.3.2"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/node": "^18.14.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "typescript": "^4.9.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.8.0", "ts-loader": "^9.4.2", "css-loader": "^6.7.3", "sass-loader": "^13.2.0", "sass": "^1.58.0", "style-loader": "^3.3.1", "mini-css-extract-plugin": "^2.7.2", "html-webpack-plugin": "^5.5.0", "clean-webpack-plugin": "^4.0.0", "eslint": "^8.34.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["react", "typescript", "wordpress", "dashboard", "marketking", "vendor"], "author": "MarketKing", "license": "GPL-2.0+"}