import React, { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import { apiService } from '../../utils/api';
import { ChartData } from '../../types';

ChartJS.register(ArcElement, Tooltip, Legend);

const OrdersChart: React.FC = () => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOrdersData = async () => {
      try {
        setLoading(true);
        const response = await apiService.get<ChartData>('/vendor/orders-chart?period=30days');
        if (response.success) {
          setChartData(response.data);
        }
      } catch (error) {
        console.error('Sipariş grafik verileri yüklenirken hata:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrdersData();
  }, []);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#1c2b46',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#364a63',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed || 0;
            return `${label}: ${value} sipariş`;
          }
        }
      },
    },
    cutout: '70%',
  };

  const data = chartData ? {
    labels: chartData.labels,
    datasets: [
      {
        data: chartData.datasets[0]?.data || [],
        backgroundColor: [
          '#66bb6a', // Tamamlanan - Yeşil
          '#42a5f5', // Bekleyen - Mavi
          '#ef5350', // İptal - Kırmızı
          '#ffa726', // Diğer - Turuncu
        ],
        borderWidth: 0,
        hoverBorderWidth: 2,
        hoverBorderColor: '#fff',
      },
    ],
  } : null;

  // Toplam sipariş sayısını hesapla
  const totalOrders = chartData?.datasets[0]?.data.reduce((sum, value) => sum + value, 0) || 0;

  return (
    <div className="card card-full overflow-hidden">
      <div className="nk-ecwg nk-ecwg7 h-100">
        <div className="card-inner flex-grow-1">
          <div className="card-title-group mb-4">
            <div className="card-title">
              <h6 className="title">Sipariş İstatistikleri (Son 30 Gün)</h6>
            </div>
          </div>
          
          <div className="nk-ecwg7-ck" style={{ height: '200px', position: 'relative' }}>
            {loading ? (
              <div className="d-flex justify-content-center align-items-center h-100">
                <div className="nk-spinner"></div>
              </div>
            ) : data && totalOrders > 0 ? (
              <>
                <Doughnut data={data} options={options} />
                <div 
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    textAlign: 'center',
                  }}
                >
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#364a63' }}>
                    {totalOrders}
                  </div>
                  <div style={{ fontSize: '12px', color: '#8094ae' }}>
                    Toplam Sipariş
                  </div>
                </div>
              </>
            ) : (
              <div className="d-flex justify-content-center align-items-center h-100">
                <p className="text-soft">Henüz sipariş bulunmuyor...</p>
              </div>
            )}
          </div>
          
          {chartData && totalOrders > 0 && (
            <ul className="nk-ecwg7-legends">
              {chartData.labels.map((label, index) => (
                <li key={label}>
                  <div className="title">
                    <span 
                      className="dot dot-lg sq" 
                      style={{ 
                        backgroundColor: data?.datasets[0].backgroundColor[index] 
                      }}
                    ></span>
                    <span>{label}</span>
                  </div>
                  <div className="value">
                    {chartData.datasets[0]?.data[index] || 0}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrdersChart;
