import React from 'react';
import { useDashboard } from '../../contexts/DashboardContext';

interface HeaderProps {
  onToggleSidebar: () => void;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar }) => {
  const { vendor } = useDashboard();

  return (
    <div className="nk-header nk-header-fixed is-light">
      <div className="container-fluid">
        <div className="nk-header-wrap">
          <div className="nk-menu-trigger d-xl-none ml-n1">
            <button 
              className="nk-nav-toggle nk-quick-nav-icon" 
              onClick={onToggleSidebar}
              aria-label="Toggle Sidebar"
            >
              <em className="icon ni ni-menu"></em>
            </button>
          </div>
          
          <div className="nk-header-brand d-xl-none">
            <a href="#" className="logo-link">
              <img 
                className="logo-small logo-img logo-img-small" 
                src="/wp-content/plugins/marketking-core/public/dashboard/assets/images/logo-small.png" 
                alt="logo-small" 
              />
            </a>
          </div>
          
          <div className="nk-header-tools">
            <ul className="nk-quick-nav">
              <li className="dropdown user-dropdown">
                <a href="#" className="dropdown-toggle" data-toggle="dropdown">
                  <div className="user-toggle">
                    <div className="user-avatar sm">
                      <em className="icon ni ni-user-alt"></em>
                    </div>
                    <div className="user-info d-none d-md-block">
                      <div className="user-status">Satıcı</div>
                      <div className="user-name dropdown-indicator">
                        {vendor?.name || 'Kullanıcı'}
                      </div>
                    </div>
                  </div>
                </a>
                <div className="dropdown-menu dropdown-menu-md dropdown-menu-right dropdown-menu-s1">
                  <div className="dropdown-inner user-card-wrap bg-lighter d-none d-md-block">
                    <div className="user-card">
                      <div className="user-avatar">
                        <span>{vendor?.name?.charAt(0) || 'U'}</span>
                      </div>
                      <div className="user-info">
                        <span className="lead-text">{vendor?.name || 'Kullanıcı'}</span>
                        <span className="sub-text">{vendor?.email || ''}</span>
                      </div>
                    </div>
                  </div>
                  <div className="dropdown-inner">
                    <ul className="link-list">
                      <li>
                        <a href="/profile">
                          <em className="icon ni ni-user-alt"></em>
                          <span>Profil Görüntüle</span>
                        </a>
                      </li>
                      <li>
                        <a href="/profile-settings">
                          <em className="icon ni ni-setting-alt"></em>
                          <span>Hesap Ayarları</span>
                        </a>
                      </li>
                      <li>
                        <a href="/help">
                          <em className="icon ni ni-help-alt"></em>
                          <span>Yardım</span>
                        </a>
                      </li>
                    </ul>
                  </div>
                  <div className="dropdown-inner">
                    <ul className="link-list">
                      <li>
                        <a href="/wp-admin/admin.php?page=wc-admin&path=%2Fanalytics%2Frevenue">
                          <em className="icon ni ni-signout"></em>
                          <span>Çıkış Yap</span>
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
