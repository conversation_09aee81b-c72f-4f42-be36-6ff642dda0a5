#!/bin/bash

# MarketKing React Dashboard Build Script

echo "🚀 MarketKing React Dashboard Build Başlatılıyor..."

# Node.js ve npm kontrolü
if ! command -v node &> /dev/null; then
    echo "❌ Node.js bulunamadı. Lütfen Node.js'i yükleyin."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm bulunamadı. Lütfen npm'i yükleyin."
    exit 1
fi

echo "✅ Node.js ve npm bulundu."

# Bağımlılıkları yükle
echo "📦 Bağımlılıklar yükleniyor..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Bağımlılık yükleme başarısız."
    exit 1
fi

echo "✅ Bağımlılıklar başarıyla yüklendi."

# TypeScript kontrolü
echo "🔍 TypeScript kontrolü yapılıyor..."
npm run type-check

if [ $? -ne 0 ]; then
    echo "⚠️  TypeScript hataları bulundu, ancak build devam ediyor..."
fi

# Linting
echo "🧹 Kod kalitesi kontrolü..."
npm run lint

if [ $? -ne 0 ]; then
    echo "⚠️  Linting hataları bulundu, ancak build devam ediyor..."
fi

# Production build
echo "🏗️  Production build oluşturuluyor..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build başarısız."
    exit 1
fi

echo "✅ Build başarıyla tamamlandı!"

# Build sonuçlarını kontrol et
if [ -d "dist" ]; then
    echo "📁 Build dosyaları:"
    ls -la dist/
    
    # Dosya boyutlarını göster
    echo ""
    echo "📊 Dosya boyutları:"
    du -h dist/*
    
    echo ""
    echo "🎉 MarketKing React Dashboard başarıyla build edildi!"
    echo "📂 Build dosyaları 'dist' klasöründe bulunuyor."
    echo ""
    echo "🔧 WordPress'te kullanmak için:"
    echo "   1. WordPress admin panelinde MarketKing ayarlarına gidin"
    echo "   2. 'React Dashboard Kullan' seçeneğini etkinleştirin"
    echo "   3. Dashboard sayfasını yenileyin"
    
else
    echo "❌ Build klasörü bulunamadı."
    exit 1
fi
