import React from 'react';

const Products: React.FC = () => {
  return (
    <div className="nk-content">
      <div className="nk-content-inner">
        <div className="nk-content-body">
          <div className="nk-block-head nk-block-head-sm">
            <div className="nk-block-between">
              <div className="nk-block-head-content">
                <h4 className="nk-block-title page-title"><PERSON>rünler</h4>
                <div className="nk-block-des text-soft">
                  <p><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yönetin, düzenleyin ve yeni ürünler ekleyin.</p>
                </div>
              </div>
              <div className="nk-block-head-content">
                <div className="toggle-wrap nk-block-tools-toggle">
                  <a href="#" className="btn btn-icon btn-trigger toggle-expand mr-n1">
                    <em className="icon ni ni-menu-alt-r"></em>
                  </a>
                  <div className="toggle-expand-content">
                    <ul className="nk-block-tools g-3">
                      <li>
                        <button className="btn btn-primary">
                          <em className="icon ni ni-plus"></em>
                          <span>Yeni Ürün</span>
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="nk-block">
            <div className="card card-stretch">
              <div className="card-inner-group">
                <div className="card-inner position-relative card-tools-toggle">
                  <div className="card-title-group">
                    <div className="card-tools mr-n1">
                      <ul className="btn-toolbar gx-1">
                        <li>
                          <div className="form-control-wrap">
                            <div className="form-icon form-icon-right">
                              <em className="icon ni ni-search"></em>
                            </div>
                            <input 
                              type="text" 
                              className="form-control" 
                              placeholder="Ürün ara..."
                            />
                          </div>
                        </li>
                        <li>
                          <div className="dropdown">
                            <a href="#" className="btn btn-trigger btn-icon dropdown-toggle">
                              <em className="icon ni ni-setting"></em>
                            </a>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                <div className="card-inner p-0">
                  <div className="nk-tb-list nk-tb-ulist">
                    <div className="nk-tb-item nk-tb-head">
                      <div className="nk-tb-col nk-tb-col-check">
                        <div className="custom-control custom-control-sm custom-checkbox notext">
                          <input type="checkbox" className="custom-control-input" id="pid-all" />
                          <label className="custom-control-label" htmlFor="pid-all"></label>
                        </div>
                      </div>
                      <div className="nk-tb-col">
                        <span className="sub-text">Ürün</span>
                      </div>
                      <div className="nk-tb-col tb-col-mb">
                        <span className="sub-text">SKU</span>
                      </div>
                      <div className="nk-tb-col tb-col-md">
                        <span className="sub-text">Fiyat</span>
                      </div>
                      <div className="nk-tb-col tb-col-lg">
                        <span className="sub-text">Stok</span>
                      </div>
                      <div className="nk-tb-col tb-col-lg">
                        <span className="sub-text">Durum</span>
                      </div>
                      <div className="nk-tb-col nk-tb-col-tools text-right">
                        <span className="sub-text">İşlemler</span>
                      </div>
                    </div>
                    
                    {/* Ürün listesi buraya gelecek */}
                    <div className="nk-tb-item">
                      <div className="nk-tb-col-12">
                        <div className="text-center py-4">
                          <em className="icon ni ni-package" style={{ fontSize: '3rem', color: '#c4c4c4' }}></em>
                          <p className="text-soft mt-2">Henüz ürün eklenmemiş</p>
                          <button className="btn btn-primary btn-sm mt-2">
                            İlk Ürününüzü Ekleyin
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Products;
