/*
    A simple jQuery modalzz (http://github.com/kylefox/jquery-modalzz)
    Version 0.9.1
*/
!function(o){"object"==typeof module&&"object"==typeof module.exports?o(require("jquery"),window,document):o(jQuery,window,document)}(function(o,t,i,e){var s=[],l=function(){return s.length?s[s.length-1]:null},n=function(){var o,t=!1;for(o=s.length-1;o>=0;o--)s[o].$blocker&&(s[o].$blocker.toggleClass("current",!t).toggleClass("behind",t),t=!0)};o.modalzz=function(t,i){var e,n;if(this.$body=o("body"),this.options=o.extend({},o.modalzz.defaults,i),this.options.doFade=!isNaN(parseInt(this.options.fadeDuration,10)),this.$blocker=null,this.options.closeExisting)for(;o.modalzz.isActive();)o.modalzz.close();if(s.push(this),t.is("a"))if(n=t.attr("href"),this.anchor=t,/^#/.test(n)){if(this.$elm=o(n),1!==this.$elm.length)return null;this.$body.append(this.$elm),this.open()}else this.$elm=o("<div>"),this.$body.append(this.$elm),e=function(o,t){t.elm.remove()},this.showSpinner(),t.trigger(o.modalzz.AJAX_SEND),o.get(n).done(function(i){if(o.modalzz.isActive()){t.trigger(o.modalzz.AJAX_SUCCESS);var s=l();s.$elm.empty().append(i).on(o.modalzz.CLOSE,e),s.hideSpinner(),s.open(),t.trigger(o.modalzz.AJAX_COMPLETE)}}).fail(function(){t.trigger(o.modalzz.AJAX_FAIL);var i=l();i.hideSpinner(),s.pop(),t.trigger(o.modalzz.AJAX_COMPLETE)});else this.$elm=t,this.anchor=t,this.$body.append(this.$elm),this.open()},o.modalzz.prototype={constructor:o.modalzz,open:function(){var t=this;this.block(),this.anchor.blur(),this.options.doFade?setTimeout(function(){t.show()},this.options.fadeDuration*this.options.fadeDelay):this.show(),o(i).off("keydown.modalzz").on("keydown.modalzz",function(o){var t=l();27===o.which&&t.options.escapeClose&&t.close()}),this.options.clickClose&&this.$blocker.click(function(t){t.target===this&&o.modalzz.close()})},close:function(){s.pop(),this.unblock(),this.hide(),o.modalzz.isActive()||o(i).off("keydown.modalzz")},block:function(){jQuery('#masthead').css('z-index','1'),this.$elm.trigger(o.modalzz.BEFORE_BLOCK,[this._ctx()]),this.$body.css("overflow","hidden"),this.$blocker=o('<div class="'+this.options.blockerClass+' blocker current"></div>').appendTo(this.$body),n(),this.options.doFade&&this.$blocker.css("opacity",0).animate({opacity:1},this.options.fadeDuration),this.$elm.trigger(o.modalzz.BLOCK,[this._ctx()])},unblock:function(t){jQuery('#masthead').css('z-index','999'),!t&&this.options.doFade?this.$blocker.fadeOut(this.options.fadeDuration,this.unblock.bind(this,!0)):(this.$blocker.children().appendTo(this.$body),this.$blocker.remove(),this.$blocker=null,n(),o.modalzz.isActive()||this.$body.css("overflow",""))},show:function(){this.$elm.trigger(o.modalzz.BEFORE_OPEN,[this._ctx()]),this.options.showClose&&(this.closeButton=o('<a href="#close-modalzz" rel="modalzz:close" class="close-modalzz '+this.options.closeClass+'">'+this.options.closeText+"</a>"),this.$elm.append(this.closeButton)),this.$elm.addClass(this.options.modalzzClass).appendTo(this.$blocker),this.options.doFade?this.$elm.css({opacity:0,display:"inline-block"}).animate({opacity:1},this.options.fadeDuration):this.$elm.css("display","inline-block"),this.$elm.trigger(o.modalzz.OPEN,[this._ctx()])},hide:function(){this.$elm.trigger(o.modalzz.BEFORE_CLOSE,[this._ctx()]),this.closeButton&&this.closeButton.remove();var t=this;this.options.doFade?this.$elm.fadeOut(this.options.fadeDuration,function(){t.$elm.trigger(o.modalzz.AFTER_CLOSE,[t._ctx()])}):this.$elm.hide(0,function(){t.$elm.trigger(o.modalzz.AFTER_CLOSE,[t._ctx()])}),this.$elm.trigger(o.modalzz.CLOSE,[this._ctx()])},showSpinner:function(){this.options.showSpinner&&(this.spinner=this.spinner||o('<div class="'+this.options.modalzzClass+'-spinner"></div>').append(this.options.spinnerHtml),this.$body.append(this.spinner),this.spinner.show())},hideSpinner:function(){this.spinner&&this.spinner.remove()},_ctx:function(){return{elm:this.$elm,$elm:this.$elm,$blocker:this.$blocker,options:this.options}}},o.modalzz.close=function(t){if(o.modalzz.isActive()){t&&t.preventDefault();var i=l();return i.close(),i.$elm}},o.modalzz.isActive=function(){return s.length>0},o.modalzz.getCurrent=l,o.modalzz.defaults={closeExisting:!0,escapeClose:!0,clickClose:!0,closeText:"",closeClass:"",modalzzClass:"modalzz",blockerClass:"jquery-modalzz",spinnerHtml:'<div class="rect1"></div><div class="rect2"></div><div class="rect3"></div><div class="rect4"></div>',showSpinner:!0,showClose:!0,fadeDuration:null,fadeDelay:1},o.modalzz.BEFORE_BLOCK="modalzz:before-block",o.modalzz.BLOCK="modalzz:block",o.modalzz.BEFORE_OPEN="modalzz:before-open",o.modalzz.OPEN="modalzz:open",o.modalzz.BEFORE_CLOSE="modalzz:before-close",o.modalzz.CLOSE="modalzz:close",o.modalzz.AFTER_CLOSE="modalzz:after-close",o.modalzz.AJAX_SEND="modalzz:ajax:send",o.modalzz.AJAX_SUCCESS="modalzz:ajax:success",o.modalzz.AJAX_FAIL="modalzz:ajax:fail",o.modalzz.AJAX_COMPLETE="modalzz:ajax:complete",o.fn.modalzz=function(t){return 1===this.length&&new o.modalzz(this,t),this},o(i).on("click.modalzz",'a[rel~="modalzz:close"]',o.modalzz.close),o(i).on("click.modalzz",'a[rel~="modalzz:open"]',function(t){t.preventDefault(),o(this).modalzz()})});