import React from 'react';
import { Link } from 'react-router-dom';
import { Order } from '../../types';
import { formatCurrency, formatShortDate, getOrderStatusColor, getOrderStatusText } from '../../utils/helpers';

interface RecentOrdersProps {
  orders: Order[];
  loading: boolean;
}

const RecentOrders: React.FC<RecentOrdersProps> = ({ orders, loading }) => {
  if (loading) {
    return (
      <div className="card card-full">
        <div className="card-inner">
          <div className="nk-spinner"></div>
        </div>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="card card-full">
        <div className="card-inner">
          <div className="card-title-group">
            <div className="card-title">
              <h6 className="title">Son Si<PERSON>ş<PERSON></h6>
            </div>
          </div>
          <p className="text-soft"><PERSON><PERSON><PERSON><PERSON> sipariş bulunmuyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card card-full">
      <div className="card-inner">
        <div className="card-title-group">
          <div className="card-title">
            <h6 className="title">Son Siparişler</h6>
          </div>
          <div className="card-tools">
            <Link to="/orders" className="link">
              Tümünü Görüntüle
            </Link>
          </div>
        </div>
      </div>
      
      <div className="nk-tb-list mt-n2">
        <div className="nk-tb-item nk-tb-head">
          <div className="nk-tb-col">
            <span>Sipariş No.</span>
          </div>
          <div className="nk-tb-col tb-col-sm">
            <span>Müşteri</span>
          </div>
          <div className="nk-tb-col tb-col-md">
            <span>Tarih</span>
          </div>
          <div className="nk-tb-col">
            <span>Tutar</span>
          </div>
          <div className="nk-tb-col">
            <span className="d-none d-sm-inline">Durum</span>
          </div>
        </div>

        {orders.map((order) => (
          <div key={order.id} className="nk-tb-item">
            <div className="nk-tb-col">
              <Link to={`/orders/${order.id}`}>
                <span className="tb-lead">#{order.order_number}</span>
              </Link>
            </div>
            
            <div className="nk-tb-col tb-col-sm">
              <Link to={`/orders/${order.id}`}>
                <div className="user-card">
                  <div className="user-avatar sm bg-purple-dim">
                    <span>
                      {order.customer_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </span>
                  </div>
                  <div className="user-name">
                    <span className="tb-lead">{order.customer_name}</span>
                  </div>
                </div>
              </Link>
            </div>
            
            <div className="nk-tb-col tb-col-md">
              <Link to={`/orders/${order.id}`}>
                <span className="tb-sub">{formatShortDate(order.date)}</span>
              </Link>
            </div>
            
            <div className="nk-tb-col">
              <Link to={`/orders/${order.id}`}>
                <span className="tb-sub tb-amount">
                  {formatCurrency(order.total, order.currency)}
                </span>
              </Link>
            </div>
            
            <div className="nk-tb-col">
              <Link to={`/orders/${order.id}`}>
                <span 
                  className="badge badge-dot badge-dot-xs"
                  style={{ 
                    backgroundColor: getOrderStatusColor(order.status),
                    color: 'white'
                  }}
                >
                  {getOrderStatusText(order.status)}
                </span>
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecentOrders;
