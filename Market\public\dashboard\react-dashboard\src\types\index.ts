// Vendor/Satıcı tipleri
export interface Vendor {
  id: number;
  name: string;
  email: string;
  store_name: string;
  store_url: string;
  avatar: string;
  status: 'active' | 'pending' | 'inactive';
  balance: number;
  total_earnings: number;
  commission_rate: number;
}

// Sipariş tipleri
export interface Order {
  id: number;
  order_number: string;
  customer_name: string;
  customer_email: string;
  date: string;
  status: 'pending' | 'processing' | 'completed' | 'cancelled' | 'refunded';
  total: number;
  currency: string;
  items: OrderItem[];
}

export interface OrderItem {
  id: number;
  name: string;
  quantity: number;
  price: number;
  total: number;
}

// Ürün tipleri
export interface Product {
  id: number;
  name: string;
  sku: string;
  price: number;
  sale_price?: number;
  stock_quantity: number;
  status: 'publish' | 'draft' | 'pending';
  image: string;
  categories: string[];
  date_created: string;
}

// İstatistik tipleri
export interface DashboardStats {
  total_orders: number;
  total_customers: number;
  total_products: number;
  total_earnings: number;
  monthly_earnings: number;
  pending_orders: number;
  completed_orders: number;
  cancelled_orders: number;
}

// Grafik veri tipleri
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
  }[];
}

// API Response tipleri
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  total?: number;
  pages?: number;
}

// Menu item tipleri
export interface MenuItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  badge?: number;
  children?: MenuItem[];
  permission?: string;
}

// Context tipleri
export interface DashboardContextType {
  vendor: Vendor | null;
  stats: DashboardStats | null;
  loading: boolean;
  error: string | null;
  refreshData: () => void;
}
