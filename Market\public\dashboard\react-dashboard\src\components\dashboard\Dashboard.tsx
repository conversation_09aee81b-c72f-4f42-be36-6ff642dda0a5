import React, { useEffect, useState } from 'react';
import { useDashboard } from '../../contexts/DashboardContext';
import StatsCards from './StatsCards';
import EarningsChart from './EarningsChart';
import OrdersChart from './OrdersChart';
import RecentOrders from './RecentOrders';
import { Order } from '../../types';
import { apiService } from '../../utils/api';

const Dashboard: React.FC = () => {
  const { vendor, stats } = useDashboard();
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [loadingOrders, setLoadingOrders] = useState(true);

  useEffect(() => {
    const fetchRecentOrders = async () => {
      try {
        setLoadingOrders(true);
        const response = await apiService.get<Order[]>('/vendor/orders?limit=5');
        if (response.success) {
          setRecentOrders(response.data);
        }
      } catch (error) {
        console.error('Son siparişler yüklenirken hata:', error);
      } finally {
        setLoadingOrders(false);
      }
    };

    fetchRecentOrders();
  }, []);

  return (
    <div className="nk-content marketking_dashboard_page">
      <div className="nk-content-inner">
        <div className="nk-content-body">
          {/* Başlık */}
          <div className="nk-block-head nk-block-head-sm">
            <div className="nk-block-between">
              <div className="nk-block-head-content">
                <h4 className="nk-block-title page-title">Dashboard</h4>
                <div className="nk-block-des text-soft fs-15px">
                  <p>
                    Satıcı dashboard'unuza hoş geldiniz
                    {vendor?.name && `, ${vendor.name}`}! 
                    İşte her şeyin bir bakışta özeti...
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* İstatistik Kartları */}
          <div className="nk-block">
            <div className="row g-gs">
              <StatsCards stats={stats} />
            </div>
          </div>

          {/* Grafikler */}
          <div className="nk-block">
            <div className="row g-gs">
              <div className="col-xxl-8">
                <EarningsChart />
              </div>
              <div className="col-xxl-4">
                <OrdersChart />
              </div>
            </div>
          </div>

          {/* Son Siparişler */}
          {recentOrders.length > 0 && (
            <div className="nk-block">
              <div className="row g-gs">
                <div className="col-12">
                  <RecentOrders 
                    orders={recentOrders} 
                    loading={loadingOrders} 
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
