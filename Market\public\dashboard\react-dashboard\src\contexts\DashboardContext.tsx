import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Vendor, DashboardStats, DashboardContextType } from '../types';
import { apiService } from '../utils/api';

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

interface DashboardProviderProps {
  children: ReactNode;
}

export const DashboardProvider: React.FC<DashboardProviderProps> = ({ children }) => {
  const [vendor, setVendor] = useState<Vendor | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Vendor bilgilerini al
      const vendorResponse = await apiService.get<Vendor>('/vendor/profile');
      if (vendorResponse.success) {
        setVendor(vendorResponse.data);
      }

      // Dashboard istatistiklerini al
      const statsResponse = await apiService.get<DashboardStats>('/vendor/stats');
      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = () => {
    fetchDashboardData();
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const value: DashboardContextType = {
    vendor,
    stats,
    loading,
    error,
    refreshData,
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};
