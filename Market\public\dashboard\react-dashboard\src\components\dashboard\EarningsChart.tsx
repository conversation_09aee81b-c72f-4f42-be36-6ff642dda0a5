import React, { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { apiService } from '../../utils/api';
import { ChartData } from '../../types';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const EarningsChart: React.FC = () => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState<'7days' | '30days' | '90days'>('30days');

  useEffect(() => {
    const fetchEarningsData = async () => {
      try {
        setLoading(true);
        const response = await apiService.get<ChartData>(`/vendor/earnings-chart?period=${period}`);
        if (response.success) {
          setChartData(response.data);
        }
      } catch (error) {
        console.error('Kazanç grafik verileri yüklenirken hata:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEarningsData();
  }, [period]);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#1c2b46',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#364a63',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            return `Kazanç: ₺${context.parsed.y.toLocaleString('tr-TR')}`;
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#8094ae',
        },
      },
      y: {
        grid: {
          color: '#e5ecf8',
        },
        ticks: {
          color: '#8094ae',
          callback: function(value: any) {
            return '₺' + value.toLocaleString('tr-TR');
          }
        },
      },
    },
    elements: {
      point: {
        radius: 4,
        hoverRadius: 6,
      },
      line: {
        tension: 0.4,
      },
    },
  };

  const data = chartData ? {
    labels: chartData.labels,
    datasets: [
      {
        label: 'Kazançlar',
        data: chartData.datasets[0]?.data || [],
        borderColor: '#816bff',
        backgroundColor: 'rgba(129, 107, 255, 0.1)',
        fill: true,
        borderWidth: 2,
      },
    ],
  } : null;

  return (
    <div className="card card-full">
      <div className="card-inner">
        <div className="card-title-group">
          <div className="card-title">
            <h6 className="title">Kazanç Grafiği</h6>
          </div>
          <div className="card-tools">
            <div className="dropdown">
              <button 
                className="dropdown-toggle btn btn-sm btn-outline-light" 
                data-toggle="dropdown"
              >
                {period === '7days' && 'Son 7 Gün'}
                {period === '30days' && 'Son 30 Gün'}
                {period === '90days' && 'Son 90 Gün'}
              </button>
              <div className="dropdown-menu dropdown-menu-right">
                <button 
                  className="dropdown-item"
                  onClick={() => setPeriod('7days')}
                >
                  Son 7 Gün
                </button>
                <button 
                  className="dropdown-item"
                  onClick={() => setPeriod('30days')}
                >
                  Son 30 Gün
                </button>
                <button 
                  className="dropdown-item"
                  onClick={() => setPeriod('90days')}
                >
                  Son 90 Gün
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="nk-ecwg-ck" style={{ height: '300px', padding: '20px' }}>
        {loading ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <div className="nk-spinner"></div>
          </div>
        ) : data ? (
          <Line data={data} options={options} />
        ) : (
          <div className="d-flex justify-content-center align-items-center h-100">
            <p className="text-soft">Grafik verileri yüklenemedi</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EarningsChart;
