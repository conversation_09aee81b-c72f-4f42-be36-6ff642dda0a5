@font-face { font-family: "Nioicon"; src: url("../../../public/dashboard/assets/fonts/Nioicon.eot"); src: url("../../../public/dashboard/assets/fonts/Nioicon.eot#iefix") format("embedded-opentype"), url("../../../public/dashboard/assets/fonts/Nioicon.ttf") format("truetype"), url("../../../public/dashboard/assets/fonts/Nioicon.woff") format("woff"), url("../../../public/dashboard/assets/fonts/Nioicon.svg#Nioicon") format("svg"); font-weight: normal; font-style: normal; font-display: block; }

.plugin-card .column-compatibility, .plugin-card .column-updated {
    width: 65% !important;
}	
.plugin-card .name, .plugin-card .desc > p {
    margin-right: 108px;
}

.marketking-ni { /* use !important to prevent issues with browser extensions that change fonts */ font-family: "Nioicon" !important; speak: never; font-style: normal; font-weight: normal; font-variant: normal; text-transform: none; line-height: 1; /* Better Font Rendering =========== */ -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }

.marketking-ni-centos:before { content: ""; }

.marketking-ni-covid:before { content: ""; }

.marketking-ni-fedora:before { content: ""; }

.marketking-ni-hot-fill:before { content: ""; }

.marketking-ni-hot:before { content: ""; }

.marketking-ni-linux-server:before { content: ""; }

.marketking-ni-linux:before { content: ""; }

.marketking-ni-note-add-fill:before { content: ""; }

.marketking-ni-repeat-fill:before { content: ""; }

.marketking-ni-tranx-fill:before { content: ""; }

.marketking-ni-ubuntu:before { content: ""; }

.marketking-ni-virus:before { content: ""; }

.marketking-ni-b-chrome:before { content: ""; }

.marketking-ni-b-edge:before { content: ""; }

.marketking-ni-b-firefox:before { content: ""; }

.marketking-ni-b-ie:before { content: ""; }

.marketking-ni-b-opera:before { content: ""; }

.marketking-ni-b-safari:before { content: ""; }

.marketking-ni-b-si:before { content: ""; }

.marketking-ni-b-uc:before { content: ""; }

.marketking-ni-brick-fill:before { content: ""; }

.marketking-ni-brick:before { content: ""; }

.marketking-ni-col-3s:before { content: ""; }

.marketking-ni-col-4s:before { content: ""; }

.marketking-ni-col-2s:before { content: ""; }

.marketking-ni-comments:before { content: ""; }

.marketking-ni-dot-sq:before { content: ""; }

.marketking-ni-dot:before { content: ""; }

.marketking-ni-footer:before { content: ""; }

.marketking-ni-header:before { content: ""; }

.marketking-ni-heading:before { content: ""; }

.marketking-ni-layout-alt-fill:before { content: ""; }

.marketking-ni-layout-alt:before { content: ""; }

.marketking-ni-layout-fill1:before { content: ""; }

.marketking-ni-layout1:before { content: ""; }

.marketking-ni-list-index-fill:before { content: ""; }

.marketking-ni-list-index:before { content: ""; }

.marketking-ni-list-thumb-alt-fill:before { content: ""; }

.marketking-ni-list-thumb-alt:before { content: ""; }

.marketking-ni-list-thumb-fill:before { content: ""; }

.marketking-ni-list-thumb:before { content: ""; }

.marketking-ni-masonry-fill:before { content: ""; }

.marketking-ni-masonry:before { content: ""; }

.marketking-ni-menu-circled:before { content: ""; }

.marketking-ni-menu-squared:before { content: ""; }

.marketking-ni-notice:before { content: ""; }

.marketking-ni-pen2:before { content: ""; }

.marketking-ni-property-blank:before { content: ""; }

.marketking-ni-propert-blank:before { content: ""; }

.marketking-ni-property-add:before { content: ""; }

.marketking-ni-property-alt:before { content: ""; }

.marketking-ni-property-remove:before { content: ""; }

.marketking-ni-property:before { content: ""; }

.marketking-ni-puzzle-fill:before { content: ""; }

.marketking-ni-puzzle:before { content: ""; }

.marketking-ni-quote-left:before { content: ""; }

.marketking-ni-quote-right:before { content: ""; }

.marketking-ni-row-mix:before { content: ""; }

.marketking-ni-row-view1:before { content: ""; }

.marketking-ni-sidebar-r:before { content: ""; }

.marketking-ni-text2:before { content: ""; }

.marketking-ni-tile-thumb-fill:before { content: ""; }

.marketking-ni-tile-thumb:before { content: ""; }

.marketking-ni-view-col-fill:before { content: ""; }

.marketking-ni-view-col-sq:before { content: ""; }

.marketking-ni-view-col:before { content: ""; }

.marketking-ni-view-col2:before { content: ""; }

.marketking-ni-view-col3:before { content: ""; }

.marketking-ni-view-cols-fill:before { content: ""; }

.marketking-ni-view-cols-sq:before { content: ""; }

.marketking-ni-view-cols:before { content: ""; }

.marketking-ni-view-grid-fill:before { content: ""; }

.marketking-ni-view-grid-sq:before { content: ""; }

.marketking-ni-view-grid-wd:before { content: ""; }

.marketking-ni-view-grid:before { content: ""; }

.marketking-ni-view-grid2-wd:before { content: ""; }

.marketking-ni-view-grid3-wd:before { content: ""; }

.marketking-ni-view-group-fill:before { content: ""; }

.marketking-ni-view-group-wd:before { content: ""; }

.marketking-ni-view-list-fill:before { content: ""; }

.marketking-ni-view-list-sq:before { content: ""; }

.marketking-ni-view-list-wd:before { content: ""; }

.marketking-ni-view-list:before { content: ""; }

.marketking-ni-view-panel-fill:before { content: ""; }

.marketking-ni-view-panel-sq:before { content: ""; }

.marketking-ni-view-panel:before { content: ""; }

.marketking-ni-view-row-fill:before { content: ""; }

.marketking-ni-view-row-sq:before { content: ""; }

.marketking-ni-view-row-wd:before { content: ""; }

.marketking-ni-view-row:before { content: ""; }

.marketking-ni-view-x1:before { content: ""; }

.marketking-ni-view-x2:before { content: ""; }

.marketking-ni-view-x3:before { content: ""; }

.marketking-ni-view-x4:before { content: ""; }

.marketking-ni-view-x5:before { content: ""; }

.marketking-ni-view-x6:before { content: ""; }

.marketking-ni-view-x7:before { content: ""; }

.marketking-ni-dashlite:before { content: ""; }

.marketking-ni-dashlite-circle:before { content: ""; }

.marketking-ni-dashlite-alt:before { content: ""; }

.marketking-ni-master-card:before { content: ""; }

.marketking-ni-paypal:before { content: ""; }

.marketking-ni-visa-alt:before { content: ""; }

.marketking-ni-coin-eur:before { content: ""; }

.marketking-ni-coin-gbp:before { content: ""; }

.marketking-ni-sign-ada-alt:before { content: ""; }

.marketking-ni-sign-bch-alt:before { content: ""; }

.marketking-ni-sign-bgp-alt:before { content: ""; }

.marketking-ni-sign-bnb-alt:before { content: ""; }

.marketking-ni-sign-brl-alt:before { content: ""; }

.marketking-ni-sign-btc-alt:before { content: ""; }

.marketking-ni-sign-cc-alt:before { content: ""; }

.marketking-ni-sign-cc-alt2:before { content: ""; }

.marketking-ni-sign-chf-alt:before { content: ""; }

.marketking-ni-sign-cny-alt:before { content: ""; }

.marketking-ni-sign-czk-alt:before { content: ""; }

.marketking-ni-sign-dash-alt:before { content: ""; }

.marketking-ni-sign-dkk-alt:before { content: ""; }

.marketking-ni-sign-eos-alt:before { content: ""; }

.marketking-ni-sign-eth-alt:before { content: ""; }

.marketking-ni-sign-eur-alt2:before { content: ""; }

.marketking-ni-sign-euro-alt:before { content: ""; }

.marketking-ni-sign-gbp-alt2:before { content: ""; }

.marketking-ni-sign-hkd-alt:before { content: ""; }

.marketking-ni-sign-idr-alt:before { content: ""; }

.marketking-ni-sign-inr-alt:before { content: ""; }

.marketking-ni-sign-jpy-alt:before { content: ""; }

.marketking-ni-sign-kr-alt:before { content: ""; }

.marketking-ni-sign-ltc-alt:before { content: ""; }

.marketking-ni-sign-ltc:before { content: ""; }

.marketking-ni-sign-mxn-alt:before { content: ""; }

.marketking-ni-sign-mxr-alt:before { content: ""; }

.marketking-ni-sign-myr-alt:before { content: ""; }

.marketking-ni-sign-paypal-alt:before { content: ""; }

.marketking-ni-sign-paypal-full:before { content: ""; }

.marketking-ni-sign-php-alt:before { content: ""; }

.marketking-ni-sign-pln-alt:before { content: ""; }

.marketking-ni-sign-rub-alt:before { content: ""; }

.marketking-ni-sign-sek-alt:before { content: ""; }

.marketking-ni-sign-sgd-alt:before { content: ""; }

.marketking-ni-sign-kobo-alt:before { content: ""; }

.marketking-ni-sign-steem-alt:before { content: ""; }

.marketking-ni-sign-steller-alt:before { content: ""; }

.marketking-ni-sign-stripe-fulll:before { content: ""; }

.marketking-ni-sign-thb-alt:before { content: ""; }

.marketking-ni-sign-trx-alt:before { content: ""; }

.marketking-ni-sign-try-alt:before { content: ""; }

.marketking-ni-sign-usd-alt:before { content: ""; }

.marketking-ni-sign-usd-alt2:before { content: ""; }

.marketking-ni-sign-usdc-alt:before { content: ""; }

.marketking-ni-sign-usdt-alt:before { content: ""; }

.marketking-ni-sign-visa-alt:before { content: ""; }

.marketking-ni-sign-vnd-alt:before { content: ""; }

.marketking-ni-sign-waves-alt:before { content: ""; }

.marketking-ni-sign-xem-alt:before { content: ""; }

.marketking-ni-sign-xrp-new-alt:before { content: ""; }

.marketking-ni-sign-xrp-old-alt:before { content: ""; }

.marketking-ni-sign-zcash-alt:before { content: ""; }

.marketking-ni-chevron-left:before { content: ""; }

.marketking-ni-chevron-right:before { content: ""; }

.marketking-ni-chevron-up:before { content: ""; }

.marketking-ni-chevron-down:before { content: ""; }

.marketking-ni-chevron-left-round:before { content: ""; }

.marketking-ni-chevron-right-round:before { content: ""; }

.marketking-ni-chevron-up-round:before { content: ""; }

.marketking-ni-chevron-down-round:before { content: ""; }

.marketking-ni-chevron-left-round-fill:before { content: ""; }

.marketking-ni-chevron-right-round-fill:before { content: ""; }

.marketking-ni-chevron-up-round-fill:before { content: ""; }

.marketking-ni-chevron-down-round-fill:before { content: ""; }

.marketking-ni-chevron-left-c:before { content: ""; }

.marketking-ni-chevron-right-c:before { content: ""; }

.marketking-ni-chevron-up-c:before { content: ""; }

.marketking-ni-chevron-down-c:before { content: ""; }

.marketking-ni-chevron-left-fill-c:before { content: ""; }

.marketking-ni-chevron-right-fill-c:before { content: ""; }

.marketking-ni-chevron-up-fill-c:before { content: ""; }

.marketking-ni-chevron-down-fill-c:before { content: ""; }

.marketking-ni-chevron-left-circle:before { content: ""; }

.marketking-ni-chevron-right-circle:before { content: ""; }

.marketking-ni-chevron-up-circle:before { content: ""; }

.marketking-ni-chevron-down-circle:before { content: ""; }

.marketking-ni-chevron-left-circle-fill:before { content: ""; }

.marketking-ni-chevron-right-circle-fill:before { content: ""; }

.marketking-ni-chevron-up-circle-fill:before { content: ""; }

.marketking-ni-chevron-down-circle-fill:before { content: ""; }

.marketking-ni-caret-left:before { content: ""; }

.marketking-ni-caret-right:before { content: ""; }

.marketking-ni-caret-up:before { content: ""; }

.marketking-ni-caret-down:before { content: ""; }

.marketking-ni-caret-left-fill:before { content: ""; }

.marketking-ni-caret-right-fill:before { content: ""; }

.marketking-ni-caret-up-fill:before { content: ""; }

.marketking-ni-caret-down-fill:before { content: ""; }

.marketking-ni-sort:before { content: ""; }

.marketking-ni-sort-up:before { content: ""; }

.marketking-ni-sort-down:before { content: ""; }

.marketking-ni-sort-fill:before { content: ""; }

.marketking-ni-sort-up-fill:before { content: ""; }

.marketking-ni-sort-down-fill:before { content: ""; }

.marketking-ni-sort-v:before { content: ""; }

.marketking-ni-swap-v:before { content: ""; }

.marketking-ni-swap:before { content: ""; }

.marketking-ni-arrow-left-round:before { content: ""; }

.marketking-ni-arrow-right-round:before { content: ""; }

.marketking-ni-arrow-up-round:before { content: ""; }

.marketking-ni-arrow-down-round:before { content: ""; }

.marketking-ni-arrow-left-round-fill:before { content: ""; }

.marketking-ni-arrow-right-round-fill:before { content: ""; }

.marketking-ni-arrow-up-round-fill:before { content: ""; }

.marketking-ni-arrow-down-round-fill:before { content: ""; }

.marketking-ni-arrow-left-c:before { content: ""; }

.marketking-ni-arrow-right-c:before { content: ""; }

.marketking-ni-arrow-up-c:before { content: ""; }

.marketking-ni-arrow-down-c:before { content: ""; }

.marketking-ni-arrow-left-fill-c:before { content: ""; }

.marketking-ni-arrow-right-fill-c:before { content: ""; }

.marketking-ni-arrow-up-fill-c:before { content: ""; }

.marketking-ni-arrow-down-fill-c:before { content: ""; }

.marketking-ni-arrow-left-circle:before { content: ""; }

.marketking-ni-arrow-right-circle:before { content: ""; }

.marketking-ni-arrow-up-circle:before { content: ""; }

.marketking-ni-arrow-down-circle:before { content: ""; }

.marketking-ni-arrow-left-circle-fill:before { content: ""; }

.marketking-ni-arrow-up-circle-fill:before { content: ""; }

.marketking-ni-arrow-down-circle-fill:before { content: ""; }

.marketking-ni-arrow-right-circle-fill:before { content: ""; }

.marketking-ni-chevrons-left:before { content: ""; }

.marketking-ni-chevrons-right:before { content: ""; }

.marketking-ni-chevrons-up:before { content: ""; }

.marketking-ni-chevrons-down:before { content: ""; }

.marketking-ni-first:before { content: ""; }

.marketking-ni-last:before { content: ""; }

.marketking-ni-back-ios:before { content: ""; }

.marketking-ni-forward-ios:before { content: ""; }

.marketking-ni-upword-ios:before { content: ""; }

.marketking-ni-downward-ios:before { content: ""; }

.marketking-ni-back-alt:before { content: ""; }

.marketking-ni-forward-alt:before { content: ""; }

.marketking-ni-upword-alt:before { content: ""; }

.marketking-ni-downward-alt:before { content: ""; }

.marketking-ni-back-alt-fill:before { content: ""; }

.marketking-ni-forward-alt-fill:before { content: ""; }

.marketking-ni-upword-alt-fill:before { content: ""; }

.marketking-ni-downward-alt-fill:before { content: ""; }

.marketking-ni-arrow-long-left:before { content: ""; }

.marketking-ni-arrow-long-right:before { content: ""; }

.marketking-ni-arrow-long-up:before { content: ""; }

.marketking-ni-arrow-long-down:before { content: ""; }

.marketking-ni-arrow-left:before { content: ""; }

.marketking-ni-arrow-right:before { content: ""; }

.marketking-ni-arrow-up:before { content: ""; }

.marketking-ni-arrow-down:before { content: ""; }

.marketking-ni-arrow-up-left:before { content: ""; }

.marketking-ni-arrow-up-right:before { content: ""; }

.marketking-ni-arrow-down-left:before { content: ""; }

.marketking-ni-arrow-down-right:before { content: ""; }

.marketking-ni-arrow-to-left:before { content: ""; }

.marketking-ni-arrow-to-right:before { content: ""; }

.marketking-ni-arrow-to-up:before { content: ""; }

.marketking-ni-arrow-to-down:before { content: ""; }

.marketking-ni-arrow-from-left:before { content: ""; }

.marketking-ni-arrow-from-right:before { content: ""; }

.marketking-ni-arrow-from-up:before { content: ""; }

.marketking-ni-arrow-from-down:before { content: ""; }

.marketking-ni-curve-down-left:before { content: ""; }

.marketking-ni-curve-up-right:before { content: ""; }

.marketking-ni-curve-up-left:before { content: ""; }

.marketking-ni-curve-down-right:before { content: ""; }

.marketking-ni-curve-left-up:before { content: ""; }

.marketking-ni-curve-right-up:before { content: ""; }

.marketking-ni-curve-left-down:before { content: ""; }

.marketking-ni-curve-right-down:before { content: ""; }

.marketking-ni-back-arrow:before { content: ""; }

.marketking-ni-forward-arrow:before { content: ""; }

.marketking-ni-back-arrow-fill:before { content: ""; }

.marketking-ni-forward-arrow-fill:before { content: ""; }

.marketking-ni-navigate:before { content: ""; }

.marketking-ni-navigate-up:before { content: ""; }

.marketking-ni-navigate-fill:before { content: ""; }

.marketking-ni-navigate-up-fill:before { content: ""; }

.marketking-ni-send:before { content: ""; }

.marketking-ni-send-alt:before { content: ""; }

.marketking-ni-unfold-less:before { content: ""; }

.marketking-ni-unfold-more:before { content: ""; }

.marketking-ni-exchange-v:before { content: ""; }

.marketking-ni-exchange:before { content: ""; }

.marketking-ni-expand:before { content: ""; }

.marketking-ni-shrink:before { content: ""; }

.marketking-ni-focus:before { content: ""; }

.marketking-ni-maximize:before { content: ""; }

.marketking-ni-minimize:before { content: ""; }

.marketking-ni-maximize-alt:before { content: ""; }

.marketking-ni-minimize-alt:before { content: ""; }

.marketking-ni-shuffle:before { content: ""; }

.marketking-ni-cross-sm:before { content: ""; }

.marketking-ni-cross:before { content: ""; }

.marketking-ni-cross-round:before { content: ""; }

.marketking-ni-cross-circle:before { content: ""; }

.marketking-ni-cross-c:before { content: ""; }

.marketking-ni-cross-round-fill:before { content: ""; }

.marketking-ni-cross-circle-fill:before { content: ""; }

.marketking-ni-cross-fill-c:before { content: ""; }

.marketking-ni-na:before { content: ""; }

.marketking-ni-check:before { content: ""; }

.marketking-ni-check-thick:before { content: ""; }

.marketking-ni-done:before { content: ""; }

.marketking-ni-check-round:before { content: ""; }

.marketking-ni-check-circle:before { content: ""; }

.marketking-ni-check-c:before { content: ""; }

.marketking-ni-check-round-fill:before { content: ""; }

.marketking-ni-check-circle-fill:before { content: ""; }

.marketking-ni-check-fill-c:before { content: ""; }

.marketking-ni-check-circle-cut:before { content: ""; }

.marketking-ni-check-round-cut:before { content: ""; }

.marketking-ni-bullet:before { content: ""; }

.marketking-ni-circle:before { content: ""; }

.marketking-ni-square:before { content: ""; }

.marketking-ni-square-c:before { content: ""; }

.marketking-ni-bullet-fill:before { content: ""; }

.marketking-ni-circle-fill:before { content: ""; }

.marketking-ni-square-fill:before { content: ""; }

.marketking-ni-square-fill-c:before { content: ""; }

.marketking-ni-plus-sm:before { content: ""; }

.marketking-ni-minus-sm:before { content: ""; }

.marketking-ni-plus:before { content: ""; }

.marketking-ni-minus:before { content: ""; }

.marketking-ni-plus-round:before { content: ""; }

.marketking-ni-minus-round:before { content: ""; }

.marketking-ni-plus-circle:before { content: ""; }

.marketking-ni-minus-circle:before { content: ""; }

.marketking-ni-plus-c:before { content: ""; }

.marketking-ni-minus-c:before { content: ""; }

.marketking-ni-plus-round-fill:before { content: ""; }

.marketking-ni-plus-circle-fill:before { content: ""; }

.marketking-ni-minus-round-fill:before { content: ""; }

.marketking-ni-minus-circle-fill:before { content: ""; }

.marketking-ni-plus-fill-c:before { content: ""; }

.marketking-ni-minus-fill-c:before { content: ""; }

.marketking-ni-plus-medi:before { content: ""; }

.marketking-ni-plus-medi-fill:before { content: ""; }

.marketking-ni-equal-sm:before { content: ""; }

.marketking-ni-equal:before { content: ""; }

.marketking-ni-calc:before { content: ""; }

.marketking-ni-search:before { content: ""; }

.marketking-ni-zoom-out:before { content: ""; }

.marketking-ni-zoom-in:before { content: ""; }

.marketking-ni-play:before { content: ""; }

.marketking-ni-play-fill:before { content: ""; }

.marketking-ni-play-circle:before { content: ""; }

.marketking-ni-play-circle-fill:before { content: ""; }

.marketking-ni-pause:before { content: ""; }

.marketking-ni-pause-fill:before { content: ""; }

.marketking-ni-pause-circle:before { content: ""; }

.marketking-ni-pause-circle-fill:before { content: ""; }

.marketking-ni-stop:before { content: ""; }

.marketking-ni-stop-fill:before { content: ""; }

.marketking-ni-stop-circle:before { content: ""; }

.marketking-ni-stop-circle-fill:before { content: ""; }

.marketking-ni-rewind:before { content: ""; }

.marketking-ni-forward:before { content: ""; }

.marketking-ni-rewind-fill:before { content: ""; }

.marketking-ni-forward-fill:before { content: ""; }

.marketking-ni-step-back:before { content: ""; }

.marketking-ni-step-forward:before { content: ""; }

.marketking-ni-vol-off:before { content: ""; }

.marketking-ni-vol-no:before { content: ""; }

.marketking-ni-vol-half:before { content: ""; }

.marketking-ni-vol:before { content: ""; }

.marketking-ni-mic:before { content: ""; }

.marketking-ni-mic-off:before { content: ""; }

.marketking-ni-video:before { content: ""; }

.marketking-ni-video-off:before { content: ""; }

.marketking-ni-video-fill:before { content: ""; }

.marketking-ni-loader:before { content: ""; }

.marketking-ni-power:before { content: ""; }

.marketking-ni-signout:before { content: ""; }

.marketking-ni-signin:before { content: ""; }

.marketking-ni-upload:before { content: ""; }

.marketking-ni-download:before { content: ""; }

.marketking-ni-alert-circle:before { content: ""; }

.marketking-ni-alert:before { content: ""; }

.marketking-ni-caution:before { content: ""; }

.marketking-ni-report:before { content: ""; }

.marketking-ni-alert-c:before { content: ""; }

.marketking-ni-alert-circle-fill:before { content: ""; }

.marketking-ni-alert-fill:before { content: ""; }

.marketking-ni-caution-fill:before { content: ""; }

.marketking-ni-report-fill:before { content: ""; }

.marketking-ni-alert-fill-c:before { content: ""; }

.marketking-ni-info-i:before { content: ""; }

.marketking-ni-info:before { content: ""; }

.marketking-ni-info-fill:before { content: ""; }

.marketking-ni-help:before { content: ""; }

.marketking-ni-help-fill:before { content: ""; }

.marketking-ni-archived:before { content: ""; }

.marketking-ni-archive:before { content: ""; }

.marketking-ni-unarchive:before { content: ""; }

.marketking-ni-archived-fill:before { content: ""; }

.marketking-ni-archive-fill:before { content: ""; }

.marketking-ni-unarchive-fill:before { content: ""; }

.marketking-ni-bag:before { content: ""; }

.marketking-ni-bag-fill:before { content: ""; }

.marketking-ni-bell:before { content: ""; }

.marketking-ni-bell-off:before { content: ""; }

.marketking-ni-bell-fill:before { content: ""; }

.marketking-ni-bell-off-fill:before { content: ""; }

.marketking-ni-wifi:before { content: ""; }

.marketking-ni-wifi-off:before { content: ""; }

.marketking-ni-live:before { content: ""; }

.marketking-ni-signal:before { content: ""; }

.marketking-ni-bluetooth:before { content: ""; }

.marketking-ni-blank-alt:before { content: ""; }

.marketking-ni-blank:before { content: ""; }

.marketking-ni-blank-fill:before { content: ""; }

.marketking-ni-blankf-fill:before { content: ""; }

.marketking-ni-block-over:before { content: ""; }

.marketking-ni-book-read:before { content: ""; }

.marketking-ni-book:before { content: ""; }

.marketking-ni-book-fill:before { content: ""; }

.marketking-ni-bulb-fill:before { content: ""; }

.marketking-ni-bulb:before { content: ""; }

.marketking-ni-calendar-alt-fill:before { content: ""; }

.marketking-ni-calendar-alt:before { content: ""; }

.marketking-ni-calendar-booking-fill:before { content: ""; }

.marketking-ni-calendar-booking:before { content: ""; }

.marketking-ni-calendar-check-fill:before { content: ""; }

.marketking-ni-calendar-check:before { content: ""; }

.marketking-ni-calendar-fill:before { content: ""; }

.marketking-ni-calendar:before { content: ""; }

.marketking-ni-calender-date-fill:before { content: ""; }

.marketking-ni-calender-date:before { content: ""; }

.marketking-ni-call:before { content: ""; }

.marketking-ni-call-alt:before { content: ""; }

.marketking-ni-call-alt-fill:before { content: ""; }

.marketking-ni-call-fill:before { content: ""; }

.marketking-ni-camera-fill:before { content: ""; }

.marketking-ni-camera:before { content: ""; }

.marketking-ni-capsule:before { content: ""; }

.marketking-ni-capsule-fill:before { content: ""; }

.marketking-ni-cards:before { content: ""; }

.marketking-ni-cards-fill:before { content: ""; }

.marketking-ni-cart:before { content: ""; }

.marketking-ni-cart-fill:before { content: ""; }

.marketking-ni-cc:before { content: ""; }

.marketking-ni-cc-alt:before { content: ""; }

.marketking-ni-cc-alt2:before { content: ""; }

.marketking-ni-cc-secure:before { content: ""; }

.marketking-ni-cc-new:before { content: ""; }

.marketking-ni-cc-off:before { content: ""; }

.marketking-ni-cc-fill:before { content: ""; }

.marketking-ni-cc-alt-fill:before { content: ""; }

.marketking-ni-cc-alt2-fill:before { content: ""; }

.marketking-ni-cc-secure-fill:before { content: ""; }

.marketking-ni-msg-circle:before { content: ""; }

.marketking-ni-chat-circle:before { content: ""; }

.marketking-ni-msg:before { content: ""; }

.marketking-ni-chat:before { content: ""; }

.marketking-ni-question-alt:before { content: ""; }

.marketking-ni-question:before { content: ""; }

.marketking-ni-msg-circle-fill:before { content: ""; }

.marketking-ni-chat-circle-fill:before { content: ""; }

.marketking-ni-msg-fill:before { content: ""; }

.marketking-ni-chat-fill:before { content: ""; }

.marketking-ni-clip-h:before { content: ""; }

.marketking-ni-clip-v:before { content: ""; }

.marketking-ni-clip:before { content: ""; }

.marketking-ni-link-alt:before { content: ""; }

.marketking-ni-unlink:before { content: ""; }

.marketking-ni-unlink-alt:before { content: ""; }

.marketking-ni-link-h:before { content: ""; }

.marketking-ni-link-v:before { content: ""; }

.marketking-ni-link:before { content: ""; }

.marketking-ni-clipboard:before { content: ""; }

.marketking-ni-clipboad-check:before { content: ""; }

.marketking-ni-clipboard-fill:before { content: ""; }

.marketking-ni-clipboad-check-fill:before { content: ""; }

.marketking-ni-clock:before { content: ""; }

.marketking-ni-clock-fill:before { content: ""; }

.marketking-ni-cloud:before { content: ""; }

.marketking-ni-upload-cloud:before { content: ""; }

.marketking-ni-download-cloud:before { content: ""; }

.marketking-ni-cloud-fill:before { content: ""; }

.marketking-ni-contact:before { content: ""; }

.marketking-ni-contact-fill:before { content: ""; }

.marketking-ni-coffee:before { content: ""; }

.marketking-ni-coffee-fill:before { content: ""; }

.marketking-ni-box-view:before { content: ""; }

.marketking-ni-col-view:before { content: ""; }

.marketking-ni-sidebar:before { content: ""; }

.marketking-ni-layout:before { content: ""; }

.marketking-ni-table-view:before { content: ""; }

.marketking-ni-layout2:before { content: ""; }

.marketking-ni-row-view:before { content: ""; }

.marketking-ni-dot-box:before { content: ""; }

.marketking-ni-layout-fill:before { content: ""; }

.marketking-ni-box-view-fill:before { content: ""; }

.marketking-ni-sidebar-fill:before { content: ""; }

.marketking-ni-table-view-fill:before { content: ""; }

.marketking-ni-dot-box-fill:before { content: ""; }

.marketking-ni-template:before { content: ""; }

.marketking-ni-browser:before { content: ""; }

.marketking-ni-toolbar:before { content: ""; }

.marketking-ni-browser-fill:before { content: ""; }

.marketking-ni-toolbar-fill:before { content: ""; }

.marketking-ni-template-fill:before { content: ""; }

.marketking-ni-box:before { content: ""; }

.marketking-ni-package:before { content: ""; }

.marketking-ni-layer:before { content: ""; }

.marketking-ni-layers:before { content: ""; }

.marketking-ni-panel:before { content: ""; }

.marketking-ni-server:before { content: ""; }

.marketking-ni-layer-fill:before { content: ""; }

.marketking-ni-layers-fill:before { content: ""; }

.marketking-ni-package-fill:before { content: ""; }

.marketking-ni-panel-fill:before { content: ""; }

.marketking-ni-server-fill:before { content: ""; }

.marketking-ni-color-palette:before { content: ""; }

.marketking-ni-color-palette-fill:before { content: ""; }

.marketking-ni-copy:before { content: ""; }

.marketking-ni-copy-fill:before { content: ""; }

.marketking-ni-crop-alt:before { content: ""; }

.marketking-ni-crop:before { content: ""; }

.marketking-ni-target:before { content: ""; }

.marketking-ni-crosshair:before { content: ""; }

.marketking-ni-crosshair-fill:before { content: ""; }

.marketking-ni-db-fill:before { content: ""; }

.marketking-ni-db:before { content: ""; }

.marketking-ni-hard-drive:before { content: ""; }

.marketking-ni-cpu:before { content: ""; }

.marketking-ni-disk:before { content: ""; }

.marketking-ni-pen:before { content: ""; }

.marketking-ni-edit-alt:before { content: ""; }

.marketking-ni-pen-fill:before { content: ""; }

.marketking-ni-edit-alt-fill:before { content: ""; }

.marketking-ni-pen-alt-fill:before { content: ""; }

.marketking-ni-edit-fill:before { content: ""; }

.marketking-ni-edit:before { content: ""; }

.marketking-ni-external-alt:before { content: ""; }

.marketking-ni-external:before { content: ""; }

.marketking-ni-eye-alt:before { content: ""; }

.marketking-ni-eye-alt-fill:before { content: ""; }

.marketking-ni-eye:before { content: ""; }

.marketking-ni-eye-fill:before { content: ""; }

.marketking-ni-eye-off:before { content: ""; }

.marketking-ni-eye-off-fill:before { content: ""; }

.marketking-ni-file:before { content: ""; }

.marketking-ni-file-minus:before { content: ""; }

.marketking-ni-file-plus:before { content: ""; }

.marketking-ni-file-remove:before { content: ""; }

.marketking-ni-file-check:before { content: ""; }

.marketking-ni-file-code:before { content: ""; }

.marketking-ni-file-docs:before { content: ""; }

.marketking-ni-file-img:before { content: ""; }

.marketking-ni-file-doc:before { content: ""; }

.marketking-ni-file-pdf:before { content: ""; }

.marketking-ni-file-xls:before { content: ""; }

.marketking-ni-file-zip:before { content: ""; }

.marketking-ni-file-download:before { content: ""; }

.marketking-ni-file-text:before { content: ""; }

.marketking-ni-files:before { content: ""; }

.marketking-ni-file-fill:before { content: ""; }

.marketking-ni-file-minus-fill:before { content: ""; }

.marketking-ni-file-plus-fill:before { content: ""; }

.marketking-ni-file-remove-fill:before { content: ""; }

.marketking-ni-file-check-fill:before { content: ""; }

.marketking-ni-file-text-fill:before { content: ""; }

.marketking-ni-files-fill:before { content: ""; }

.marketking-ni-folder:before { content: ""; }

.marketking-ni-folder-minus:before { content: ""; }

.marketking-ni-folder-plus:before { content: ""; }

.marketking-ni-folder-remove:before { content: ""; }

.marketking-ni-folder-check:before { content: ""; }

.marketking-ni-folder-list:before { content: ""; }

.marketking-ni-folders:before { content: ""; }

.marketking-ni-folder-fill:before { content: ""; }

.marketking-ni-folders-fill:before { content: ""; }

.marketking-ni-filter-alt:before { content: ""; }

.marketking-ni-sort-line:before { content: ""; }

.marketking-ni-filter-fill:before { content: ""; }

.marketking-ni-filter:before { content: ""; }

.marketking-ni-flag:before { content: ""; }

.marketking-ni-flag-fill:before { content: ""; }

.marketking-ni-notify:before { content: ""; }

.marketking-ni-dashboard:before { content: ""; }

.marketking-ni-dashboard-fill:before { content: ""; }

.marketking-ni-grid-sq:before { content: ""; }

.marketking-ni-grid:before { content: ""; }

.marketking-ni-grid-c:before { content: ""; }

.marketking-ni-grid-alt:before { content: ""; }

.marketking-ni-grid-plus:before { content: ""; }

.marketking-ni-grid-add-c:before { content: ""; }

.marketking-ni-grid-fill:before { content: ""; }

.marketking-ni-grid-fill-c:before { content: ""; }

.marketking-ni-grid-alt-fill:before { content: ""; }

.marketking-ni-grid-plus-fill:before { content: ""; }

.marketking-ni-grid-add-fill-c:before { content: ""; }

.marketking-ni-grid-box-alt-fill:before { content: ""; }

.marketking-ni-grid-box-alt:before { content: ""; }

.marketking-ni-grid-box:before { content: ""; }

.marketking-ni-grid-box-fill:before { content: ""; }

.marketking-ni-grid-line:before { content: ""; }

.marketking-ni-menu-alt-left:before { content: ""; }

.marketking-ni-menu-alt-r:before { content: ""; }

.marketking-ni-menu-alt:before { content: ""; }

.marketking-ni-menu-center:before { content: ""; }

.marketking-ni-menu-left:before { content: ""; }

.marketking-ni-menu-right:before { content: ""; }

.marketking-ni-menu:before { content: ""; }

.marketking-ni-trend-up:before { content: ""; }

.marketking-ni-trend-down:before { content: ""; }

.marketking-ni-line-chart-down:before { content: ""; }

.marketking-ni-line-chart-up:before { content: ""; }

.marketking-ni-line-chart:before { content: ""; }

.marketking-ni-bar-chart:before { content: ""; }

.marketking-ni-bar-chart-alt:before { content: ""; }

.marketking-ni-chart-up:before { content: ""; }

.marketking-ni-chart-down:before { content: ""; }

.marketking-ni-growth:before { content: ""; }

.marketking-ni-growth-fill:before { content: ""; }

.marketking-ni-bar-chart-fill:before { content: ""; }

.marketking-ni-bar-c:before { content: ""; }

.marketking-ni-bar-fill-c:before { content: ""; }

.marketking-ni-pie:before { content: ""; }

.marketking-ni-pie-alt:before { content: ""; }

.marketking-ni-pie-fill:before { content: ""; }

.marketking-ni-activity:before { content: ""; }

.marketking-ni-activity-alt:before { content: ""; }

.marketking-ni-activity-round:before { content: ""; }

.marketking-ni-activity-round-fill:before { content: ""; }

.marketking-ni-meter:before { content: ""; }

.marketking-ni-speed:before { content: ""; }

.marketking-ni-happy:before { content: ""; }

.marketking-ni-sad:before { content: ""; }

.marketking-ni-meh:before { content: ""; }

.marketking-ni-happy-fill:before { content: ""; }

.marketking-ni-happyf-fill:before { content: ""; }

.marketking-ni-sad-fill:before { content: ""; }

.marketking-ni-meh-fill:before { content: ""; }

.marketking-ni-home:before { content: ""; }

.marketking-ni-home-alt:before { content: ""; }

.marketking-ni-home-fill:before { content: ""; }

.marketking-ni-img:before { content: ""; }

.marketking-ni-img-fill:before { content: ""; }

.marketking-ni-inbox:before { content: ""; }

.marketking-ni-inbox-in:before { content: ""; }

.marketking-ni-inbox-out:before { content: ""; }

.marketking-ni-inbox-fill:before { content: ""; }

.marketking-ni-inbox-in-fill:before { content: ""; }

.marketking-ni-inbox-out-fill:before { content: ""; }

.marketking-ni-link-group:before { content: ""; }

.marketking-ni-lock:before { content: ""; }

.marketking-ni-lock-alt:before { content: ""; }

.marketking-ni-lock-fill:before { content: ""; }

.marketking-ni-lock-alt-fill:before { content: ""; }

.marketking-ni-unlock:before { content: ""; }

.marketking-ni-unlock-fill:before { content: ""; }

.marketking-ni-mail:before { content: ""; }

.marketking-ni-emails:before { content: ""; }

.marketking-ni-mail-fill:before { content: ""; }

.marketking-ni-emails-fill:before { content: ""; }

.marketking-ni-map-pin:before { content: ""; }

.marketking-ni-location:before { content: ""; }

.marketking-ni-map:before { content: ""; }

.marketking-ni-map-pin-fill:before { content: ""; }

.marketking-ni-list:before { content: ""; }

.marketking-ni-list-ol:before { content: ""; }

.marketking-ni-align-center:before { content: ""; }

.marketking-ni-align-justify:before { content: ""; }

.marketking-ni-align-left:before { content: ""; }

.marketking-ni-align-right:before { content: ""; }

.marketking-ni-list-check:before { content: ""; }

.marketking-ni-list-round:before { content: ""; }

.marketking-ni-card-view:before { content: ""; }

.marketking-ni-list-fill:before { content: ""; }

.marketking-ni-save:before { content: ""; }

.marketking-ni-save-fill:before { content: ""; }

.marketking-ni-move:before { content: ""; }

.marketking-ni-scissor:before { content: ""; }

.marketking-ni-text:before { content: ""; }

.marketking-ni-text-a:before { content: ""; }

.marketking-ni-bold:before { content: ""; }

.marketking-ni-italic:before { content: ""; }

.marketking-ni-underline:before { content: ""; }

.marketking-ni-percent:before { content: ""; }

.marketking-ni-at:before { content: ""; }

.marketking-ni-hash:before { content: ""; }

.marketking-ni-code:before { content: ""; }

.marketking-ni-code-download:before { content: ""; }

.marketking-ni-terminal:before { content: ""; }

.marketking-ni-cmd:before { content: ""; }

.marketking-ni-sun:before { content: ""; }

.marketking-ni-sun-fill:before { content: ""; }

.marketking-ni-moon-fill:before { content: ""; }

.marketking-ni-moon:before { content: ""; }

.marketking-ni-light:before { content: ""; }

.marketking-ni-light-fill:before { content: ""; }

.marketking-ni-more-v:before { content: ""; }

.marketking-ni-more-h:before { content: ""; }

.marketking-ni-more-h-alt:before { content: ""; }

.marketking-ni-more-v-alt:before { content: ""; }

.marketking-ni-music:before { content: ""; }

.marketking-ni-movie:before { content: ""; }

.marketking-ni-offer:before { content: ""; }

.marketking-ni-offer-fill:before { content: ""; }

.marketking-ni-opt-alt:before { content: ""; }

.marketking-ni-opt:before { content: ""; }

.marketking-ni-opt-dot-alt:before { content: ""; }

.marketking-ni-opt-dot:before { content: ""; }

.marketking-ni-opt-dot-fill:before { content: ""; }

.marketking-ni-opt-alt-fill:before { content: ""; }

.marketking-ni-user-alt:before { content: ""; }

.marketking-ni-user-alt-fill:before { content: ""; }

.marketking-ni-user:before { content: ""; }

.marketking-ni-users:before { content: ""; }

.marketking-ni-user-add:before { content: ""; }

.marketking-ni-user-remove:before { content: ""; }

.marketking-ni-user-check:before { content: ""; }

.marketking-ni-user-cross:before { content: ""; }

.marketking-ni-account-setting:before { content: ""; }

.marketking-ni-account-setting-alt:before { content: ""; }

.marketking-ni-user-list:before { content: ""; }

.marketking-ni-user-fill:before { content: ""; }

.marketking-ni-users-fill:before { content: ""; }

.marketking-ni-user-add-fill:before { content: ""; }

.marketking-ni-user-remove-fill:before { content: ""; }

.marketking-ni-user-check-fill:before { content: ""; }

.marketking-ni-user-cross-fill:before { content: ""; }

.marketking-ni-account-setting-fill:before { content: ""; }

.marketking-ni-user-list-fill:before { content: ""; }

.marketking-ni-user-circle:before { content: ""; }

.marketking-ni-user-circle-fill:before { content: ""; }

.marketking-ni-user-c:before { content: ""; }

.marketking-ni-user-fill-c:before { content: ""; }

.marketking-ni-user-round:before { content: ""; }

.marketking-ni-printer:before { content: ""; }

.marketking-ni-printer-fill:before { content: ""; }

.marketking-ni-laptop:before { content: ""; }

.marketking-ni-monitor:before { content: ""; }

.marketking-ni-tablet:before { content: ""; }

.marketking-ni-mobile:before { content: ""; }

.marketking-ni-undo:before { content: ""; }

.marketking-ni-redo:before { content: ""; }

.marketking-ni-reload-alt:before { content: ""; }

.marketking-ni-reload:before { content: ""; }

.marketking-ni-regen-alt:before { content: ""; }

.marketking-ni-regen:before { content: ""; }

.marketking-ni-invest:before { content: ""; }

.marketking-ni-history:before { content: ""; }

.marketking-ni-histroy:before { content: ""; }

.marketking-ni-update:before { content: ""; }

.marketking-ni-repeat:before { content: ""; }

.marketking-ni-repeat-v:before { content: ""; }

.marketking-ni-tranx:before { content: ""; }

.marketking-ni-reply-all:before { content: ""; }

.marketking-ni-reply:before { content: ""; }

.marketking-ni-reply-fill:before { content: ""; }

.marketking-ni-reply-all-fill:before { content: ""; }

.marketking-ni-notes:before { content: ""; }

.marketking-ni-note-add:before { content: ""; }

.marketking-ni-notes-alt:before { content: ""; }

.marketking-ni-article:before { content: ""; }

.marketking-ni-text-rich:before { content: ""; }

.marketking-ni-todo:before { content: ""; }

.marketking-ni-report-profit:before { content: ""; }

.marketking-ni-reports-alt:before { content: ""; }

.marketking-ni-reports:before { content: ""; }

.marketking-ni-task:before { content: ""; }

.marketking-ni-note-add-c:before { content: ""; }

.marketking-ni-task-c:before { content: ""; }

.marketking-ni-todo-fill:before { content: ""; }

.marketking-ni-note-add-fill-c:before { content: ""; }

.marketking-ni-task-fill-c:before { content: ""; }

.marketking-ni-scan-fill:before { content: ""; }

.marketking-ni-scan:before { content: ""; }

.marketking-ni-qr:before { content: ""; }

.marketking-ni-money:before { content: ""; }

.marketking-ni-coins:before { content: ""; }

.marketking-ni-coin:before { content: ""; }

.marketking-ni-coin-alt:before { content: ""; }

.marketking-ni-coin-alt-fill:before { content: ""; }

.marketking-ni-setting-alt-fill:before { content: ""; }

.marketking-ni-setting-alt:before { content: ""; }

.marketking-ni-setting-fill:before { content: ""; }

.marketking-ni-setting:before { content: ""; }

.marketking-ni-share-alt:before { content: ""; }

.marketking-ni-share-fill:before { content: ""; }

.marketking-ni-share:before { content: ""; }

.marketking-ni-network:before { content: ""; }

.marketking-ni-rss:before { content: ""; }

.marketking-ni-shield:before { content: ""; }

.marketking-ni-shield-star:before { content: ""; }

.marketking-ni-shield-check:before { content: ""; }

.marketking-ni-shield-alert:before { content: ""; }

.marketking-ni-shield-off:before { content: ""; }

.marketking-ni-security:before { content: ""; }

.marketking-ni-policy:before { content: ""; }

.marketking-ni-shield-alert-fill:before { content: ""; }

.marketking-ni-shield-check-fill:before { content: ""; }

.marketking-ni-shield-fill:before { content: ""; }

.marketking-ni-shield-half:before { content: ""; }

.marketking-ni-shield-star-fill:before { content: ""; }

.marketking-ni-policy-fill:before { content: ""; }

.marketking-ni-spark:before { content: ""; }

.marketking-ni-spark-off:before { content: ""; }

.marketking-ni-spark-fill:before { content: ""; }

.marketking-ni-spark-off-fill:before { content: ""; }

.marketking-ni-wallet:before { content: ""; }

.marketking-ni-wallet-alt:before { content: ""; }

.marketking-ni-wallet-in:before { content: ""; }

.marketking-ni-wallet-out:before { content: ""; }

.marketking-ni-wallet-saving:before { content: ""; }

.marketking-ni-wallet-fill:before { content: ""; }

.marketking-ni-star:before { content: ""; }

.marketking-ni-star-half:before { content: ""; }

.marketking-ni-star-half-fill:before { content: ""; }

.marketking-ni-star-fill:before { content: ""; }

.marketking-ni-star-round:before { content: ""; }

.marketking-ni-heart:before { content: ""; }

.marketking-ni-heart-fill:before { content: ""; }

.marketking-ni-swap-alt-fill:before { content: ""; }

.marketking-ni-swap-alt:before { content: ""; }

.marketking-ni-thumbs-down:before { content: ""; }

.marketking-ni-thumbs-up:before { content: ""; }

.marketking-ni-tag:before { content: ""; }

.marketking-ni-tag-alt:before { content: ""; }

.marketking-ni-tags:before { content: ""; }

.marketking-ni-tag-fill:before { content: ""; }

.marketking-ni-tag-alt-fill:before { content: ""; }

.marketking-ni-tags-fill:before { content: ""; }

.marketking-ni-bookmark:before { content: ""; }

.marketking-ni-bookmark-fill:before { content: ""; }

.marketking-ni-label:before { content: ""; }

.marketking-ni-label-fill:before { content: ""; }

.marketking-ni-priority:before { content: ""; }

.marketking-ni-piority:before { content: ""; }

.marketking-ni-priority-fill:before { content: ""; }

.marketking-ni-piority-fill:before { content: ""; }

.marketking-ni-label-alt:before { content: ""; }

.marketking-ni-label-alt-fill:before { content: ""; }

.marketking-ni-ticket-alt:before { content: ""; }

.marketking-ni-ticket:before { content: ""; }

.marketking-ni-ticket-minus:before { content: ""; }

.marketking-ni-ticket-plus:before { content: ""; }

.marketking-ni-ticket-alt-fill:before { content: ""; }

.marketking-ni-ticket-fill:before { content: ""; }

.marketking-ni-ticket-minus-fill:before { content: ""; }

.marketking-ni-ticket-plus-fill:before { content: ""; }

.marketking-ni-toggle-off:before { content: ""; }

.marketking-ni-toggle-on:before { content: ""; }

.marketking-ni-trash-alt:before { content: ""; }

.marketking-ni-trash-empty:before { content: ""; }

.marketking-ni-trash:before { content: ""; }

.marketking-ni-trash-fill:before { content: ""; }

.marketking-ni-trash-empty-fill:before { content: ""; }

.marketking-ni-delete-fill:before { content: ""; }

.marketking-ni-delete:before { content: ""; }

.marketking-ni-alarm-alt:before { content: ""; }

.marketking-ni-alarm:before { content: ""; }

.marketking-ni-bugs:before { content: ""; }

.marketking-ni-building:before { content: ""; }

.marketking-ni-building-fill:before { content: ""; }

.marketking-ni-headphone:before { content: ""; }

.marketking-ni-headphone-fill:before { content: ""; }

.marketking-ni-aperture:before { content: ""; }

.marketking-ni-help-alt:before { content: ""; }

.marketking-ni-award:before { content: ""; }

.marketking-ni-briefcase:before { content: ""; }

.marketking-ni-gift:before { content: ""; }

.marketking-ni-globe:before { content: ""; }

.marketking-ni-umbrela:before { content: ""; }

.marketking-ni-truck:before { content: ""; }

.marketking-ni-sign-usd:before { content: ""; }

.marketking-ni-sign-dollar:before { content: ""; }

.marketking-ni-sign-mxn:before { content: ""; }

.marketking-ni-sign-sgd:before { content: ""; }

.marketking-ni-sign-euro:before { content: ""; }

.marketking-ni-sign-eur:before { content: ""; }

.marketking-ni-sign-gbp:before { content: ""; }

.marketking-ni-sign-pound:before { content: ""; }

.marketking-ni-sign-thb:before { content: ""; }

.marketking-ni-sign-inr:before { content: ""; }

.marketking-ni-sign-jpy:before { content: ""; }

.marketking-ni-sign-yen:before { content: ""; }

.marketking-ni-sign-cny:before { content: ""; }

.marketking-ni-sign-kobo:before { content: ""; }

.marketking-ni-sign-chf:before { content: ""; }

.marketking-ni-sign-vnd:before { content: ""; }

.marketking-ni-sign-php:before { content: ""; }

.marketking-ni-sign-brl:before { content: ""; }

.marketking-ni-sign-idr:before { content: ""; }

.marketking-ni-sign-czk:before { content: ""; }

.marketking-ni-sign-hkd:before { content: ""; }

.marketking-ni-sign-kr:before { content: ""; }

.marketking-ni-sign-dkk:before { content: ""; }

.marketking-ni-sign-nok:before { content: ""; }

.marketking-ni-sign-sek:before { content: ""; }

.marketking-ni-sign-rub:before { content: ""; }

.marketking-ni-sign-myr:before { content: ""; }

.marketking-ni-sign-pln:before { content: ""; }

.marketking-ni-sign-try:before { content: ""; }

.marketking-ni-sign-waves:before { content: ""; }

.marketking-ni-waves:before { content: ""; }

.marketking-ni-sign-trx:before { content: ""; }

.marketking-ni-tron:before { content: ""; }

.marketking-ni-sign-xem:before { content: ""; }

.marketking-ni-nem:before { content: ""; }

.marketking-ni-sign-mxr:before { content: ""; }

.marketking-ni-monero:before { content: ""; }

.marketking-ni-sign-usdc:before { content: ""; }

.marketking-ni-sign-steller:before { content: ""; }

.marketking-ni-sign-steem:before { content: ""; }

.marketking-ni-sign-usdt:before { content: ""; }

.marketking-ni-tether:before { content: ""; }

.marketking-ni-sign-btc:before { content: ""; }

.marketking-ni-bitcoin:before { content: ""; }

.marketking-ni-sign-bch:before { content: ""; }

.marketking-ni-bitcoin-cash:before { content: ""; }

.marketking-ni-sign-bnb:before { content: ""; }

.marketking-ni-binance:before { content: ""; }

.marketking-ni-sign-ada:before { content: ""; }

.marketking-ni-sign-zcash:before { content: ""; }

.marketking-ni-sign-eth:before { content: ""; }

.marketking-ni-ethereum:before { content: ""; }

.marketking-ni-sign-dash:before { content: ""; }

.marketking-ni-dash:before { content: ""; }

.marketking-ni-sign-xrp-old:before { content: ""; }

.marketking-ni-ripple-old:before { content: ""; }

.marketking-ni-sign-eos:before { content: ""; }

.marketking-ni-eos:before { content: ""; }

.marketking-ni-sign-xrp:before { content: ""; }

.marketking-ni-ripple:before { content: ""; }

.marketking-ni-american-express:before { content: ""; }

.marketking-ni-jcb:before { content: ""; }

.marketking-ni-cc-jcb:before { content: ""; }

.marketking-ni-mc:before { content: ""; }

.marketking-ni-cc-mc:before { content: ""; }

.marketking-ni-discover:before { content: ""; }

.marketking-ni-cc-discover:before { content: ""; }

.marketking-ni-visa:before { content: ""; }

.marketking-ni-cc-visa:before { content: ""; }

.marketking-ni-cc-paypal:before { content: ""; }

.marketking-ni-cc-stripe:before { content: ""; }

.marketking-ni-amazon-pay:before { content: ""; }

.marketking-ni-amazon-pay-fill:before { content: ""; }

.marketking-ni-google-pay:before { content: ""; }

.marketking-ni-google-pay-fill:before { content: ""; }

.marketking-ni-apple-pay:before { content: ""; }

.marketking-ni-apple-pay-fill:before { content: ""; }

.marketking-ni-angular:before { content: ""; }

.marketking-ni-react:before { content: ""; }

.marketking-ni-laravel:before { content: ""; }

.marketking-ni-html5:before { content: ""; }

.marketking-ni-css3-fill:before { content: ""; }

.marketking-ni-css3:before { content: ""; }

.marketking-ni-js:before { content: ""; }

.marketking-ni-php:before { content: ""; }

.marketking-ni-python:before { content: ""; }

.marketking-ni-bootstrap:before { content: ""; }

.marketking-ni-ebay:before { content: ""; }

.marketking-ni-google-wallet:before { content: ""; }

.marketking-ni-google-drive:before { content: ""; }

.marketking-ni-google-play-store:before { content: ""; }

.marketking-ni-android:before { content: ""; }

.marketking-ni-blogger-fill:before { content: ""; }

.marketking-ni-blogger:before { content: ""; }

.marketking-ni-hangout:before { content: ""; }

.marketking-ni-apple-store:before { content: ""; }

.marketking-ni-apple-store-ios:before { content: ""; }

.marketking-ni-stripe:before { content: ""; }

.marketking-ni-apple:before { content: ""; }

.marketking-ni-microsoft:before { content: ""; }

.marketking-ni-windows:before { content: ""; }

.marketking-ni-amazon:before { content: ""; }

.marketking-ni-paypal-alt:before { content: ""; }

.marketking-ni-airbnb:before { content: ""; }

.marketking-ni-adobe:before { content: ""; }

.marketking-ni-mailchimp:before { content: ""; }

.marketking-ni-dropbox:before { content: ""; }

.marketking-ni-digital-ocean:before { content: ""; }

.marketking-ni-slack:before { content: ""; }

.marketking-ni-slack-hash:before { content: ""; }

.marketking-ni-stack-overflow:before { content: ""; }

.marketking-ni-soundcloud:before { content: ""; }

.marketking-ni-blackberry:before { content: ""; }

.marketking-ni-spotify:before { content: ""; }

.marketking-ni-kickstarter:before { content: ""; }

.marketking-ni-houzz:before { content: ""; }

.marketking-ni-vine:before { content: ""; }

.marketking-ni-yelp:before { content: ""; }

.marketking-ni-yoast:before { content: ""; }

.marketking-ni-envato:before { content: ""; }

.marketking-ni-wordpress:before { content: ""; }

.marketking-ni-wp:before { content: ""; }

.marketking-ni-wordpress-fill:before { content: ""; }

.marketking-ni-elementor:before { content: ""; }

.marketking-ni-joomla:before { content: ""; }

.marketking-ni-megento:before { content: ""; }

.marketking-ni-git:before { content: ""; }

.marketking-ni-github:before { content: ""; }

.marketking-ni-github-round:before { content: ""; }

.marketking-ni-github-circle:before { content: ""; }

.marketking-ni-dribbble:before { content: ""; }

.marketking-ni-dribbble-round:before { content: ""; }

.marketking-ni-behance:before { content: ""; }

.marketking-ni-behance-fill:before { content: ""; }

.marketking-ni-flickr:before { content: ""; }

.marketking-ni-flickr-round:before { content: ""; }

.marketking-ni-medium:before { content: ""; }

.marketking-ni-medium-round:before { content: ""; }

.marketking-ni-reddit:before { content: ""; }

.marketking-ni-reddit-round:before { content: ""; }

.marketking-ni-reddit-circle:before { content: ""; }

.marketking-ni-google:before { content: ""; }

.marketking-ni-facebook-f:before { content: ""; }

.marketking-ni-facebook-fill:before { content: ""; }

.marketking-ni-facebook-circle:before { content: ""; }

.marketking-ni-instagram:before { content: ""; }

.marketking-ni-instagram-round:before { content: ""; }

.marketking-ni-linkedin:before { content: ""; }

.marketking-ni-linkedin-round:before { content: ""; }

.marketking-ni-twitter:before { content: ""; }

.marketking-ni-twitter-round:before { content: ""; }

.marketking-ni-pinterest:before { content: ""; }

.marketking-ni-pinterest-round:before { content: ""; }

.marketking-ni-pinterest-circle:before { content: ""; }

.marketking-ni-tumblr:before { content: ""; }

.marketking-ni-tumblr-round:before { content: ""; }

.marketking-ni-skype:before { content: ""; }

.marketking-ni-viber:before { content: ""; }

.marketking-ni-whatsapp:before { content: ""; }

.marketking-ni-whatsapp-round:before { content: ""; }

.marketking-ni-snapchat:before { content: ""; }

.marketking-ni-snapchat-fill:before { content: ""; }

.marketking-ni-telegram:before { content: ""; }

.marketking-ni-telegram-circle:before { content: ""; }

.marketking-ni-youtube-line:before { content: ""; }

.marketking-ni-youtube:before { content: ""; }

.marketking-ni-youtube-fill:before { content: ""; }

.marketking-ni-youtube-round:before { content: ""; }

.marketking-ni-vimeo:before { content: ""; }

.marketking-ni-vimeo-fill:before { content: ""; }


.marketking-btn, .dual-listbox .dual-listbox__button { text-decoration:none;display: inline-block; font-family: "DM Sans", sans-serif; font-weight: 700; color: #526484; text-align: center; vertical-align: middle; user-select: none; background-color: transparent; border: 1px solid transparent; padding: 0.4375rem 1.125rem; font-size: 0.8125rem; line-height: 1.25rem; border-radius: 4px; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }

@media (prefers-reduced-motion: reduce) { .marketking-btn, .dual-listbox .dual-listbox__button { transition: none; } }

.marketking-btn:hover, .dual-listbox .dual-listbox__button:hover { color: #526484; text-decoration: none; }

.marketking-btn:focus, .dual-listbox .dual-listbox__button:focus, .marketking-btn.focus, .dual-listbox .focus.dual-listbox__button { outline: 0; box-shadow: 0 0 0 3px rgba(133, 79, 255, 0.1); }

.marketking-btn.disabled, .dual-listbox .disabled.dual-listbox__button, .marketking-btn:disabled, .dual-listbox .dual-listbox__button:disabled { opacity: 0.5; }

.marketking-btn:not(:disabled):not(.disabled), .dual-listbox .dual-listbox__button:not(:disabled):not(.disabled) { cursor: pointer; }

a.marketking-btn.disabled, .dual-listbox a.disabled.dual-listbox__button, fieldset:disabled a.marketking-btn, fieldset:disabled .dual-listbox a.dual-listbox__button, .dual-listbox fieldset:disabled a.dual-listbox__button { pointer-events: none; }

.marketking-btn-primary { color: #fff; background-color: #854fff; border-color: #854fff; }

.marketking-btn-primary:hover { color: #fff; background-color: #6a29ff; border-color: #621cff; }

.marketking-btn-primary:focus, .marketking-btn-primary.focus { color: #fff; background-color: #6a29ff; border-color: #621cff; box-shadow: 0 0 0 0.2rem rgba(151, 105, 255, 0.5); }

.marketking-btn-primary.disabled, .marketking-btn-primary:disabled { color: #fff; background-color: #854fff; border-color: #854fff; }

.marketking-btn-primary:not(:disabled):not(.disabled):active, .marketking-btn-primary:not(:disabled):not(.disabled).active, .show > .marketking-btn-primary.dropdown-toggle { color: #fff; background-color: #621cff; border-color: #590fff; }

.marketking-btn-primary:not(:disabled):not(.disabled):active:focus, .marketking-btn-primary:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-primary.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(151, 105, 255, 0.5); }

.marketking-btn-secondary { color: #fff; background-color: #364a63; border-color: #364a63; }

.marketking-btn-secondary:hover { color: #fff; background-color: #29384a; border-color: #243142; }

.marketking-btn-secondary:focus, .marketking-btn-secondary.focus { color: #fff; background-color: #29384a; border-color: #243142; box-shadow: 0 0 0 0.2rem rgba(84, 101, 122, 0.5); }

.marketking-btn-secondary.disabled, .marketking-btn-secondary:disabled { color: #fff; background-color: #364a63; border-color: #364a63; }

.marketking-btn-secondary:not(:disabled):not(.disabled):active, .marketking-btn-secondary:not(:disabled):not(.disabled).active, .show > .marketking-btn-secondary.dropdown-toggle { color: #fff; background-color: #243142; border-color: #202b3a; }

.marketking-btn-secondary:not(:disabled):not(.disabled):active:focus, .marketking-btn-secondary:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-secondary.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(84, 101, 122, 0.5); }

.marketking-btn-success { color: #fff; background-color: #1ee0ac; border-color: #1ee0ac; }

.marketking-btn-success:hover { color: #fff; background-color: #19be92; border-color: #18b389; }

.marketking-btn-success:focus, .marketking-btn-success.focus { color: #fff; background-color: #19be92; border-color: #18b389; box-shadow: 0 0 0 0.2rem rgba(64, 229, 184, 0.5); }

.marketking-btn-success.disabled, .marketking-btn-success:disabled { color: #fff; background-color: #1ee0ac; border-color: #1ee0ac; }

.marketking-btn-success:not(:disabled):not(.disabled):active, .marketking-btn-success:not(:disabled):not(.disabled).active, .show > .marketking-btn-success.dropdown-toggle { color: #fff; background-color: #18b389; border-color: #16a881; }

.marketking-btn-success:not(:disabled):not(.disabled):active:focus, .marketking-btn-success:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-success.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(64, 229, 184, 0.5); }

.marketking-btn-info { color: #fff; background-color: #09c2de; border-color: #09c2de; }

.marketking-btn-info:hover { color: #fff; background-color: #08a2b9; border-color: #0797ad; }

.marketking-btn-info:focus, .marketking-btn-info.focus { color: #fff; background-color: #08a2b9; border-color: #0797ad; box-shadow: 0 0 0 0.2rem rgba(46, 203, 227, 0.5); }

.marketking-btn-info.disabled, .marketking-btn-info:disabled { color: #fff; background-color: #09c2de; border-color: #09c2de; }

.marketking-btn-info:not(:disabled):not(.disabled):active, .marketking-btn-info:not(:disabled):not(.disabled).active, .show > .marketking-btn-info.dropdown-toggle { color: #fff; background-color: #0797ad; border-color: #078ca1; }

.marketking-btn-info:not(:disabled):not(.disabled):active:focus, .marketking-btn-info:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-info.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(46, 203, 227, 0.5); }

.marketking-btn-warning { color: #fff; background-color: #f4bd0e; border-color: #f4bd0e; }

.marketking-btn-warning:hover { color: #fff; background-color: #d2a20a; border-color: #c69909; }

.marketking-btn-warning:focus, .marketking-btn-warning.focus { color: #fff; background-color: #d2a20a; border-color: #c69909; box-shadow: 0 0 0 0.2rem rgba(246, 199, 50, 0.5); }

.marketking-btn-warning.disabled, .marketking-btn-warning:disabled { color: #fff; background-color: #f4bd0e; border-color: #f4bd0e; }

.marketking-btn-warning:not(:disabled):not(.disabled):active, .marketking-btn-warning:not(:disabled):not(.disabled).active, .show > .marketking-btn-warning.dropdown-toggle { color: #fff; background-color: #c69909; border-color: #ba8f08; }

.marketking-btn-warning:not(:disabled):not(.disabled):active:focus, .marketking-btn-warning:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-warning.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(246, 199, 50, 0.5); }

.marketking-btn-danger { color: #fff; background-color: #e85347; border-color: #e85347; }

.marketking-btn-danger:hover { color: #fff; background-color: #e43325; border-color: #e02b1c; }

.marketking-btn-danger:focus, .marketking-btn-danger.focus { color: #fff; background-color: #e43325; border-color: #e02b1c; box-shadow: 0 0 0 0.2rem rgba(235, 109, 99, 0.5); }

.marketking-btn-danger.disabled, .marketking-btn-danger:disabled { color: #fff; background-color: #e85347; border-color: #e85347; }

.marketking-btn-danger:not(:disabled):not(.disabled):active, .marketking-btn-danger:not(:disabled):not(.disabled).active, .show > .marketking-btn-danger.dropdown-toggle { color: #fff; background-color: #e02b1c; border-color: #d5281b; }

.marketking-btn-danger:not(:disabled):not(.disabled):active:focus, .marketking-btn-danger:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-danger.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(235, 109, 99, 0.5); }

.marketking-btn-dark { color: #fff; background-color: #1c2b46; border-color: #1c2b46; }

.marketking-btn-dark:hover { color: #fff; background-color: #111a2b; border-color: #0d1522; }

.marketking-btn-dark:focus, .marketking-btn-dark.focus { color: #fff; background-color: #111a2b; border-color: #0d1522; box-shadow: 0 0 0 0.2rem rgba(62, 75, 98, 0.5); }

.marketking-btn-dark.disabled, .marketking-btn-dark:disabled { color: #fff; background-color: #1c2b46; border-color: #1c2b46; }

.marketking-btn-dark:not(:disabled):not(.disabled):active, .marketking-btn-dark:not(:disabled):not(.disabled).active, .show > .marketking-btn-dark.dropdown-toggle { color: #fff; background-color: #0d1522; border-color: #0a0f18; }

.marketking-btn-dark:not(:disabled):not(.disabled):active:focus, .marketking-btn-dark:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-dark.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(62, 75, 98, 0.5); }

.marketking-btn-light-blue { color: #fff; background-color: #96b9d6; border-color: #96b9d6; }

.marketking-btn-light-blue:hover { color: #fff; background-color: #67a4d6; border-color: #67a4d6; }

.marketking-btn-gray { color: #fff; background-color: #8091a7; border-color: #8091a7; }

.marketking-btn-gray:hover { color: #fff; background-color: #697d97; border-color: #647790; }

.marketking-btn-gray:focus, .marketking-btn-gray.focus { color: #fff; background-color: #697d97; border-color: #647790; box-shadow: 0 0 0 0.2rem rgba(147, 162, 180, 0.5); }

.marketking-btn-gray.disabled, .marketking-btn-gray:disabled { color: #fff; background-color: #8091a7; border-color: #8091a7; }

.marketking-btn-gray:not(:disabled):not(.disabled):active, .marketking-btn-gray:not(:disabled):not(.disabled).active, .show > .marketking-btn-gray.dropdown-toggle { color: #fff; background-color: #647790; border-color: #5f7189; }

.marketking-btn-gray:not(:disabled):not(.disabled):active:focus, .marketking-btn-gray:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-gray.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(147, 162, 180, 0.5); }

.marketking-btn-light, .dual-listbox .dual-listbox__button { color: #101924; background-color: #e5e9f2; border-color: #e5e9f2; }

.marketking-btn-light:hover, .dual-listbox .dual-listbox__button:hover { color: #101924; background-color: #ccd3e5; border-color: #c3cce1; }

.marketking-btn-light:focus, .dual-listbox .dual-listbox__button:focus, .marketking-btn-light.focus, .dual-listbox .focus.dual-listbox__button { color: #101924; background-color: #ccd3e5; border-color: #c3cce1; box-shadow: 0 0 0 0.2rem rgba(197, 202, 211, 0.5); }

.marketking-btn-light.disabled, .dual-listbox .disabled.dual-listbox__button, .marketking-btn-light:disabled, .dual-listbox .dual-listbox__button:disabled { color: #101924; background-color: #e5e9f2; border-color: #e5e9f2; }

.marketking-btn-light:not(:disabled):not(.disabled):active, .dual-listbox .dual-listbox__button:not(:disabled):not(.disabled):active, .marketking-btn-light:not(:disabled):not(.disabled).active, .dual-listbox .dual-listbox__button:not(:disabled):not(.disabled).active, .show > .marketking-btn-light.dropdown-toggle, .dual-listbox .show > .dropdown-toggle.dual-listbox__button { color: #101924; background-color: #c3cce1; border-color: #bbc5dd; }

.marketking-btn-light:not(:disabled):not(.disabled):active:focus, .dual-listbox .dual-listbox__button:not(:disabled):not(.disabled):active:focus, .marketking-btn-light:not(:disabled):not(.disabled).active:focus, .dual-listbox .dual-listbox__button:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-light.dropdown-toggle:focus, .dual-listbox .show > .dropdown-toggle.dual-listbox__button:focus { box-shadow: 0 0 0 0.2rem rgba(197, 202, 211, 0.5); }

.marketking-btn-lighter { color: #101924; background-color: #f5f6fa; border-color: #f5f6fa; }

.marketking-btn-lighter:hover { color: #101924; background-color: #dcdfed; border-color: #d3d7e9; }

.marketking-btn-lighter:focus, .marketking-btn-lighter.focus { color: #101924; background-color: #dcdfed; border-color: #d3d7e9; box-shadow: 0 0 0 0.2rem rgba(211, 213, 218, 0.5); }

.marketking-btn-lighter.disabled, .marketking-btn-lighter:disabled { color: #101924; background-color: #f5f6fa; border-color: #f5f6fa; }

.marketking-btn-lighter:not(:disabled):not(.disabled):active, .marketking-btn-lighter:not(:disabled):not(.disabled).active, .show > .marketking-btn-lighter.dropdown-toggle { color: #101924; background-color: #d3d7e9; border-color: #cbd0e5; }

.marketking-btn-lighter:not(:disabled):not(.disabled):active:focus, .marketking-btn-lighter:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-lighter.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(211, 213, 218, 0.5); }

.marketking-btn-outline-primary { color: #854fff; border-color: #854fff; }

.marketking-btn-outline-primary:hover { color: #fff; background-color: #854fff; border-color: #854fff; }

.marketking-btn-outline-primary:focus, .marketking-btn-outline-primary.focus { box-shadow: 0 0 0 0.2rem rgba(133, 79, 255, 0.5); }

.marketking-btn-outline-primary.disabled, .marketking-btn-outline-primary:disabled { color: #854fff; background-color: transparent; }

.marketking-btn-outline-primary:not(:disabled):not(.disabled):active, .marketking-btn-outline-primary:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-primary.dropdown-toggle { color: #fff; background-color: #854fff; border-color: #854fff; }

.marketking-btn-outline-primary:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-primary.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(133, 79, 255, 0.5); }

.marketking-btn-outline-secondary { color: #364a63; border-color: #364a63; }

.marketking-btn-outline-secondary:hover { color: #fff; background-color: #364a63; border-color: #364a63; }

.marketking-btn-outline-secondary:focus, .marketking-btn-outline-secondary.focus { box-shadow: 0 0 0 0.2rem rgba(54, 74, 99, 0.5); }

.marketking-btn-outline-secondary.disabled, .marketking-btn-outline-secondary:disabled { color: #364a63; background-color: transparent; }

.marketking-btn-outline-secondary:not(:disabled):not(.disabled):active, .marketking-btn-outline-secondary:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-secondary.dropdown-toggle { color: #fff; background-color: #364a63; border-color: #364a63; }

.marketking-btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-secondary.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(54, 74, 99, 0.5); }

.marketking-btn-outline-success { color: #1ee0ac; border-color: #1ee0ac; }

.marketking-btn-outline-success:hover { color: #fff; background-color: #1ee0ac; border-color: #1ee0ac; }

.marketking-btn-outline-success:focus, .marketking-btn-outline-success.focus { box-shadow: 0 0 0 0.2rem rgba(30, 224, 172, 0.5); }

.marketking-btn-outline-success.disabled, .marketking-btn-outline-success:disabled { color: #1ee0ac; background-color: transparent; }

.marketking-btn-outline-success:not(:disabled):not(.disabled):active, .marketking-btn-outline-success:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-success.dropdown-toggle { color: #fff; background-color: #1ee0ac; border-color: #1ee0ac; }

.marketking-btn-outline-success:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-success:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-success.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(30, 224, 172, 0.5); }

.marketking-btn-outline-info { color: #09c2de; border-color: #09c2de; }

.marketking-btn-outline-info:hover { color: #fff; background-color: #09c2de; border-color: #09c2de; }

.marketking-btn-outline-info:focus, .marketking-btn-outline-info.focus { box-shadow: 0 0 0 0.2rem rgba(9, 194, 222, 0.5); }

.marketking-btn-outline-info.disabled, .marketking-btn-outline-info:disabled { color: #09c2de; background-color: transparent; }

.marketking-btn-outline-info:not(:disabled):not(.disabled):active, .marketking-btn-outline-info:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-info.dropdown-toggle { color: #fff; background-color: #09c2de; border-color: #09c2de; }

.marketking-btn-outline-info:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-info:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-info.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(9, 194, 222, 0.5); }

.marketking-btn-outline-warning { color: #f4bd0e; border-color: #f4bd0e; }

.marketking-btn-outline-warning:hover { color: #fff; background-color: #f4bd0e; border-color: #f4bd0e; }

.marketking-btn-outline-warning:focus, .marketking-btn-outline-warning.focus { box-shadow: 0 0 0 0.2rem rgba(244, 189, 14, 0.5); }

.marketking-btn-outline-warning.disabled, .marketking-btn-outline-warning:disabled { color: #f4bd0e; background-color: transparent; }

.marketking-btn-outline-warning:not(:disabled):not(.disabled):active, .marketking-btn-outline-warning:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-warning.dropdown-toggle { color: #fff; background-color: #f4bd0e; border-color: #f4bd0e; }

.marketking-btn-outline-warning:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-warning:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-warning.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(244, 189, 14, 0.5); }

.marketking-btn-outline-danger { color: #e85347; border-color: #e85347; }

.marketking-btn-outline-danger:hover { color: #fff; background-color: #e85347; border-color: #e85347; }

.marketking-btn-outline-danger:focus, .marketking-btn-outline-danger.focus { box-shadow: 0 0 0 0.2rem rgba(232, 83, 71, 0.5); }

.marketking-btn-outline-danger.disabled, .marketking-btn-outline-danger:disabled { color: #e85347; background-color: transparent; }

.marketking-btn-outline-danger:not(:disabled):not(.disabled):active, .marketking-btn-outline-danger:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-danger.dropdown-toggle { color: #fff; background-color: #e85347; border-color: #e85347; }

.marketking-btn-outline-danger:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-danger:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-danger.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(232, 83, 71, 0.5); }

.marketking-btn-outline-dark { color: #1c2b46; border-color: #1c2b46; }

.marketking-btn-outline-dark:hover { color: #fff; background-color: #1c2b46; border-color: #1c2b46; }

.marketking-btn-outline-dark:focus, .marketking-btn-outline-dark.focus { box-shadow: 0 0 0 0.2rem rgba(28, 43, 70, 0.5); }

.marketking-btn-outline-dark.disabled, .marketking-btn-outline-dark:disabled { color: #1c2b46; background-color: transparent; }

.marketking-btn-outline-dark:not(:disabled):not(.disabled):active, .marketking-btn-outline-dark:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-dark.dropdown-toggle { color: #fff; background-color: #1c2b46; border-color: #1c2b46; }

.marketking-btn-outline-dark:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-dark:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-dark.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(28, 43, 70, 0.5); }

.marketking-btn-outline-gray { color: #8091a7; border-color: #8091a7; }

.marketking-btn-outline-gray:hover { color: #fff; background-color: #8091a7; border-color: #8091a7; }

.marketking-btn-outline-gray:focus, .marketking-btn-outline-gray.focus { box-shadow: 0 0 0 0.2rem rgba(128, 145, 167, 0.5); }

.marketking-btn-outline-gray.disabled, .marketking-btn-outline-gray:disabled { color: #8091a7; background-color: transparent; }

.marketking-btn-outline-gray:not(:disabled):not(.disabled):active, .marketking-btn-outline-gray:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-gray.dropdown-toggle { color: #fff; background-color: #8091a7; border-color: #8091a7; }

.marketking-btn-outline-gray:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-gray:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-gray.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(128, 145, 167, 0.5); }

.marketking-btn-outline-light { color: #e5e9f2; border-color: #e5e9f2; }

.marketking-btn-outline-light:hover { color: #101924; background-color: #e5e9f2; border-color: #e5e9f2; }

.marketking-btn-outline-light:focus, .marketking-btn-outline-light.focus { box-shadow: 0 0 0 0.2rem rgba(229, 233, 242, 0.5); }

.marketking-btn-outline-light.disabled, .marketking-btn-outline-light:disabled { color: #e5e9f2; background-color: transparent; }

.marketking-btn-outline-light:not(:disabled):not(.disabled):active, .marketking-btn-outline-light:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-light.dropdown-toggle { color: #101924; background-color: #e5e9f2; border-color: #e5e9f2; }

.marketking-btn-outline-light:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-light:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-light.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(229, 233, 242, 0.5); }

.marketking-btn-outline-lighter { color: #f5f6fa; border-color: #f5f6fa; }

.marketking-btn-outline-lighter:hover { color: #101924; background-color: #f5f6fa; border-color: #f5f6fa; }

.marketking-btn-outline-lighter:focus, .marketking-btn-outline-lighter.focus { box-shadow: 0 0 0 0.2rem rgba(245, 246, 250, 0.5); }

.marketking-btn-outline-lighter.disabled, .marketking-btn-outline-lighter:disabled { color: #f5f6fa; background-color: transparent; }

.marketking-btn-outline-lighter:not(:disabled):not(.disabled):active, .marketking-btn-outline-lighter:not(:disabled):not(.disabled).active, .show > .marketking-btn-outline-lighter.dropdown-toggle { color: #101924; background-color: #f5f6fa; border-color: #f5f6fa; }

.marketking-btn-outline-lighter:not(:disabled):not(.disabled):active:focus, .marketking-btn-outline-lighter:not(:disabled):not(.disabled).active:focus, .show > .marketking-btn-outline-lighter.dropdown-toggle:focus { box-shadow: 0 0 0 0.2rem rgba(245, 246, 250, 0.5); }

.marketking-btn-link { font-weight: 400; color: #9d72ff; text-decoration: none; }

.marketking-btn-link:hover { color: #7a3fff; text-decoration: underline; }

.marketking-btn-link:focus, .marketking-btn-link.focus { text-decoration: underline; }

.marketking-btn-link:disabled, .marketking-btn-link.disabled { color: #f5f6fa; pointer-events: none; }

.marketking-btn-lg, .marketking-btn-group-lg > .marketking-btn, .dual-listbox .marketking-btn-group-lg > .dual-listbox__button { padding: 0.6875rem 1.5rem; font-size: 0.9375rem; line-height: 1.25rem; border-radius: 5px; }

.marketking-btn-sm, .marketking-btn-group-sm > .marketking-btn, .dual-listbox .marketking-btn-group-sm > .dual-listbox__button { padding: 0.25rem 0.75rem; font-size: 0.75rem; line-height: 1.25rem; border-radius: 3px; }

.marketking-btn-block { display: block; width: 100%; }

.marketking-btn-block + .marketking-btn-block { margin-top: 0.5rem; }

input[type="submit"].marketking-btn-block, input[type="reset"].marketking-btn-block, input[type="button"].marketking-btn-block { width: 100%; }


.marketking-btn-group, .marketking-btn-group-vertical { position: relative; display: inline-flex; vertical-align: middle; }

.marketking-btn-group > .marketking-btn, .dual-listbox .marketking-btn-group > .dual-listbox__button, .marketking-btn-group-vertical > .marketking-btn, .dual-listbox .marketking-btn-group-vertical > .dual-listbox__button { position: relative; flex: 1 1 auto; }

.marketking-btn-group > .marketking-btn:hover, .dual-listbox .marketking-btn-group > .dual-listbox__button:hover, .marketking-btn-group-vertical > .marketking-btn:hover, .dual-listbox .marketking-btn-group-vertical > .dual-listbox__button:hover { z-index: 1; }

.marketking-btn-group > .marketking-btn:focus, .dual-listbox .marketking-btn-group > .dual-listbox__button:focus, .marketking-btn-group > .marketking-btn:active, .dual-listbox .marketking-btn-group > .dual-listbox__button:active, .marketking-btn-group > .marketking-btn.active, .dual-listbox .marketking-btn-group > .active.dual-listbox__button, .marketking-btn-group-vertical > .marketking-btn:focus, .dual-listbox .marketking-btn-group-vertical > .dual-listbox__button:focus, .marketking-btn-group-vertical > .marketking-btn:active, .dual-listbox .marketking-btn-group-vertical > .dual-listbox__button:active, .marketking-btn-group-vertical > .marketking-btn.active, .dual-listbox .marketking-btn-group-vertical > .active.dual-listbox__button { z-index: 1; }

.marketking-btn-toolbar { display: flex; flex-wrap: wrap; justify-content: flex-start; }

.marketking-btn-toolbar .input-group { width: auto; }

.marketking-btn-group > .marketking-btn:not(:first-child), .dual-listbox .marketking-btn-group > .dual-listbox__button:not(:first-child), .marketking-btn-group > .marketking-btn-group:not(:first-child) { margin-left: -1px; }

.marketking-btn-group > .marketking-btn:not(:last-child):not(.dropdown-toggle), .dual-listbox .marketking-btn-group > .dual-listbox__button:not(:last-child):not(.dropdown-toggle), .marketking-btn-group > .marketking-btn-group:not(:last-child) > .marketking-btn, .dual-listbox .marketking-btn-group > .marketking-btn-group:not(:last-child) > .dual-listbox__button { border-top-right-radius: 0; border-bottom-right-radius: 0; }

.marketking-btn-group > .marketking-btn:not(:first-child), .dual-listbox .marketking-btn-group > .dual-listbox__button:not(:first-child), .marketking-btn-group > .marketking-btn-group:not(:first-child) > .marketking-btn, .dual-listbox .marketking-btn-group > .marketking-btn-group:not(:first-child) > .dual-listbox__button { border-top-left-radius: 0; border-bottom-left-radius: 0; }


.marketking-btn-sm + .dropdown-toggle-split, .marketking-btn-group-sm > .marketking-btn + .dropdown-toggle-split, .dual-listbox .marketking-btn-group-sm > .dual-listbox__button + .dropdown-toggle-split { padding-right: 0.5625rem; padding-left: 0.5625rem; }

.marketking-btn-lg + .dropdown-toggle-split, .marketking-btn-group-lg > .marketking-btn + .dropdown-toggle-split, .dual-listbox .marketking-btn-group-lg > .dual-listbox__button + .dropdown-toggle-split { padding-right: 1.125rem; padding-left: 1.125rem; }

.marketking-btn-group-vertical { flex-direction: column; align-items: flex-start; justify-content: center; }

.marketking-btn-group-vertical > .marketking-btn, .dual-listbox .marketking-btn-group-vertical > .dual-listbox__button, .marketking-btn-group-vertical > .marketking-btn-group { width: 100%; }

.marketking-btn-group-vertical > .marketking-btn:not(:first-child), .dual-listbox .marketking-btn-group-vertical > .dual-listbox__button:not(:first-child), .marketking-btn-group-vertical > .marketking-btn-group:not(:first-child) { margin-top: -1px; }

.marketking-btn-group-vertical > .marketking-btn:not(:last-child):not(.dropdown-toggle), .dual-listbox .marketking-btn-group-vertical > .dual-listbox__button:not(:last-child):not(.dropdown-toggle), .marketking-btn-group-vertical > .marketking-btn-group:not(:last-child) > .marketking-btn, .dual-listbox .marketking-btn-group-vertical > .marketking-btn-group:not(:last-child) > .dual-listbox__button { border-bottom-right-radius: 0; border-bottom-left-radius: 0; }

.marketking-btn-group-vertical > .marketking-btn:not(:first-child), .dual-listbox .marketking-btn-group-vertical > .dual-listbox__button:not(:first-child), .marketking-btn-group-vertical > .marketking-btn-group:not(:first-child) > .marketking-btn, .dual-listbox .marketking-btn-group-vertical > .marketking-btn-group:not(:first-child) > .dual-listbox__button { border-top-left-radius: 0; border-top-right-radius: 0; }

.marketking-btn-group-toggle > .marketking-btn, .dual-listbox .marketking-btn-group-toggle > .dual-listbox__button, .marketking-btn-group-toggle > .marketking-btn-group > .marketking-btn, .dual-listbox .marketking-btn-group-toggle > .marketking-btn-group > .dual-listbox__button { margin-bottom: 0; }

.marketking-btn-group-toggle > .marketking-btn input[type="radio"], .dual-listbox .marketking-btn-group-toggle > .dual-listbox__button input[type="radio"], .marketking-btn-group-toggle > .marketking-btn input[type="checkbox"], .dual-listbox .marketking-btn-group-toggle > .dual-listbox__button input[type="checkbox"], .marketking-btn-group-toggle > .marketking-btn-group > .marketking-btn input[type="radio"], .dual-listbox .marketking-btn-group-toggle > .marketking-btn-group > .dual-listbox__button input[type="radio"], .marketking-btn-group-toggle > .marketking-btn-group > .marketking-btn input[type="checkbox"], .dual-listbox .marketking-btn-group-toggle > .marketking-btn-group > .dual-listbox__button input[type="checkbox"] { position: absolute; clip: rect(0, 0, 0, 0); pointer-events: none; }


.marketking-btn, .dual-listbox .dual-listbox__button { position: relative; letter-spacing: 0.02em; display: inline-flex; align-items: center; }

.marketking-btn-xl { padding: 0.6875rem 1.5rem; font-size: 0.9375rem; line-height: 1.25rem; border-radius: 5px; }

.marketking-btn-xs { padding: 0.125rem 0.5rem; font-size: 0.6875rem; line-height: 1rem; border-radius: 3px; }

.marketking-btn .icon, .dual-listbox .dual-listbox__button .icon { font-size: 1.4em; line-height: inherit; }

.marketking-btn > span, .dual-listbox .dual-listbox__button > span { display: inline-block; white-space: nowrap; }

.marketking-btn > span:only-child, .dual-listbox .dual-listbox__button > span:only-child { width: 100%; }

.marketking-btn .icon + span, .dual-listbox .dual-listbox__button .icon + span, .marketking-btn span + .icon, .dual-listbox .dual-listbox__button span + .icon { padding-left: 8px; }

.marketking-btn .dd-indc, .dual-listbox .dual-listbox__button .dd-indc { transform: translateX(-8px); }

.marketking-btn span + .dd-indc, .dual-listbox .dual-listbox__button span + .dd-indc { transform: translateX(8px); }

.marketking-btn-lg .icon + span, .marketking-btn-group-lg > .marketking-btn .icon + span, .dual-listbox .marketking-btn-group-lg > .dual-listbox__button .icon + span, .marketking-btn-lg span + .icon, .marketking-btn-group-lg > .marketking-btn span + .icon, .dual-listbox .marketking-btn-group-lg > .dual-listbox__button span + .icon { padding-left: 12px; }

.marketking-btn-round { border-radius: 2.125rem; }

.marketking-btn-block { justify-content: center; }

.marketking-btn-ucap, .marketking-btn.ucap, .dual-listbox .ucap.dual-listbox__button { text-transform: uppercase; font-size: 12px; letter-spacing: 0.05em; }

.marketking-btn-icon:not([class*="btn-icon-break"]) { padding-left: 0; padding-right: 0; }

.marketking-btn-icon .icon { width: 2.125rem; }

.marketking-btn-icon.marketking-btn-xl .icon { width: 2.625rem; }

.marketking-btn-icon.marketking-btn-lg .icon, .marketking-btn-group-lg > .marketking-btn-icon.marketking-btn .icon, .dual-listbox .marketking-btn-group-lg > .marketking-btn-icon.dual-listbox__button .icon { width: 2.625rem; }

.marketking-btn-icon.marketking-btn-sm .icon, .marketking-btn-group-sm > .marketking-btn-icon.marketking-btn .icon, .dual-listbox .marketking-btn-group-sm > .marketking-btn-icon.dual-listbox__button .icon { width: 1.75rem; }

.marketking-btn-icon.marketking-btn-xs .icon { width: 1.25rem; font-size: 1.1em; }

.marketking-btn-icon .dot { top: .35rem; position: absolute; right: .35rem; transform: translate(50%, -50%); }

.marketking-btn-icon .badge { top: .25rem; position: absolute; right: .25rem; transform: translate(50%, -50%); }

.marketking-btn-icon .badge-circle { border-radius: 50%; height: 1.125rem; width: 1.125rem; padding: 0; font-weight: 700; font-size: 11px; text-align: center; justify-content: center; }

.marketking-btn-mw { min-width: 120px; justify-content: center; }

.marketking-btn-wrap { flex-direction: column; align-items: center; flex-grow: 0; }

.marketking-btn-extext { font-size: 12px; font-weight: 500; text-align: center; margin-top: 0.25rem; color: #8094ae; }

.marketking-btn-wider { display: flex; }

.marketking-btn-wider .icon + span, .marketking-btn-wider span + .icon { margin-left: auto; }

.marketking-btn-auto { min-width: auto; }

.marketking-btn-pd-auto { padding-left: 0.25rem; padding-right: 0.25rem; }

.marketking-btn .spinner-border, .dual-listbox .dual-listbox__button .spinner-border, .marketking-btn .spinner-grow, .dual-listbox .dual-listbox__button .spinner-grow { margin: .125rem; }

.marketking-btn .spinner-border + span, .dual-listbox .dual-listbox__button .spinner-border + span, .marketking-btn .spinner-grow + span, .dual-listbox .dual-listbox__button .spinner-grow + span { margin-left: 0.25rem; }

.marketking-btn-indc { width: 100%; }

.marketking-btn-indc .icon { font-size: 1.43em; }

.marketking-btn-indc .indc { opacity: .6; margin-left: -8px; margin-right: auto; }

.marketking-btn-indc span + .indc { margin-left: auto; margin-right: -8px; }

@media (min-width: 768px) { .marketking-btn-xl { padding: 0.625rem 2rem; font-size: 1.125rem; line-height: 2rem; border-radius: 5px; }
  .marketking-btn-xl.marketking-btn-round { border-radius: 3.25rem; }
  .marketking-btn-icon.marketking-btn-xl .icon { width: 3.25rem; } }

.marketking-btn-trigger { position: relative; z-index: 1; color: #526484; }

.marketking-btn-trigger:focus { box-shadow: none; }

.marketking-btn-trigger:before { position: absolute; z-index: -1; height: 20px; width: 20px; top: 50%; left: 50%; transform: translate(-50%, -50%); transform-origin: 50% 50%; content: ''; background-color: #e5e9f2; border-radius: 50%; opacity: 0; transition: all .3s; }

.is-dark .marketking-btn-trigger:before { background-color: #26164b; }

.show > .marketking-btn-trigger:before { opacity: 1; height: 120%; width: 120%; }

.marketking-btn-trigger:hover:before, .marketking-btn-trigger:focus:before, .marketking-btn-trigger.active:not(.revarse):before { opacity: 1; height: 120%; width: 120%; }

.marketking-btn-trigger.active:hover:before { background-color: #dbdfea; }

a:hover .marketking-btn-trigger:before { opacity: 1; height: 120%; width: 120%; }

.marketking-btn-dim.marketking-btn-outline-primary { color: #854fff; background-color: #f2ecff; border-color: #ceb9ff; }

.marketking-btn-dim.marketking-btn-outline-primary:not(:disabled):not(.disabled):hover { color: #fff; background-color: #854fff; border-color: #854fff; }

.marketking-btn-white.marketking-btn-outline-primary:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-primary:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #854fff; background: #f2ecff; }

.marketking-btn-dim.marketking-btn-outline-success { color: #1ee0ac; background-color: #e6fcf6; border-color: #a5f3de; }

.marketking-btn-dim.marketking-btn-outline-success:not(:disabled):not(.disabled):hover { color: #fff; background-color: #1ee0ac; border-color: #1ee0ac; }

.marketking-btn-white.marketking-btn-outline-success:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-success:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #1ee0ac; background: #e6fcf6; }

.marketking-btn-dim.marketking-btn-outline-warning { color: #f4bd0e; background-color: #fef8e4; border-color: #fbe59f; }

.marketking-btn-dim.marketking-btn-outline-warning:not(:disabled):not(.disabled):hover { color: #fff; background-color: #f4bd0e; border-color: #f4bd0e; }

.marketking-btn-white.marketking-btn-outline-warning:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-warning:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #f4bd0e; background: #fef8e4; }

.marketking-btn-dim.marketking-btn-outline-info { color: #09c2de; background-color: #e4f8fb; border-color: #9de7f2; }

.marketking-btn-dim.marketking-btn-outline-info:not(:disabled):not(.disabled):hover { color: #fff; background-color: #09c2de; border-color: #09c2de; }

.marketking-btn-white.marketking-btn-outline-info:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-info:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #09c2de; background: #e4f8fb; }

.marketking-btn-dim.marketking-btn-outline-danger { color: #e85347; background-color: #fceceb; border-color: #f6bab5; }

.marketking-btn-dim.marketking-btn-outline-danger:not(:disabled):not(.disabled):hover { color: #fff; background-color: #e85347; border-color: #e85347; }

.marketking-btn-white.marketking-btn-outline-danger:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-danger:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #e85347; background: #fceceb; }

.marketking-btn-dim.marketking-btn-outline-secondary { color: #364a63; background-color: #e9ebee; border-color: #b9c0c8; }

.marketking-btn-dim.marketking-btn-outline-secondary:not(:disabled):not(.disabled):hover { color: #fff; background-color: #364a63; border-color: #364a63; }

.marketking-btn-white.marketking-btn-outline-secondary:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-secondary:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #364a63; background: #e9ebee; }

.marketking-btn-dim.marketking-btn-outline-gray { color: #3c4d62; background-color: #f1f3f5; border-color: #d3d9e0; }

.marketking-btn-dim.marketking-btn-outline-gray:not(:disabled):not(.disabled):hover { color: #fff; background-color: #3c4d62; border-color: #3c4d62; }

.marketking-btn-white.marketking-btn-outline-gray:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-gray:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #3c4d62; background: #f1f3f5; }

.marketking-btn-dim.marketking-btn-outline-dark { color: #1f2b3a; background-color: #e6e8e9; border-color: #b1b5ba; }

.marketking-btn-dim.marketking-btn-outline-dark:not(:disabled):not(.disabled):hover { color: #fff; background-color: #1f2b3a; border-color: #1f2b3a; }

.marketking-btn-white.marketking-btn-outline-dark:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-dark:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #1f2b3a; background: #e6e8e9; }

.marketking-btn-dim.marketking-btn-outline-light { color: #526484; background-color: #f5f6fa; border-color: #dbdfea; }

.marketking-btn-dim.marketking-btn-outline-light:not(:disabled):not(.disabled):hover { color: #fff; background-color: #526484; border-color: #526484; }

.marketking-btn-white.marketking-btn-outline-light:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover, .marketking-btn-trans.marketking-btn-outline-light:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #526484; background: #f5f6fa; }

.marketking-btn-dim.marketking-btn-primary { color: #854fff; background-color: #f2ecff; border-color: transparent; }

.marketking-btn-dim.marketking-btn-primary:not(:disabled):not(.disabled):hover { color: #fff; background-color: #854fff; border-color: #854fff; }

.marketking-btn-dim.marketking-btn-secondary { color: #364a63; background-color: #e9ebee; border-color: transparent; }

.marketking-btn-dim.marketking-btn-secondary:not(:disabled):not(.disabled):hover { color: #fff; background-color: #364a63; border-color: #364a63; }

.marketking-btn-dim.marketking-btn-success { color: #1ee0ac; background-color: #e6fcf6; border-color: transparent; }

.marketking-btn-dim.marketking-btn-success:not(:disabled):not(.disabled):hover { color: #fff; background-color: #1ee0ac; border-color: #1ee0ac; }

.marketking-btn-dim.marketking-btn-warning { color: #f4bd0e; background-color: #fef8e4; border-color: transparent; }

.marketking-btn-dim.marketking-btn-warning:not(:disabled):not(.disabled):hover { color: #fff; background-color: #f4bd0e; border-color: #f4bd0e; }

.marketking-btn-dim.marketking-btn-info { color: #09c2de; background-color: #e4f8fb; border-color: transparent; }

.marketking-btn-dim.marketking-btn-info:not(:disabled):not(.disabled):hover { color: #fff; background-color: #09c2de; border-color: #09c2de; }

.marketking-btn-dim.marketking-btn-danger { color: #e85347; background-color: #fceceb; border-color: transparent; }

.marketking-btn-dim.marketking-btn-danger:not(:disabled):not(.disabled):hover { color: #fff; background-color: #e85347; border-color: #e85347; }

.marketking-btn-dim.marketking-btn-gray { color: #3c4d62; background-color: #eaebee; border-color: transparent; }

.marketking-btn-dim.marketking-btn-gray:not(:disabled):not(.disabled):hover { color: #fff; background-color: #3c4d62; border-color: #3c4d62; }

.marketking-btn-dim.marketking-btn-dark { color: #1f2b3a; background-color: #e6e8e9; border-color: transparent; }

.marketking-btn-dim.marketking-btn-dark:not(:disabled):not(.disabled):hover { color: #fff; background-color: #1f2b3a; border-color: #1f2b3a; }

.marketking-btn-dim.marketking-btn-light, .dual-listbox .marketking-btn-dim.dual-listbox__button { color: #8091a7; background-color: #f1f3f5; border-color: transparent; }

.marketking-btn-dim.marketking-btn-light:not(:disabled):not(.disabled):hover, .dual-listbox .marketking-btn-dim.dual-listbox__button:not(:disabled):not(.disabled):hover { color: #fff; background-color: #8091a7; border-color: #8091a7; }

.marketking-btn-dim.marketking-btn-lighter { color: #b7c2d0; background-color: #f7f8fa; border-color: transparent; }

.marketking-btn-dim.marketking-btn-lighter:not(:disabled):not(.disabled):hover { color: #101924; background-color: #b7c2d0; border-color: #b7c2d0; }

.marketking-btn-trans.marketking-btn, .dual-listbox .marketking-btn-trans.dual-listbox__button { background-color: transparent; border-color: transparent; }

.marketking-btn-outline-light { border-color: #dbdfea; }

.marketking-btn-outline-lighter { border-color: #e5e9f2; }

.marketking-btn-outline-light, .marketking-btn-outline-lighter { color: #526484; }

.marketking-btn-white, .marketking-btn-white.marketking-btn-dim { background: #fff; }

.marketking-btn-white.marketking-btn-outline-light:not(.marketking-btn-dim):not(:disabled):not(.disabled):hover { color: #fff; background: #526484; border-color: #526484; }

.marketking-btn-toolbar-sep { border-right: 1px solid #e5e9f2; margin-left: .75rem; margin-right: .75rem; }

.card-tools .marketking-btn-toolbar-sep { padding: 0 !important; margin-left: .5rem; margin-right: .5rem; }

.marketking-btn-group.is-tags .marketking-btn-xs:first-child { padding-left: 0.25rem; padding-right: 0.25rem; }

.marketking-btn-group.is-tags .marketking-btn-xs:last-child { margin-left: 0; }

.marketking-btn-group.is-tags .marketking-btn-xs .icon { width: 1rem; }

.marketking-btn-group.is-tags .marketking-btn-dim:not(:disabled):not(.disabled):hover { border-color: transparent; }


.marketking-bg-blue { background-color: #559bfb !important; }

.marketking-text-blue { color: #559bfb !important; }

.marketking-bg-azure { background-color: #1676fb !important; }

.marketking-text-azure { color: #1676fb !important; }

.marketking-bg-indigo { background-color: #2c3782 !important; }

.marketking-text-indigo { color: #2c3782 !important; }

.marketking-bg-purple { background-color: #816bff !important; }

.marketking-text-purple { color: #816bff !important; }

.marketking-bg-pink { background-color: #ff63a5 !important; }

.marketking-text-pink { color: #ff63a5 !important; }

.marketking-bg-orange { background-color: #ffa353 !important; }

.marketking-text-orange { color: #ffa353 !important; }

.marketking-bg-teal { background-color: #20c997 !important; }

.marketking-text-teal { color: #20c997 !important; }

.marketking-bg-blue-dim { background-color: #e9f2fe !important; }

.marketking-text-blue-dim { color: #e9f2fe !important; }

.marketking-bg-azure-dim { background-color: #e1edfe !important; }

.marketking-text-azure-dim { color: #e1edfe !important; }

.marketking-bg-indigo-dim { background-color: #e4e5ef !important; }

.marketking-text-indigo-dim { color: #e4e5ef !important; }

.marketking-bg-purple-dim { background-color: #efecff !important; }

.marketking-text-purple-dim { color: #efecff !important; }

.marketking-bg-pink-dim { background-color: #ffebf3 !important; }

.marketking-text-pink-dim { color: #ffebf3 !important; }

.marketking-bg-orange-dim { background-color: #fff3e9 !important; }

.marketking-text-orange-dim { color: #fff3e9 !important; }

.marketking-bg-teal-dim { background-color: #e2f8f1 !important; }

.marketking-text-teal-dim { color: #e2f8f1 !important; }

.marketking-bg-primary-dim, .dual-listbox .dual-listbox__item:active, .dual-listbox .dual-listbox__item.dual-listbox__item--selected { background-color: #efe8ff !important; }

.marketking-text-primary-dim { color: #efe8ff !important; }

.marketking-bg-success-dim { background-color: #e2fbf4 !important; }

.marketking-text-success-dim { color: #e2fbf4 !important; }

.marketking-bg-info-dim { background-color: #dff7fb !important; }

.marketking-text-info-dim { color: #dff7fb !important; }

.marketking-bg-warning-dim { background-color: #fef6e0 !important; }

.marketking-text-warning-dim { color: #fef6e0 !important; }

.marketking-bg-danger-dim { background-color: #fce9e7 !important; }

.marketking-text-danger-dim { color: #fce9e7 !important; }

.marketking-bg-secondary-dim { background-color: #e5ecf5 !important; }

.marketking-text-secondary-dim { color: #e5ecf5 !important; }

.marketking-bg-dark-dim { background-color: #d9e1ef !important; }

.marketking-text-dark-dim { color: #d9e1ef !important; }

.marketking-bg-gray-dim { background-color: #edf2f9 !important; }

.marketking-text-gray-dim { color: #edf2f9 !important; }

.marketking-bg-gray-100 { background-color: #ebeef2 !important; }

.marketking-bg-gray-200 { background-color: #e5e9f2 !important; }

.marketking-bg-gray-300 { background-color: #dbdfea !important; }

.marketking-bg-gray-400 { background-color: #b7c2d0 !important; }

.marketking-bg-gray-500 { background-color: #8091a7 !important; }

.marketking-bg-gray-600 { background-color: #3c4d62 !important; }

.marketking-bg-gray-700 { background-color: #344357 !important; }

.marketking-bg-gray-800 { background-color: #2b3748 !important; }

.marketking-bg-gray-900 { background-color: #1f2b3a !important; }

.marketking-bg-abstract { background-image: linear-gradient(to right, #2c3782 calc(60% - 150px), #39469f calc(60% - 150px), #39469f 60%, #4856b5 60%, #4856b5 calc(60% + 150px), #5b6ac6 calc(60% + 150px), #5b6ac6 100%); }

.marketking-bg-white-1 { background: rgba(255, 255, 255, 0.1) !important; }

.marketking-bg-white-2 { background: rgba(255, 255, 255, 0.2) !important; }

.marketking-bg-white-3 { background: rgba(255, 255, 255, 0.3) !important; }

.marketking-bg-white-4 { background: rgba(255, 255, 255, 0.4) !important; }

.marketking-bg-white-5 { background: rgba(255, 255, 255, 0.5) !important; }

.marketking-bg-white-6 { background: rgba(255, 255, 255, 0.6) !important; }

.marketking-bg-white-7 { background: rgba(255, 255, 255, 0.7) !important; }

.marketking-bg-white-8 { background: rgba(255, 255, 255, 0.8) !important; }

.marketking-bg-white-9 { background: rgba(255, 255, 255, 0.9) !important; }
