.m-b-0 {
    margin-bottom: 0;
}
.m-r-15 {
    margin-right: 15px;
}
.m-r-10 {
    margin-right: 10px;
}
#marketking_dashboard_wrapper {
    margin-left: -20px;
}
.marketking_dashboard_page_wrapper {
    padding: 15px;
}
.marketking_mail_icon {
    color: #fff;
    font-size: 36px;
}
#marketking_dashboard_wrapper .card {
    max-width: none;
}
.fa-fw,
.fa-li,
.wi-fw {
    text-align: center;
}
.flag-icon,
.flag-icon-background {
    background-repeat: no-repeat;
    background-position: 50%;
}
.waves-effect,
html {
    -webkit-tap-highlight-color: transparent;
}
pre,
textarea {
    overflow: auto;
}
.fa-stack,
.flag-icon,
.mdi-set,
.mdi:before,
.wi,
label,
output {
    display: inline-block;
}
.dropdown-menu,
.nav,
.navbar-nav {
    list-style: none;
}
@font-face {
    font-family: "Nunito Sans";
    font-style: normal;
    font-weight: 300;
    src: local("Nunito Sans Light"), local("NunitoSans-Light"), url(https://fonts.gstatic.com/s/nunitosans/v3/pe03MImSLYBIv1o4X1M8cc8WAc5tU1Q.ttf) format("truetype");
}
@font-face {
    font-family: "Nunito Sans";
    font-style: normal;
    font-weight: 400;
    src: local("Nunito Sans Regular"), local("NunitoSans-Regular"), url(https://fonts.gstatic.com/s/nunitosans/v3/pe0qMImSLYBIv1o4X1M8cce9I94.ttf) format("truetype");
}
@font-face {
    font-family: "Nunito Sans";
    font-style: normal;
    font-weight: 600;
    src: local("Nunito Sans SemiBold"), local("NunitoSans-SemiBold"), url(https://fonts.gstatic.com/s/nunitosans/v3/pe03MImSLYBIv1o4X1M8cc9iB85tU1Q.ttf) format("truetype");
}
@font-face {
    font-family: "Nunito Sans";
    font-style: normal;
    font-weight: 700;
    src: local("Nunito Sans Bold"), local("NunitoSans-Bold"), url(https://fonts.gstatic.com/s/nunitosans/v3/pe03MImSLYBIv1o4X1M8cc8GBs5tU1Q.ttf) format("truetype");
}
@font-face {
    font-family: "Nunito Sans";
    font-style: normal;
    font-weight: 800;
    src: local("Nunito Sans ExtraBold"), local("NunitoSans-ExtraBold"), url(https://fonts.gstatic.com/s/nunitosans/v3/pe03MImSLYBIv1o4X1M8cc8aBc5tU1Q.ttf) format("truetype");
}
.sr-only {
    margin: -1px;
}
dl,
h1,
h2,
h3,
h4,
h5,
h6,
ol,
p,
pre,
ul {
    margin-top: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
    margin: 0;
}
address,
dl,
ol,
p,
pre,
ul {
    margin-bottom: 1rem;
}
dd,
h1,
h2,
h3,
h4,
h5,
h6,
label {
    margin-bottom: 0.5rem;
}
@font-face {
    font-family: "Font Awesome 5 Brands";
    font-style: normal;
    font-weight: 400;
    src: url(icons/font-awesome/webfonts/fa-brands-400.eot);
    src: url(icons/font-awesome/webfonts/fa-brands-400.eot?#iefix) format("embedded-opentype"), url(icons/font-awesome/webfonts/fa-brands-400.woff2) format("woff2"), url(icons/font-awesome/webfonts/fa-brands-400.woff) format("woff"),
        url(icons/font-awesome/webfonts/fa-brands-400.ttf) format("truetype"), url(icons/font-awesome/webfonts/fa-brands-400.svg#fontawesome) format("svg");
}
.fab {
    font-family: "Font Awesome 5 Brands";
}
.fa,
.far,
.fas {
    font-family: "Font Awesome 5 Free";
}
@font-face {
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-weight: 400;
    src: url(icons/font-awesome/webfonts/fa-regular-400.eot);
    src: url(icons/font-awesome/webfonts/fa-regular-400.eot?#iefix) format("embedded-opentype"), url(icons/font-awesome/webfonts/fa-regular-400.woff2) format("woff2"), url(icons/font-awesome/webfonts/fa-regular-400.woff) format("woff"),
        url(icons/font-awesome/webfonts/fa-regular-400.ttf) format("truetype"), url(icons/font-awesome/webfonts/fa-regular-400.svg#fontawesome) format("svg");
}
.far {
    font-weight: 400;
}
@font-face {
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-weight: 900;
    src: url(icons/font-awesome/webfonts/fa-solid-900.eot);
    src: url(icons/font-awesome/webfonts/fa-solid-900.eot?#iefix) format("embedded-opentype"), url(icons/font-awesome/webfonts/fa-solid-900.woff2) format("woff2"), url(icons/font-awesome/webfonts/fa-solid-900.woff) format("woff"),
        url(icons/font-awesome/webfonts/fa-solid-900.ttf) format("truetype"), url(icons/font-awesome/webfonts/fa-solid-900.svg#fontawesome) format("svg");
}
.fa,
.fas {
    font-weight: 900;
}
.wi,
[class*=" ti-"],
[class^="ti-"] {
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
@font-face {
    font-family: simple-line-icons;
    src: url(icons/simple-line-icons/fonts/Simple-Line-Icons.eot?-i3a2kk);
    src: url(icons/simple-line-icons/fonts/Simple-Line-Icons.eot?#iefix-i3a2kk) format("embedded-opentype"), url(icons/simple-line-icons/fonts/Simple-Line-Icons.ttf?-i3a2kk) format("truetype"),
        url(icons/simple-line-icons/fonts/Simple-Line-Icons.woff2?-i3a2kk) format("woff2"), url(icons/simple-line-icons/fonts/Simple-Line-Icons.woff?-i3a2kk) format("woff"),
        url(icons/simple-line-icons/fonts/Simple-Line-Icons.svg?-i3a2kk#simple-line-icons) format("svg");
    font-weight: 400;
    font-style: normal;
}
.icon-action-redo,
.icon-action-undo,
.icon-anchor,
.icon-arrow-down,
.icon-arrow-down-circle,
.icon-arrow-left,
.icon-arrow-left-circle,
.icon-arrow-right,
.icon-arrow-right-circle,
.icon-arrow-up,
.icon-arrow-up-circle,
.icon-badge,
.icon-bag,
.icon-ban,
.icon-basket,
.icon-basket-loaded,
.icon-bell,
.icon-book-open,
.icon-briefcase,
.icon-bubble,
.icon-bubbles,
.icon-bulb,
.icon-calculator,
.icon-calender,
.icon-call-end,
.icon-call-in,
.icon-call-out,
.icon-camera,
.icon-camrecorder,
.icon-chart,
.icon-check,
.icon-chemistry,
.icon-clock,
.icon-close,
.icon-cloud-download,
.icon-cloud-upload,
.icon-compass,
.icon-control-end,
.icon-control-forward,
.icon-control-pause,
.icon-control-play,
.icon-control-rewind,
.icon-control-start,
.icon-credit-card,
.icon-crop,
.icon-cup,
.icon-cursor,
.icon-cursor-move,
.icon-diamond,
.icon-direction,
.icon-directions,
.icon-disc,
.icon-dislike,
.icon-doc,
.icon-docs,
.icon-drawar,
.icon-drop,
.icon-earphones,
.icon-earphones-alt,
.icon-emotsmile,
.icon-energy,
.icon-envelope,
.icon-envelope-letter,
.icon-envelope-open,
.icon-equalizer,
.icon-eye,
.icon-eyeglass,
.icon-feed,
.icon-film,
.icon-fire,
.icon-flag,
.icon-folder,
.icon-folder-alt,
.icon-frame,
.icon-game-controller,
.icon-ghost,
.icon-globe,
.icon-globe-alt,
.icon-graduation,
.icon-graph,
.icon-grid,
.icon-handbag,
.icon-heart,
.icon-home,
.icon-hourglass,
.icon-info,
.icon-key,
.icon-layers,
.icon-like,
.icon-link,
.icon-list,
.icon-location-pin,
.icon-lock,
.icon-lock-open,
.icon-login,
.icon-logout,
.icon-loop,
.icon-magic-wand,
.icon-magnet,
.icon-magnifier,
.icon-magnifier-add,
.icon-magnifier-remove,
.icon-map,
.icon-menu,
.icon-microphone,
.icon-mouse,
.icon-music-tone,
.icon-music-tone-alt,
.icon-mustache,
.icon-note,
.icon-notebook,
.icon-options,
.icon-options-vertical,
.icon-paper-clip,
.icon-paper-plane,
.icon-paypal,
.icon-pencil,
.icon-people,
.icon-phone,
.icon-picture,
.icon-pie-chart,
.icon-pin,
.icon-plane,
.icon-playlist,
.icon-plus,
.icon-power,
.icon-present,
.icon-printer,
.icon-puzzle,
.icon-question,
.icon-refresh,
.icon-reload,
.icon-rocket,
.icon-screen-desktop,
.icon-screen-smartphone,
.icon-screen-tablet,
.icon-settings,
.icon-share,
.icon-share-alt,
.icon-shield,
.icon-shuffle,
.icon-size-actual,
.icon-size-fullscreen,
.icon-social-behance,
.icon-social-dribbble,
.icon-social-dropbox,
.icon-social-facebook,
.icon-social-foursqare,
.icon-social-github,
.icon-social-gplus,
.icon-social-instagram,
.icon-social-linkedin,
.icon-social-pintarest,
.icon-social-reddit,
.icon-social-skype,
.icon-social-soundcloud,
.icon-social-spotify,
.icon-social-stumbleupon,
.icon-social-tumblr,
.icon-social-twitter,
.icon-social-youtube,
.icon-speech,
.icon-speedometer,
.icon-star,
.icon-support,
.icon-symble-female,
.icon-symbol-male,
.icon-tag,
.icon-target,
.icon-trash,
.icon-trophy,
.icon-umbrella,
.icon-user,
.icon-user-female,
.icon-user-follow,
.icon-user-following,
.icon-user-unfollow,
.icon-vector,
.icon-volume-1,
.icon-volume-2,
.icon-volume-off,
.icon-wallet,
.icon-wrench {
    font-family: simple-line-icons;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.icon-user:before {
    content: "\e005";
}
.icon-people:before {
    content: "\e001";
}
.icon-user-female:before {
    content: "\e000";
}
.icon-user-follow:before {
    content: "\e002";
}
.icon-user-following:before {
    content: "\e003";
}
.icon-user-unfollow:before {
    content: "\e004";
}
.icon-login:before {
    content: "\e066";
}
.icon-logout:before {
    content: "\e065";
}
.icon-emotsmile:before {
    content: "\e021";
}
.icon-phone:before {
    content: "\e600";
}
.icon-call-end:before {
    content: "\e048";
}
.icon-call-in:before {
    content: "\e047";
}
.icon-call-out:before {
    content: "\e046";
}
.icon-map:before {
    content: "\e033";
}
.icon-location-pin:before {
    content: "\e096";
}
.icon-direction:before {
    content: "\e042";
}
.icon-directions:before {
    content: "\e041";
}
.icon-compass:before {
    content: "\e045";
}
.icon-layers:before {
    content: "\e034";
}
.icon-menu:before {
    content: "\e601";
}
.icon-list:before {
    content: "\e067";
}
.icon-options-vertical:before {
    content: "\e602";
}
.icon-options:before {
    content: "\e603";
}
.icon-arrow-down:before {
    content: "\e604";
}
.icon-arrow-left:before {
    content: "\e605";
}
.icon-arrow-right:before {
    content: "\e606";
}
.icon-arrow-up:before {
    content: "\e607";
}
.icon-arrow-up-circle:before {
    content: "\e078";
}
.icon-arrow-left-circle:before {
    content: "\e07a";
}
.icon-arrow-right-circle:before {
    content: "\e079";
}
.icon-arrow-down-circle:before {
    content: "\e07b";
}
.icon-check:before {
    content: "\e080";
}
.icon-clock:before {
    content: "\e081";
}
.icon-plus:before {
    content: "\e095";
}
.icon-close:before {
    content: "\e082";
}
.icon-trophy:before {
    content: "\e006";
}
.icon-screen-smartphone:before {
    content: "\e010";
}
.icon-screen-desktop:before {
    content: "\e011";
}
.icon-plane:before {
    content: "\e012";
}
.icon-notebook:before {
    content: "\e013";
}
.icon-mustache:before {
    content: "\e014";
}
.icon-mouse:before {
    content: "\e015";
}
.icon-magnet:before {
    content: "\e016";
}
.icon-energy:before {
    content: "\e020";
}
.icon-disc:before {
    content: "\e022";
}
.icon-cursor:before {
    content: "\e06e";
}
.icon-cursor-move:before {
    content: "\e023";
}
.icon-crop:before {
    content: "\e024";
}
.icon-chemistry:before {
    content: "\e026";
}
.icon-speedometer:before {
    content: "\e007";
}
.icon-shield:before {
    content: "\e00e";
}
.icon-screen-tablet:before {
    content: "\e00f";
}
.icon-magic-wand:before {
    content: "\e017";
}
.icon-hourglass:before {
    content: "\e018";
}
.icon-graduation:before {
    content: "\e019";
}
.icon-ghost:before {
    content: "\e01a";
}
.icon-game-controller:before {
    content: "\e01b";
}
.icon-fire:before {
    content: "\e01c";
}
.icon-eyeglass:before {
    content: "\e01d";
}
.icon-envelope-open:before {
    content: "\e01e";
}
.icon-envelope-letter:before {
    content: "\e01f";
}
.icon-bell:before {
    content: "\e027";
}
.icon-badge:before {
    content: "\e028";
}
.icon-anchor:before {
    content: "\e029";
}
.icon-wallet:before {
    content: "\e02a";
}
.icon-vector:before {
    content: "\e02b";
}
.icon-speech:before {
    content: "\e02c";
}
.icon-puzzle:before {
    content: "\e02d";
}
.icon-printer:before {
    content: "\e02e";
}
.icon-present:before {
    content: "\e02f";
}
.icon-playlist:before {
    content: "\e030";
}
.icon-pin:before {
    content: "\e031";
}
.icon-picture:before {
    content: "\e032";
}
.icon-handbag:before {
    content: "\e035";
}
.icon-globe-alt:before {
    content: "\e036";
}
.icon-globe:before {
    content: "\e037";
}
.icon-folder-alt:before {
    content: "\e039";
}
.icon-folder:before {
    content: "\e089";
}
.icon-film:before {
    content: "\e03a";
}
.icon-feed:before {
    content: "\e03b";
}
.icon-drop:before {
    content: "\e03e";
}
.icon-drawar:before {
    content: "\e03f";
}
.icon-docs:before {
    content: "\e040";
}
.icon-doc:before {
    content: "\e085";
}
.icon-diamond:before {
    content: "\e043";
}
.icon-cup:before {
    content: "\e044";
}
.icon-calculator:before {
    content: "\e049";
}
.icon-bubbles:before {
    content: "\e04a";
}
.icon-briefcase:before {
    content: "\e04b";
}
.icon-book-open:before {
    content: "\e04c";
}
.icon-basket-loaded:before {
    content: "\e04d";
}
.icon-basket:before {
    content: "\e04e";
}
.icon-bag:before {
    content: "\e04f";
}
.icon-action-undo:before {
    content: "\e050";
}
.icon-action-redo:before {
    content: "\e051";
}
.icon-wrench:before {
    content: "\e052";
}
.icon-umbrella:before {
    content: "\e053";
}
.icon-trash:before {
    content: "\e054";
}
.icon-tag:before {
    content: "\e055";
}
.icon-support:before {
    content: "\e056";
}
.icon-frame:before {
    content: "\e038";
}
.icon-size-fullscreen:before {
    content: "\e057";
}
.icon-size-actual:before {
    content: "\e058";
}
.icon-shuffle:before {
    content: "\e059";
}
.icon-share-alt:before {
    content: "\e05a";
}
.icon-share:before {
    content: "\e05b";
}
.icon-rocket:before {
    content: "\e05c";
}
.icon-question:before {
    content: "\e05d";
}
.icon-pie-chart:before {
    content: "\e05e";
}
.icon-pencil:before {
    content: "\e05f";
}
.icon-note:before {
    content: "\e060";
}
.icon-loop:before {
    content: "\e064";
}
.icon-home:before {
    content: "\e069";
}
.icon-grid:before {
    content: "\e06a";
}
.icon-graph:before {
    content: "\e06b";
}
.icon-microphone:before {
    content: "\e063";
}
.icon-music-tone-alt:before {
    content: "\e061";
}
.icon-music-tone:before {
    content: "\e062";
}
.icon-earphones-alt:before {
    content: "\e03c";
}
.icon-earphones:before {
    content: "\e03d";
}
.icon-equalizer:before {
    content: "\e06c";
}
.icon-like:before {
    content: "\e068";
}
.icon-dislike:before {
    content: "\e06d";
}
.icon-control-start:before {
    content: "\e06f";
}
.icon-control-rewind:before {
    content: "\e070";
}
.icon-control-play:before {
    content: "\e071";
}
.icon-control-pause:before {
    content: "\e072";
}
.icon-control-forward:before {
    content: "\e073";
}
.icon-control-end:before {
    content: "\e074";
}
.icon-volume-1:before {
    content: "\e09f";
}
.icon-volume-2:before {
    content: "\e0a0";
}
.icon-volume-off:before {
    content: "\e0a1";
}
.icon-calender:before {
    content: "\e075";
}
.icon-bulb:before {
    content: "\e076";
}
.icon-chart:before {
    content: "\e077";
}
.icon-ban:before {
    content: "\e07c";
}
.icon-bubble:before {
    content: "\e07d";
}
.icon-camrecorder:before {
    content: "\e07e";
}
.icon-camera:before {
    content: "\e07f";
}
.icon-cloud-download:before {
    content: "\e083";
}
.icon-cloud-upload:before {
    content: "\e084";
}
.icon-envelope:before {
    content: "\e086";
}
.icon-eye:before {
    content: "\e087";
}
.icon-flag:before {
    content: "\e088";
}
.icon-heart:before {
    content: "\e08a";
}
.icon-info:before {
    content: "\e08b";
}
.icon-key:before {
    content: "\e08c";
}
.icon-link:before {
    content: "\e08d";
}
.icon-lock:before {
    content: "\e08e";
}
.icon-lock-open:before {
    content: "\e08f";
}
.icon-magnifier:before {
    content: "\e090";
}
.icon-magnifier-add:before {
    content: "\e091";
}
.icon-magnifier-remove:before {
    content: "\e092";
}
.icon-paper-clip:before {
    content: "\e093";
}
.icon-paper-plane:before {
    content: "\e094";
}
.icon-power:before {
    content: "\e097";
}
.icon-refresh:before {
    content: "\e098";
}
.icon-reload:before {
    content: "\e099";
}
.icon-settings:before {
    content: "\e09a";
}
.icon-star:before {
    content: "\e09b";
}
.icon-symble-female:before {
    content: "\e09c";
}
.icon-symbol-male:before {
    content: "\e09d";
}
.icon-target:before {
    content: "\e09e";
}
.icon-credit-card:before {
    content: "\e025";
}
.icon-paypal:before {
    content: "\e608";
}
.icon-social-tumblr:before {
    content: "\e00a";
}
.icon-social-twitter:before {
    content: "\e009";
}
.icon-social-facebook:before {
    content: "\e00b";
}
.icon-social-instagram:before {
    content: "\e609";
}
.icon-social-linkedin:before {
    content: "\e60a";
}
.icon-social-pintarest:before {
    content: "\e60b";
}
.icon-social-github:before {
    content: "\e60c";
}
.icon-social-gplus:before {
    content: "\e60d";
}
.icon-social-reddit:before {
    content: "\e60e";
}
.icon-social-skype:before {
    content: "\e60f";
}
.icon-social-dribbble:before {
    content: "\e00d";
}
.icon-social-behance:before {
    content: "\e610";
}
.icon-social-foursqare:before {
    content: "\e611";
}
.icon-social-soundcloud:before {
    content: "\e612";
}
.icon-social-spotify:before {
    content: "\e613";
}
.icon-social-stumbleupon:before {
    content: "\e614";
}
.icon-social-youtube:before {
    content: "\e008";
}
.icon-social-dropbox:before {
    content: "\e00c";
} /*!
 *  Weather Icons 2.0
 *  Updated August 1, 2015
 *  Weather themed icons for Bootstrap
 *  Author - Erik Flowers - <EMAIL>
 *  Email: <EMAIL>
 *  Twitter: http://twitter.com/Erik_UX
 *  ------------------------------------------------------------------------------
 *  Maintained at http://erikflowers.github.io/weather-icons
 *
 *  License
 *  ------------------------------------------------------------------------------
 *  - Font licensed under SIL OFL 1.1 -
 *    http://scripts.sil.org/OFL
 *  - CSS, SCSS and LESS are licensed under MIT License -
 *    http://opensource.org/licenses/mit-license.html
 *  - Documentation licensed under CC BY 3.0 -
 *    http://creativecommons.org/licenses/by/3.0/
 *  - Inspired by and works great as a companion with Font Awesome
 *    "Font Awesome by Dave Gandy - http://fontawesome.io"
 * Bootstrap v4.2.1 (https://getbootstrap.com/)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
:root {
    --gray-dark: #343a40;
    --blue: #2962ff;
    --indigo: #6610f2;
    --pink: #e83e8c;
    --red: #f62d51;
    --yellow: #ffbc34;
    --green: #36bea6;
    --teal: #20c997;
    --white: #fff;
    --gray: #6c757d;
    --primary: #7460ee;
    --secondary: #6c757d;
    --success: #36bea6;
    --info: #2962ff;
    --warning: #ffbc34;
    --danger: #f62d51;
    --light: #f8f9fa;
    --dark: #343a40;
    --cyan: #4fc3f7;
    --orange: #fb8c00;
    --purple: #7460ee;
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1600px;
    --font-family-sans-serif: "Nunito Sans", sans-serif;
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
*,
::after,
::before {
    box-sizing: border-box;
}
html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
}
#marketking_dashboard_wrapper,
.popover,
.tooltip {
    font-family: "Nunito Sans", sans-serif;
}
#marketking_dashboard_wrapper {
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    background-color: #191821;
}
.text-monospace,
code,
kbd,
pre,
samp {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
[tabindex="-1"]:focus {
    outline: 0 !important;
}
abbr[data-original-title],
abbr[title] {
    text-decoration: underline;
    text-decoration: underline dotted;
    cursor: help;
    border-bottom: 0;
    text-decoration-skip-ink: none;
}
.btn:not(:disabled):not(.disabled),
summary {
    cursor: pointer;
}
address {
    font-style: normal;
}
ol ol,
ol ul,
ul ol,
ul ul {
    margin-bottom: 0;
}
dt {
    font-weight: 800;
}
dd {
    margin-left: 0;
}
blockquote,
figure {
    margin: 0 0 1rem;
}
b,
strong {
    font-weight: bolder;
}
small {
    font-size: 80%;
}
sub,
sup {
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline;
}
sub {
    bottom: -0.25em;
}
sup {
    top: -0.5em;
}
a {
    text-decoration: none;
}

a:not([href]):not([tabindex]),
a:not([href]):not([tabindex]):focus,
a:not([href]):not([tabindex]):hover {
    color: inherit;
    text-decoration: none;
}
a:not([href]):not([tabindex]):focus {
    outline: 0;
}
code,
kbd,
pre,
samp {
    font-size: 1em;
}
img {
    border-style: none;
}
svg {
    overflow: hidden;
}
table {
    border-collapse: collapse;
}
caption {
    padding-top: 1rem;
    padding-bottom: 1rem;
    color: #a1aab2;
    caption-side: bottom;
}
th {
    text-align: inherit;
}
button {
    border-radius: 0;
}
button:focus {
    outline: dotted 1px;
    outline: -webkit-focus-ring-color auto 5px;
}
button,
input,
optgroup,
select,
textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}
button,
input {
    overflow: visible;
}
button,
select {
    text-transform: none;
}
[type="button"],
[type="reset"],
[type="submit"],
button {
    -webkit-appearance: button;
}
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner,
button::-moz-focus-inner {
    padding: 0;
    border-style: none;
}
input[type="checkbox"],
input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}
input[type="date"],
input[type="datetime-local"],
input[type="month"],
input[type="time"] {
    -webkit-appearance: listbox;
}
textarea {
    resize: vertical;
}
fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0;
}
legend {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    color: inherit;
    white-space: normal;
}
.badge,
.dropdown-header,
.dropdown-item,
.input-group-text,
.navbar-brand,
.progress-bar {
    white-space: nowrap;
}
progress {
    vertical-align: baseline;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
    height: auto;
}
[type="search"] {
    outline-offset: -2px;
    -webkit-appearance: none;
}
[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}
::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button;
}
.display-1,
.display-2,
.display-3,
.display-4 {
    line-height: 1.2;
}
summary {
    display: list-item;
}
template {
    display: none;
}
[hidden] {
    display: none !important;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-bottom: 0.5rem;
    font-family: inherit;
    font-weight: 700;
    line-height: 1.2;
    color: inherit;
}
.blockquote,
hr {
    margin-bottom: 1rem;
}
.display-1,
.display-2,
.display-3,
.display-4,
.lead {
    font-weight: 300;
}
.h1,
h1 {
    font-size: 36px;
}
.h2,
h2 {
    font-size: 30px;
}
.h3,
h3 {
    font-size: 24px;
}
.h4,
h4 {
    font-size: 18px;
}
.h5,
h5 {
    font-size: 16px;
}
.h6,
h6 {
    font-size: 14px;
}
.lead {
    font-size: 1.09375rem;
}
.display-1 {
    font-size: 6rem;
}
.display-2 {
    font-size: 5.5rem;
}
.display-3 {
    font-size: 4.5rem;
}
.display-4 {
    font-size: 3.5rem;
}
hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
    margin-top: 1rem;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.img-fluid,
.img-thumbnail {
    max-width: 100%;
    height: auto;
}
.small,
small {
    font-size: 80%;
    font-weight: 400;
}
.mark,
mark {
    padding: 0.2em;
    background-color: #fcf8e3;
}
.list-inline,
.list-unstyled {
    padding-left: 0;
    list-style: none;
}
.list-inline-item {
    display: inline-block;
}
.list-inline-item:not(:last-child) {
    margin-right: 0.5rem;
}
.initialism {
    font-size: 90%;
    text-transform: uppercase;
}
.blockquote {
    font-size: 1.09375rem;
}
.blockquote-footer {
    display: block;
    font-size: 80%;
    color: #6c757d;
}
.blockquote-footer::before {
    content: "\2014\00A0";
}
.img-thumbnail {
    padding: 0.25rem;
    background-color: #191821;
    border: 1px solid #dee2e6;
    border-radius: 2px;
}
.figure {
    display: inline-block;
}
.figure-img {
    margin-bottom: 0.5rem;
    line-height: 1;
}
.figure-caption {
    font-size: 90%;
    color: #6c757d;
}
code,
kbd {
    font-size: 87.5%;
}
a > code,
pre code {
    color: inherit;
}
code {
    color: #e83e8c;
    word-break: break-word;
}
kbd {
    padding: 0.2rem 0.4rem;
    color: #fff;
    background-color: #212529;
    border-radius: 1px;
}
kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: 800;
}
.container,
.container-fluid {
    padding-right: 10px;
    padding-left: 10px;
    margin-right: auto;
    margin-left: auto;
    width: 100%;
}
.btn,
.btn-link,
.custom-select,
.dropdown-item,
.form-control,
.input-group-text {
    font-weight: 400;
}
pre {
    display: block;
    font-size: 87.5%;
    color: #212529;
}
pre code {
    font-size: inherit;
    word-break: normal;
}
.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll;
}
@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
}
@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
}
@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}
@media (min-width: 1600px) {
    .container {
        max-width: 1140px;
    }
}
.col,
.col-auto {
    max-width: 100%;
}
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -10px;
    margin-left: -10px;
}
.no-gutters {
    margin-right: 0;
    margin-left: 0;
}
.no-gutters > .col,
.no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
}
.col,
.col-1,
.col-10,
.col-11,
.col-12,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-auto,
.col-lg,
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto,
.col-md,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-auto,
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-auto,
.col-xl,
.col-xl-1,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-auto {
    position: relative;
    width: 100%;
    padding-right: 10px;
    padding-left: 10px;
}
.col {
    flex-basis: 0;
    flex-grow: 1;
}
.col-auto {
    flex: 0 0 auto;
    width: auto;
}
.col-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
}
.col-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
}
.col-3 {
    flex: 0 0 25%;
    max-width: 25%;
}
.col-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
}
.col-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
}
.col-6 {
    flex: 0 0 50%;
    max-width: 50%;
}
.col-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
}
.col-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
}
.col-9 {
    flex: 0 0 75%;
    max-width: 75%;
}
.col-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
}
.col-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
}
.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
}
.order-first {
    order: -1;
}
.order-last {
    order: 13;
}
.order-0 {
    order: 0;
}
.order-1 {
    order: 1;
}
.order-2 {
    order: 2;
}
.order-3 {
    order: 3;
}
.order-4 {
    order: 4;
}
.order-5 {
    order: 5;
}
.order-6 {
    order: 6;
}
.order-7 {
    order: 7;
}
.order-8 {
    order: 8;
}
.order-9 {
    order: 9;
}
.order-10 {
    order: 10;
}
.order-11 {
    order: 11;
}
.order-12 {
    order: 12;
}
.offset-1 {
    margin-left: 8.33333%;
}
.offset-2 {
    margin-left: 16.66667%;
}
.offset-3 {
    margin-left: 25%;
}
.offset-4 {
    margin-left: 33.33333%;
}
.offset-5 {
    margin-left: 41.66667%;
}
.offset-6 {
    margin-left: 50%;
}
.offset-7 {
    margin-left: 58.33333%;
}
.offset-8 {
    margin-left: 66.66667%;
}
.offset-9 {
    margin-left: 75%;
}
.offset-10 {
    margin-left: 83.33333%;
}
.offset-11 {
    margin-left: 91.66667%;
}
@media (min-width: 576px) {
    .col-sm {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
    .col-sm-auto {
        flex: 0 0 auto;
        width: auto;
        max-width: 100%;
    }
    .col-sm-1 {
        flex: 0 0 8.33333%;
        max-width: 8.33333%;
    }
    .col-sm-2 {
        flex: 0 0 16.66667%;
        max-width: 16.66667%;
    }
    .col-sm-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    .col-sm-4 {
        flex: 0 0 33.33333%;
        max-width: 33.33333%;
    }
    .col-sm-5 {
        flex: 0 0 41.66667%;
        max-width: 41.66667%;
    }
    .col-sm-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .col-sm-7 {
        flex: 0 0 58.33333%;
        max-width: 58.33333%;
    }
    .col-sm-8 {
        flex: 0 0 66.66667%;
        max-width: 66.66667%;
    }
    .col-sm-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }
    .col-sm-10 {
        flex: 0 0 83.33333%;
        max-width: 83.33333%;
    }
    .col-sm-11 {
        flex: 0 0 91.66667%;
        max-width: 91.66667%;
    }
    .col-sm-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .order-sm-first {
        order: -1;
    }
    .order-sm-last {
        order: 13;
    }
    .order-sm-0 {
        order: 0;
    }
    .order-sm-1 {
        order: 1;
    }
    .order-sm-2 {
        order: 2;
    }
    .order-sm-3 {
        order: 3;
    }
    .order-sm-4 {
        order: 4;
    }
    .order-sm-5 {
        order: 5;
    }
    .order-sm-6 {
        order: 6;
    }
    .order-sm-7 {
        order: 7;
    }
    .order-sm-8 {
        order: 8;
    }
    .order-sm-9 {
        order: 9;
    }
    .order-sm-10 {
        order: 10;
    }
    .order-sm-11 {
        order: 11;
    }
    .order-sm-12 {
        order: 12;
    }
    .offset-sm-0 {
        margin-left: 0;
    }
    .offset-sm-1 {
        margin-left: 8.33333%;
    }
    .offset-sm-2 {
        margin-left: 16.66667%;
    }
    .offset-sm-3 {
        margin-left: 25%;
    }
    .offset-sm-4 {
        margin-left: 33.33333%;
    }
    .offset-sm-5 {
        margin-left: 41.66667%;
    }
    .offset-sm-6 {
        margin-left: 50%;
    }
    .offset-sm-7 {
        margin-left: 58.33333%;
    }
    .offset-sm-8 {
        margin-left: 66.66667%;
    }
    .offset-sm-9 {
        margin-left: 75%;
    }
    .offset-sm-10 {
        margin-left: 83.33333%;
    }
    .offset-sm-11 {
        margin-left: 91.66667%;
    }
}
@media (min-width: 768px) {
    .col-md {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
    .col-md-auto {
        flex: 0 0 auto;
        width: auto;
        max-width: 100%;
    }
    .col-md-1 {
        flex: 0 0 8.33333%;
        max-width: 8.33333%;
    }
    .col-md-2 {
        flex: 0 0 16.66667%;
        max-width: 16.66667%;
    }
    .col-md-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    .col-md-4 {
        flex: 0 0 33.33333%;
        max-width: 33.33333%;
    }
    .col-md-5 {
        flex: 0 0 41.66667%;
        max-width: 41.66667%;
    }
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .col-md-7 {
        flex: 0 0 58.33333%;
        max-width: 58.33333%;
    }
    .col-md-8 {
        flex: 0 0 66.66667%;
        max-width: 66.66667%;
    }
    .col-md-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }
    .col-md-10 {
        flex: 0 0 83.33333%;
        max-width: 83.33333%;
    }
    .col-md-11 {
        flex: 0 0 91.66667%;
        max-width: 91.66667%;
    }
    .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .order-md-first {
        order: -1;
    }
    .order-md-last {
        order: 13;
    }
    .order-md-0 {
        order: 0;
    }
    .order-md-1 {
        order: 1;
    }
    .order-md-2 {
        order: 2;
    }
    .order-md-3 {
        order: 3;
    }
    .order-md-4 {
        order: 4;
    }
    .order-md-5 {
        order: 5;
    }
    .order-md-6 {
        order: 6;
    }
    .order-md-7 {
        order: 7;
    }
    .order-md-8 {
        order: 8;
    }
    .order-md-9 {
        order: 9;
    }
    .order-md-10 {
        order: 10;
    }
    .order-md-11 {
        order: 11;
    }
    .order-md-12 {
        order: 12;
    }
    .offset-md-0 {
        margin-left: 0;
    }
    .offset-md-1 {
        margin-left: 8.33333%;
    }
    .offset-md-2 {
        margin-left: 16.66667%;
    }
    .offset-md-3 {
        margin-left: 25%;
    }
    .offset-md-4 {
        margin-left: 33.33333%;
    }
    .offset-md-5 {
        margin-left: 41.66667%;
    }
    .offset-md-6 {
        margin-left: 50%;
    }
    .offset-md-7 {
        margin-left: 58.33333%;
    }
    .offset-md-8 {
        margin-left: 66.66667%;
    }
    .offset-md-9 {
        margin-left: 75%;
    }
    .offset-md-10 {
        margin-left: 83.33333%;
    }
    .offset-md-11 {
        margin-left: 91.66667%;
    }
}
@media (min-width: 992px) {
    .col-lg {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
    .col-lg-auto {
        flex: 0 0 auto;
        width: auto;
        max-width: 100%;
    }
    .col-lg-1 {
        flex: 0 0 8.33333%;
        max-width: 8.33333%;
    }
    .col-lg-2 {
        flex: 0 0 16.66667%;
        max-width: 16.66667%;
    }
    .col-lg-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    .col-lg-4 {
        flex: 0 0 33.33333%;
        max-width: 33.33333%;
    }
    .col-lg-5 {
        flex: 0 0 41.66667%;
        max-width: 41.66667%;
    }
    .col-lg-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .col-lg-7 {
        flex: 0 0 58.33333%;
        max-width: 58.33333%;
    }
    .col-lg-8 {
        flex: 0 0 66.66667%;
        max-width: 66.66667%;
    }
    .col-lg-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }
    .col-lg-10 {
        flex: 0 0 83.33333%;
        max-width: 83.33333%;
    }
    .col-lg-11 {
        flex: 0 0 91.66667%;
        max-width: 91.66667%;
    }
    .col-lg-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .order-lg-first {
        order: -1;
    }
    .order-lg-last {
        order: 13;
    }
    .order-lg-0 {
        order: 0;
    }
    .order-lg-1 {
        order: 1;
    }
    .order-lg-2 {
        order: 2;
    }
    .order-lg-3 {
        order: 3;
    }
    .order-lg-4 {
        order: 4;
    }
    .order-lg-5 {
        order: 5;
    }
    .order-lg-6 {
        order: 6;
    }
    .order-lg-7 {
        order: 7;
    }
    .order-lg-8 {
        order: 8;
    }
    .order-lg-9 {
        order: 9;
    }
    .order-lg-10 {
        order: 10;
    }
    .order-lg-11 {
        order: 11;
    }
    .order-lg-12 {
        order: 12;
    }
    .offset-lg-0 {
        margin-left: 0;
    }
    .offset-lg-1 {
        margin-left: 8.33333%;
    }
    .offset-lg-2 {
        margin-left: 16.66667%;
    }
    .offset-lg-3 {
        margin-left: 25%;
    }
    .offset-lg-4 {
        margin-left: 33.33333%;
    }
    .offset-lg-5 {
        margin-left: 41.66667%;
    }
    .offset-lg-6 {
        margin-left: 50%;
    }
    .offset-lg-7 {
        margin-left: 58.33333%;
    }
    .offset-lg-8 {
        margin-left: 66.66667%;
    }
    .offset-lg-9 {
        margin-left: 75%;
    }
    .offset-lg-10 {
        margin-left: 83.33333%;
    }
    .offset-lg-11 {
        margin-left: 91.66667%;
    }
}
@media (min-width: 1600px) {
    .col-xl {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
    .col-xl-auto {
        flex: 0 0 auto;
        width: auto;
        max-width: 100%;
    }
    .col-xl-1 {
        flex: 0 0 8.33333%;
        max-width: 8.33333%;
    }
    .col-xl-2 {
        flex: 0 0 16.66667%;
        max-width: 16.66667%;
    }
    .col-xl-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    .col-xl-4 {
        flex: 0 0 33.33333%;
        max-width: 33.33333%;
    }
    .col-xl-5 {
        flex: 0 0 41.66667%;
        max-width: 41.66667%;
    }
    .col-xl-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .col-xl-7 {
        flex: 0 0 58.33333%;
        max-width: 58.33333%;
    }
    .col-xl-8 {
        flex: 0 0 66.66667%;
        max-width: 66.66667%;
    }
    .col-xl-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }
    .col-xl-10 {
        flex: 0 0 83.33333%;
        max-width: 83.33333%;
    }
    .col-xl-11 {
        flex: 0 0 91.66667%;
        max-width: 91.66667%;
    }
    .col-xl-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .order-xl-first {
        order: -1;
    }
    .order-xl-last {
        order: 13;
    }
    .order-xl-0 {
        order: 0;
    }
    .order-xl-1 {
        order: 1;
    }
    .order-xl-2 {
        order: 2;
    }
    .order-xl-3 {
        order: 3;
    }
    .order-xl-4 {
        order: 4;
    }
    .order-xl-5 {
        order: 5;
    }
    .order-xl-6 {
        order: 6;
    }
    .order-xl-7 {
        order: 7;
    }
    .order-xl-8 {
        order: 8;
    }
    .order-xl-9 {
        order: 9;
    }
    .order-xl-10 {
        order: 10;
    }
    .order-xl-11 {
        order: 11;
    }
    .order-xl-12 {
        order: 12;
    }
    .offset-xl-0 {
        margin-left: 0;
    }
    .offset-xl-1 {
        margin-left: 8.33333%;
    }
    .offset-xl-2 {
        margin-left: 16.66667%;
    }
    .offset-xl-3 {
        margin-left: 25%;
    }
    .offset-xl-4 {
        margin-left: 33.33333%;
    }
    .offset-xl-5 {
        margin-left: 41.66667%;
    }
    .offset-xl-6 {
        margin-left: 50%;
    }
    .offset-xl-7 {
        margin-left: 58.33333%;
    }
    .offset-xl-8 {
        margin-left: 66.66667%;
    }
    .offset-xl-9 {
        margin-left: 75%;
    }
    .offset-xl-10 {
        margin-left: 83.33333%;
    }
    .offset-xl-11 {
        margin-left: 91.66667%;
    }
}
.table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent;
}
.table td,
.table th {
    padding: 1rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}
.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
}
.table tbody + tbody {
    border-top: 2px solid #dee2e6;
}
.table .table {
    background-color: #191821;
}
.table-sm td,
.table-sm th {
    padding: 0.3rem;
}
.table-bordered,
.table-bordered td,
.table-bordered th {
    border: 1px solid #dee2e6;
}
.table-bordered thead td,
.table-bordered thead th {
    border-bottom-width: 2px;
}
.table-borderless tbody + tbody,
.table-borderless td,
.table-borderless th,
.table-borderless thead th {
    border: 0;
}
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}
.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}
.table-primary,
.table-primary > td,
.table-primary > th {
    background-color: #e3dffc;
}
.table-primary tbody + tbody,
.table-primary td,
.table-primary th,
.table-primary thead th {
    border-color: #b7acf6;
}
.table-hover .table-primary:hover,
.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
    background-color: #cfc8fa;
}
.table-secondary,
.table-secondary > td,
.table-secondary > th {
    background-color: #e2e3e5;
}
.table-secondary tbody + tbody,
.table-secondary td,
.table-secondary th,
.table-secondary thead th {
    border-color: #b3b7bb;
}
.table-hover .table-secondary:hover,
.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
    background-color: #d5d6d9;
}
.table-success,
.table-success > td,
.table-success > th {
    background-color: #d7f2ed;
}
.table-success tbody + tbody,
.table-success td,
.table-success th,
.table-success thead th {
    border-color: #96ddd1;
}
.table-hover .table-success:hover,
.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
    background-color: #c4ece4;
}
.table-info,
.table-info > td,
.table-info > th {
    background-color: #d4e0ff;
}
.table-info tbody + tbody,
.table-info td,
.table-info th,
.table-info thead th {
    border-color: #90adff;
}
.table-hover .table-info:hover,
.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
    background-color: #bbceff;
}
.table-warning,
.table-warning > td,
.table-warning > th {
    background-color: #fff2d6;
}
.table-warning tbody + tbody,
.table-warning td,
.table-warning th,
.table-warning thead th {
    border-color: #ffdc95;
}
.table-hover .table-warning:hover,
.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
    background-color: #ffeabd;
}
.table-danger,
.table-danger > td,
.table-danger > th {
    background-color: #fdd5dc;
}
.table-danger tbody + tbody,
.table-danger td,
.table-danger th,
.table-danger thead th {
    border-color: #fa92a5;
}
.table-hover .table-danger:hover,
.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
    background-color: #fcbdc8;
}
.table-light,
.table-light > td,
.table-light > th {
    background-color: #fefefe;
}
.table-light tbody + tbody,
.table-light td,
.table-light th,
.table-light thead th {
    border-color: #fbfcfc;
}
.table-hover .table-light:hover,
.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
    background-color: #f1f1f1;
}
.table-dark,
.table-dark > td,
.table-dark > th {
    background-color: #d6d8d9;
}
.table-dark tbody + tbody,
.table-dark td,
.table-dark th,
.table-dark thead th {
    border-color: #95999c;
}
.table-hover .table-dark:hover,
.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
    background-color: #c9cbcd;
}
.table-cyan,
.table-cyan > td,
.table-cyan > th {
    background-color: #dcf3fd;
}
.table-cyan tbody + tbody,
.table-cyan td,
.table-cyan th,
.table-cyan thead th {
    border-color: #a3e0fb;
}
.table-hover .table-cyan:hover,
.table-hover .table-cyan:hover > td,
.table-hover .table-cyan:hover > th {
    background-color: #c4ebfc;
}
.table-orange,
.table-orange > td,
.table-orange > th {
    background-color: #fee8cc;
}
.table-orange tbody + tbody,
.table-orange td,
.table-orange th,
.table-orange thead th {
    border-color: #fdc37a;
}
.table-hover .table-orange:hover,
.table-hover .table-orange:hover > td,
.table-hover .table-orange:hover > th {
    background-color: #feddb3;
}
.table-purple,
.table-purple > td,
.table-purple > th {
    background-color: #e3dffc;
}
.table-purple tbody + tbody,
.table-purple td,
.table-purple th,
.table-purple thead th {
    border-color: #b7acf6;
}
.table-hover .table-purple:hover,
.table-hover .table-purple:hover > td,
.table-hover .table-purple:hover > th {
    background-color: #cfc8fa;
}
.table-active,
.table-active > td,
.table-active > th {
    background-color: #f8f9fa;
}
.table-hover .table-active:hover,
.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
    background-color: #e9ecef;
}
.table .thead-dark th {
    color: #fff;
    background-color: #212529;
    border-color: #32383e;
}
.table .thead-light th {
    color: #4f5467;
    background-color: #e9ecef;
    border-color: #dee2e6;
}
.table-dark {
    color: #fff;
    background-color: #212529;
}
.table-dark td,
.table-dark th,
.table-dark thead th {
    border-color: #32383e;
}
.table-dark.table-bordered,
.table-responsive > .table-bordered {
    border: 0;
}
.table-dark.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}
.table-dark.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.075);
}
@media (max-width: 575.98px) {
    .table-responsive-sm {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
    .table-responsive-sm > .table-bordered {
        border: 0;
    }
}
@media (max-width: 767.98px) {
    .table-responsive-md {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
    .table-responsive-md > .table-bordered {
        border: 0;
    }
}
@media (max-width: 991.98px) {
    .table-responsive-lg {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
    .table-responsive-lg > .table-bordered {
        border: 0;
    }
}
@media (max-width: 1599.98px) {
    .table-responsive-xl {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
    .table-responsive-xl > .table-bordered {
        border: 0;
    }
}
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
}
.accordion .card,
.collapsing,
.modal-open,
.progress,
.toast {
    overflow: hidden;
}
.form-control {
    display: block;
    width: 100%;
    height: calc(2.0625rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #4f5467;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media screen and (prefers-reduced-motion: reduce) {
    .form-control {
        transition: none;
    }
}
.form-control::-ms-expand {
    background-color: transparent;
    border: 0;
}
.form-control:focus {
    color: #4f5467;
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.25);
    outline: 0;
    box-shadow: transparent;
}
.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}
.form-control:disabled,
.form-control[readonly] {
    background-color: #e9ecef;
    opacity: 1;
}
select.form-control:focus::-ms-value {
    color: #4f5467;
    background-color: #fff;
}
.form-control-file,
.form-control-range {
    display: block;
    width: 100%;
}
.col-form-label {
    padding-top: calc(0.375rem + 1px);
    padding-bottom: calc(0.375rem + 1px);
    margin-bottom: 0;
    font-size: inherit;
    line-height: 1.5;
}
.col-form-label-lg {
    padding-top: calc(0.5rem + 1px);
    padding-bottom: calc(0.5rem + 1px);
    font-size: 1.09375rem;
    line-height: 1.5;
}
.col-form-label-sm,
.form-control-sm {
    font-size: 0.76563rem;
    line-height: 1.5;
}
.col-form-label-sm {
    padding-top: calc(0.25rem + 1px);
    padding-bottom: calc(0.25rem + 1px);
}
.form-control-plaintext {
    display: block;
    width: 100%;
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
    margin-bottom: 0;
    line-height: 1.5;
    color: #3e5569;
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0;
}
.form-control-plaintext.form-control-lg,
.form-control-plaintext.form-control-sm {
    padding-right: 0;
    padding-left: 0;
}
.form-control-sm {
    height: calc(1.64844rem + 2px);
    padding: 0.25rem 0.5rem;
    border-radius: 1px;
}
.form-control-lg {
    height: calc(2.64063rem + 2px);
    padding: 0.5rem 1rem;
    font-size: 1.09375rem;
    line-height: 1.5;
    border-radius: 2px;
}
select.form-control[multiple],
select.form-control[size],
textarea.form-control {
    height: auto;
}
.form-group {
    margin-bottom: 1rem;
}
.form-text {
    display: block;
    margin-top: 0.25rem;
}
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -5px;
    margin-left: -5px;
}
.form-row > .col,
.form-row > [class*="col-"] {
    padding-right: 5px;
    padding-left: 5px;
}
.form-check {
    display: block;
    padding-left: 1.25rem;
}
.form-check-input {
    position: absolute;
    margin-top: 0.3rem;
    margin-left: -1.25rem;
}
.form-check-input:disabled ~ .form-check-label {
    color: #a1aab2;
}
.form-check-label {
    margin-bottom: 0;
}
.form-check-inline {
    display: inline-flex;
    align-items: center;
    padding-left: 0;
    margin-right: 0.75rem;
}
.form-check-inline .form-check-input {
    position: static;
    margin-top: 0;
    margin-right: 0.3125rem;
    margin-left: 0;
}
.invalid-tooltip,
.valid-tooltip {
    position: absolute;
    z-index: 5;
    max-width: 100%;
    border-radius: 2px;
    top: 100%;
    line-height: 1.5;
}
.valid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #36bea6;
}
.valid-tooltip {
    display: none;
    padding: 0.25rem 0.5rem;
    margin-top: 0.1rem;
    font-size: 0.76563rem;
    color: #fff;
    background-color: rgba(54, 190, 166, 0.9);
}
.custom-control-input.is-valid ~ .valid-feedback,
.custom-control-input.is-valid ~ .valid-tooltip,
.custom-file-input.is-valid ~ .valid-feedback,
.custom-file-input.is-valid ~ .valid-tooltip,
.custom-select.is-valid ~ .valid-feedback,
.custom-select.is-valid ~ .valid-tooltip,
.form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip,
.form-control-file.is-valid ~ .valid-feedback,
.form-control-file.is-valid ~ .valid-tooltip,
.form-control.is-valid ~ .valid-feedback,
.form-control.is-valid ~ .valid-tooltip,
.was-validated .custom-control-input:valid ~ .valid-feedback,
.was-validated .custom-control-input:valid ~ .valid-tooltip,
.was-validated .custom-file-input:valid ~ .valid-feedback,
.was-validated .custom-file-input:valid ~ .valid-tooltip,
.was-validated .custom-select:valid ~ .valid-feedback,
.was-validated .custom-select:valid ~ .valid-tooltip,
.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip,
.was-validated .form-control-file:valid ~ .valid-feedback,
.was-validated .form-control-file:valid ~ .valid-tooltip,
.was-validated .form-control:valid ~ .valid-feedback,
.was-validated .form-control:valid ~ .valid-tooltip {
    display: block;
}
.custom-control-input.is-valid ~ .custom-control-label,
.form-check-input.is-valid ~ .form-check-label,
.was-validated .custom-control-input:valid ~ .custom-control-label,
.was-validated .form-check-input:valid ~ .form-check-label {
    color: #36bea6;
}
.form-control.is-valid,
.was-validated .form-control:valid {
    border-color: #36bea6;
    padding-right: 2.0625rem;
    background-repeat: no-repeat;
    background-position: center right calc(2.0625rem / 4);
    background-size: calc(2.0625rem / 2) calc(2.0625rem / 2);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2336bea6' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
}
.form-control.is-valid:focus,
.was-validated .form-control:valid:focus {
    border-color: #36bea6;
    box-shadow: 0 0 0 0.2rem rgba(54, 190, 166, 0.25);
}
.was-validated textarea.form-control:valid,
textarea.form-control.is-valid {
    padding-right: 2.0625rem;
    background-position: top calc(2.0625rem / 4) right calc(2.0625rem / 4);
}
.custom-select.is-valid,
.was-validated .custom-select:valid {
    border-color: #36bea6;
    padding-right: 3.29688rem;
    background: url(../assets/images/custom-select.png) right 0.75rem center/8px 10px no-repeat,
        url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2336bea6' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e")
            center right 1.75rem/1.03125rem 1.03125rem no-repeat;
}
.custom-select.is-valid:focus,
.was-validated .custom-select:valid:focus {
    border-color: #36bea6;
    box-shadow: 0 0 0 0.2rem rgba(54, 190, 166, 0.25);
}
.custom-control-input.is-valid ~ .custom-control-label::before,
.was-validated .custom-control-input:valid ~ .custom-control-label::before {
    border-color: #36bea6;
}
.custom-control-input.is-valid:checked ~ .custom-control-label::before,
.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before {
    border-color: #58cfba;
    background-color: #58cfba;
}
.custom-control-input.is-valid:focus ~ .custom-control-label::before,
.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before {
    box-shadow: 0 0 0 0.2rem rgba(54, 190, 166, 0.25);
}
.custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before,
.custom-file-input.is-valid ~ .custom-file-label,
.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before,
.was-validated .custom-file-input:valid ~ .custom-file-label {
    border-color: #36bea6;
}
.custom-file-input.is-valid:focus ~ .custom-file-label,
.was-validated .custom-file-input:valid:focus ~ .custom-file-label {
    border-color: #36bea6;
    box-shadow: 0 0 0 0.2rem rgba(54, 190, 166, 0.25);
}
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #f62d51;
}
.invalid-tooltip {
    display: none;
    padding: 0.25rem 0.5rem;
    margin-top: 0.1rem;
    font-size: 0.76563rem;
    color: #fff;
    background-color: rgba(246, 45, 81, 0.9);
}
.collapsing,
.dropdown,
.dropleft,
.dropright,
.dropup {
    position: relative;
}
.custom-control-input.is-invalid ~ .invalid-feedback,
.custom-control-input.is-invalid ~ .invalid-tooltip,
.custom-file-input.is-invalid ~ .invalid-feedback,
.custom-file-input.is-invalid ~ .invalid-tooltip,
.custom-select.is-invalid ~ .invalid-feedback,
.custom-select.is-invalid ~ .invalid-tooltip,
.form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip,
.form-control-file.is-invalid ~ .invalid-feedback,
.form-control-file.is-invalid ~ .invalid-tooltip,
.form-control.is-invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-tooltip,
.was-validated .custom-control-input:invalid ~ .invalid-feedback,
.was-validated .custom-control-input:invalid ~ .invalid-tooltip,
.was-validated .custom-file-input:invalid ~ .invalid-feedback,
.was-validated .custom-file-input:invalid ~ .invalid-tooltip,
.was-validated .custom-select:invalid ~ .invalid-feedback,
.was-validated .custom-select:invalid ~ .invalid-tooltip,
.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip,
.was-validated .form-control-file:invalid ~ .invalid-feedback,
.was-validated .form-control-file:invalid ~ .invalid-tooltip,
.was-validated .form-control:invalid ~ .invalid-feedback,
.was-validated .form-control:invalid ~ .invalid-tooltip {
    display: block;
}
.custom-control-input.is-invalid ~ .custom-control-label,
.form-check-input.is-invalid ~ .form-check-label,
.was-validated .custom-control-input:invalid ~ .custom-control-label,
.was-validated .form-check-input:invalid ~ .form-check-label {
    color: #f62d51;
}
.form-control.is-invalid,
.was-validated .form-control:invalid {
    border-color: #f62d51;
    padding-right: 2.0625rem;
    background-repeat: no-repeat;
    background-position: center right calc(2.0625rem / 4);
    background-size: calc(2.0625rem / 2) calc(2.0625rem / 2);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23f62d51' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");
}
.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus {
    border-color: #f62d51;
    box-shadow: 0 0 0 0.2rem rgba(246, 45, 81, 0.25);
}
.was-validated textarea.form-control:invalid,
textarea.form-control.is-invalid {
    padding-right: 2.0625rem;
    background-position: top calc(2.0625rem / 4) right calc(2.0625rem / 4);
}
.custom-select.is-invalid,
.was-validated .custom-select:invalid {
    border-color: #f62d51;
    padding-right: 3.29688rem;
    background: url(../assets/images/custom-select.png) right 0.75rem center/8px 10px no-repeat,
        url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23f62d51' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E")
            center right 1.75rem/1.03125rem 1.03125rem no-repeat;
}
.custom-select.is-invalid:focus,
.was-validated .custom-select:invalid:focus {
    border-color: #f62d51;
    box-shadow: 0 0 0 0.2rem rgba(246, 45, 81, 0.25);
}
.custom-control-input.is-invalid ~ .custom-control-label::before,
.was-validated .custom-control-input:invalid ~ .custom-control-label::before {
    border-color: #f62d51;
}
.custom-control-input.is-invalid:checked ~ .custom-control-label::before,
.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before {
    border-color: #f85e7a;
    background-color: #f85e7a;
}
.custom-control-input.is-invalid:focus ~ .custom-control-label::before,
.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before {
    box-shadow: 0 0 0 0.2rem rgba(246, 45, 81, 0.25);
}
.custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before,
.custom-file-input.is-invalid ~ .custom-file-label,
.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before,
.was-validated .custom-file-input:invalid ~ .custom-file-label {
    border-color: #f62d51;
}
.custom-file-input.is-invalid:focus ~ .custom-file-label,
.was-validated .custom-file-input:invalid:focus ~ .custom-file-label {
    border-color: #f62d51;
    box-shadow: 0 0 0 0.2rem rgba(246, 45, 81, 0.25);
}
.form-inline {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
}
.form-inline .form-check {
    width: 100%;
}
@media (min-width: 576px) {
    .form-inline label {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0;
    }
    .form-inline .form-group {
        display: flex;
        flex: 0 0 auto;
        flex-flow: row wrap;
        align-items: center;
        margin-bottom: 0;
    }
    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle;
    }
    .form-inline .form-control-plaintext {
        display: inline-block;
    }
    .form-inline .custom-select,
    .form-inline .input-group {
        width: auto;
    }
    .form-inline .form-check {
        display: flex;
        align-items: center;
        justify-content: center;
        width: auto;
        padding-left: 0;
    }
    .form-inline .form-check-input {
        position: relative;
        margin-top: 0;
        margin-right: 0.25rem;
        margin-left: 0;
    }
    .form-inline .custom-control {
        align-items: center;
        justify-content: center;
    }
    .form-inline .custom-control-label {
        margin-bottom: 0;
    }
}
.btn-block,
input[type="button"].btn-block,
input[type="reset"].btn-block,
input[type="submit"].btn-block {
    width: 100%;
}
.btn {
    display: inline-block;
    color: #3e5569;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 2px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.dropdown-toggle::after,
.dropup .dropdown-toggle::after {
    vertical-align: 0.255em;
    content: "";
}
@media screen and (prefers-reduced-motion: reduce) {
    .btn {
        transition: none;
    }
}
.btn:hover {
    color: #3e5569;
    text-decoration: none;
}
.btn.focus,
.btn:focus {
    outline: 0;
    box-shadow: transparent;
}
.btn-primary.focus,
.btn-primary:focus,
.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(137, 120, 241, 0.5);
}
.btn.disabled,
.btn:disabled {
    opacity: 0.65;
}
a.btn.disabled,
fieldset:disabled a.btn {
    pointer-events: none;
}
.btn-primary {
    background-color: #7460ee;
    border-color: #7460ee;
}
.btn-primary:hover {
    background-color: #563dea;
    border-color: #4c32e9;
}
.btn-primary.disabled,
.btn-primary:disabled {
    color: #fff;
    background-color: #7460ee;
    border-color: #7460ee;
}
.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #4c32e9;
    border-color: #4226e8;
}
.btn-secondary.focus,
.btn-secondary:focus,
.btn-secondary:not(:disabled):not(.disabled).active:focus,
.btn-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}
.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}
.btn-secondary.disabled,
.btn-secondary:disabled {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-secondary:not(:disabled):not(.disabled).active,
.btn-secondary:not(:disabled):not(.disabled):active,
.show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #545b62;
    border-color: #4e555b;
}
.btn-success.focus,
.btn-success:focus,
.btn-success:not(:disabled):not(.disabled).active:focus,
.btn-success:not(:disabled):not(.disabled):active:focus,
.show > .btn-success.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(84, 200, 179, 0.5);
}
.btn-success {
    color: #fff;
    background-color: #36bea6;
    border-color: #36bea6;
}
.btn-success:hover {
    color: #fff;
    background-color: #2ea08c;
    border-color: #2b9683;
}
.btn-success.disabled,
.btn-success:disabled {
    color: #fff;
    background-color: #36bea6;
    border-color: #36bea6;
}
.btn-success:not(:disabled):not(.disabled).active,
.btn-success:not(:disabled):not(.disabled):active,
.show > .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #2b9683;
    border-color: #288c7b;
}
.btn-info.focus,
.btn-info:focus,
.btn-info:not(:disabled):not(.disabled).active:focus,
.btn-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-info.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(73, 122, 255, 0.5);
}
.btn-info {
    color: #fff;
    background-color: #2962ff;
    border-color: #2962ff;
}
.btn-info:hover {
    color: #fff;
    background-color: #0346ff;
    border-color: #0041f5;
}
.btn-info.disabled,
.btn-info:disabled {
    color: #fff;
    background-color: #2962ff;
    border-color: #2962ff;
}
.btn-info:not(:disabled):not(.disabled).active,
.btn-info:not(:disabled):not(.disabled):active,
.show > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #0041f5;
    border-color: #003ee8;
}
.btn-warning.focus,
.btn-warning:focus,
.btn-warning:not(:disabled):not(.disabled).active:focus,
.btn-warning:not(:disabled):not(.disabled):active:focus,
.show > .btn-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(222, 165, 50, 0.5);
}
.btn-warning {
    background-color: #ffbc34;
    border-color: #ffbc34;
}
.btn-warning:hover {
    background-color: #ffaf0e;
    border-color: #ffab01;
}
.btn-warning.disabled,
.btn-warning:disabled {
    color: #212529;
    background-color: #ffbc34;
    border-color: #ffbc34;
}
.btn-warning:not(:disabled):not(.disabled).active,
.btn-warning:not(:disabled):not(.disabled):active,
.show > .btn-warning.dropdown-toggle {
    color: #212529;
    background-color: #ffab01;
    border-color: #f3a300;
}
.btn-danger.focus,
.btn-danger:focus,
.btn-danger:not(:disabled):not(.disabled).active:focus,
.btn-danger:not(:disabled):not(.disabled):active:focus,
.show > .btn-danger.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(247, 77, 107, 0.5);
}
.btn-danger {
    color: #fff;
    background-color: #f62d51;
    border-color: #f62d51;
}
.btn-danger:hover {
    color: #fff;
    background-color: #f20a34;
    border-color: #e60a31;
}
.btn-danger.disabled,
.btn-danger:disabled {
    color: #fff;
    background-color: #f62d51;
    border-color: #f62d51;
}
.btn-danger:not(:disabled):not(.disabled).active,
.btn-danger:not(:disabled):not(.disabled):active,
.show > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #e60a31;
    border-color: #da092f;
}
.btn-light.focus,
.btn-light:focus,
.btn-light:not(:disabled):not(.disabled).active:focus,
.btn-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}
.btn-light {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}
.btn-light:hover {
    color: #212529;
    background-color: #e2e6ea;
    border-color: #dae0e5;
}
.btn-light.disabled,
.btn-light:disabled {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}
.btn-light:not(:disabled):not(.disabled).active,
.btn-light:not(:disabled):not(.disabled):active,
.show > .btn-light.dropdown-toggle {
    color: #212529;
    background-color: #dae0e5;
    border-color: #d3d9df;
}
.btn-dark.focus,
.btn-dark:focus,
.btn-dark:not(:disabled):not(.disabled).active:focus,
.btn-dark:not(:disabled):not(.disabled):active:focus,
.show > .btn-dark.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}
.btn-dark {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40;
}
.btn-dark:hover {
    color: #fff;
    background-color: #23272b;
    border-color: #1d2124;
}
.btn-dark.disabled,
.btn-dark:disabled {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40;
}
.btn-dark:not(:disabled):not(.disabled).active,
.btn-dark:not(:disabled):not(.disabled):active,
.show > .btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #1d2124;
    border-color: #171a1d;
}
.btn-cyan.focus,
.btn-cyan:focus,
.btn-cyan:not(:disabled):not(.disabled).active:focus,
.btn-cyan:not(:disabled):not(.disabled):active:focus,
.show > .btn-cyan.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(72, 171, 216, 0.5);
}
.btn-cyan {
    color: #212529;
    background-color: #4fc3f7;
    border-color: #4fc3f7;
}
.btn-cyan:hover {
    color: #fff;
    background-color: #2ab7f5;
    border-color: #1eb2f5;
}
.btn-cyan.disabled,
.btn-cyan:disabled {
    color: #212529;
    background-color: #4fc3f7;
    border-color: #4fc3f7;
}
.btn-cyan:not(:disabled):not(.disabled).active,
.btn-cyan:not(:disabled):not(.disabled):active,
.show > .btn-cyan.dropdown-toggle {
    color: #fff;
    background-color: #1eb2f5;
    border-color: #12aef4;
}
.btn-orange.focus,
.btn-orange:focus,
.btn-orange:not(:disabled):not(.disabled).active:focus,
.btn-orange:not(:disabled):not(.disabled):active:focus,
.show > .btn-orange.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(218, 125, 6, 0.5);
}
.btn-orange {
    color: #212529;
    background-color: #fb8c00;
    border-color: #fb8c00;
}
.btn-orange:hover {
    color: #fff;
    background-color: #d57700;
    border-color: #c87000;
}
.btn-orange.disabled,
.btn-orange:disabled {
    color: #212529;
    background-color: #fb8c00;
    border-color: #fb8c00;
}
.btn-orange:not(:disabled):not(.disabled).active,
.btn-orange:not(:disabled):not(.disabled):active,
.show > .btn-orange.dropdown-toggle {
    color: #fff;
    background-color: #c87000;
    border-color: #bb6800;
}
.btn-purple.focus,
.btn-purple:focus,
.btn-purple:not(:disabled):not(.disabled).active:focus,
.btn-purple:not(:disabled):not(.disabled):active:focus,
.show > .btn-purple.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(137, 120, 241, 0.5);
}
.btn-purple {
    color: #fff;
    background-color: #7460ee;
    border-color: #7460ee;
}
.btn-purple:hover {
    color: #fff;
    background-color: #563dea;
    border-color: #4c32e9;
}
.btn-purple.disabled,
.btn-purple:disabled {
    color: #fff;
    background-color: #7460ee;
    border-color: #7460ee;
}
.btn-purple:not(:disabled):not(.disabled).active,
.btn-purple:not(:disabled):not(.disabled):active,
.show > .btn-purple.dropdown-toggle {
    color: #fff;
    background-color: #4c32e9;
    border-color: #4226e8;
}
.btn-outline-primary.focus,
.btn-outline-primary:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(116, 96, 238, 0.5);
}
.btn-outline-primary {
    color: #7460ee;
    border-color: #7460ee;
}
.btn-outline-primary:hover {
    background-color: #7460ee;
    border-color: #7460ee;
}
.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
    color: #7460ee;
    background-color: transparent;
}
.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #7460ee;
    border-color: #7460ee;
}
.btn-outline-secondary.focus,
.btn-outline-secondary:focus,
.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}
.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}
.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent;
}
.btn-outline-secondary:not(:disabled):not(.disabled).active,
.btn-outline-secondary:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-outline-success.focus,
.btn-outline-success:focus,
.btn-outline-success:not(:disabled):not(.disabled).active:focus,
.btn-outline-success:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-success.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(54, 190, 166, 0.5);
}
.btn-outline-success {
    color: #36bea6;
    border-color: #36bea6;
}
.btn-outline-success:hover {
    color: #fff;
    background-color: #36bea6;
    border-color: #36bea6;
}
.btn-outline-success.disabled,
.btn-outline-success:disabled {
    color: #36bea6;
    background-color: transparent;
}
.btn-outline-success:not(:disabled):not(.disabled).active,
.btn-outline-success:not(:disabled):not(.disabled):active,
.show > .btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #36bea6;
    border-color: #36bea6;
}
.btn-outline-info.focus,
.btn-outline-info:focus,
.btn-outline-info:not(:disabled):not(.disabled).active:focus,
.btn-outline-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(41, 98, 255, 0.5);
}
.btn-outline-info {
    color: #2962ff;
    border-color: #2962ff;
}
.btn-outline-info:hover {
    color: #fff;
    background-color: #2962ff;
    border-color: #2962ff;
}
.btn-outline-info.disabled,
.btn-outline-info:disabled {
    color: #2962ff;
    background-color: transparent;
}
.btn-outline-info:not(:disabled):not(.disabled).active,
.btn-outline-info:not(:disabled):not(.disabled):active,
.show > .btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #2962ff;
    border-color: #2962ff;
}
.btn-outline-warning.focus,
.btn-outline-warning:focus,
.btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.btn-outline-warning:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 188, 52, 0.5);
}
.btn-outline-warning {
    color: #ffbc34;
    border-color: #ffbc34;
}
.btn-outline-warning:hover {
    background-color: #ffbc34;
    border-color: #ffbc34;
}
.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
    color: #ffbc34;
    background-color: transparent;
}
.btn-outline-warning:not(:disabled):not(.disabled).active,
.btn-outline-warning:not(:disabled):not(.disabled):active,
.show > .btn-outline-warning.dropdown-toggle {
    color: #212529;
    background-color: #ffbc34;
    border-color: #ffbc34;
}
.btn-outline-danger.focus,
.btn-outline-danger:focus,
.btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.btn-outline-danger:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-danger.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(246, 45, 81, 0.5);
}
.btn-outline-danger {
    color: #f62d51;
    border-color: #f62d51;
}
.btn-outline-danger:hover {
    color: #fff;
    background-color: #f62d51;
    border-color: #f62d51;
}
.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
    color: #f62d51;
    background-color: transparent;
}
.btn-outline-danger:not(:disabled):not(.disabled).active,
.btn-outline-danger:not(:disabled):not(.disabled):active,
.show > .btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #f62d51;
    border-color: #f62d51;
}
.btn-outline-light.focus,
.btn-outline-light:focus,
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}
.btn-outline-light {
    color: #f8f9fa;
    border-color: #f8f9fa;
}
.btn-outline-light:hover {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}
.btn-outline-light.disabled,
.btn-outline-light:disabled {
    color: #f8f9fa;
    background-color: transparent;
}
.btn-outline-light:not(:disabled):not(.disabled).active,
.btn-outline-light:not(:disabled):not(.disabled):active,
.show > .btn-outline-light.dropdown-toggle {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}
.btn-outline-dark.focus,
.btn-outline-dark:focus,
.btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.btn-outline-dark:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-dark.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}
.btn-outline-dark {
    color: #343a40;
    border-color: #343a40;
}
.btn-outline-dark:hover {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40;
}
.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
    color: #343a40;
    background-color: transparent;
}
.btn-outline-dark:not(:disabled):not(.disabled).active,
.btn-outline-dark:not(:disabled):not(.disabled):active,
.show > .btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40;
}
.btn-outline-cyan.focus,
.btn-outline-cyan:focus,
.btn-outline-cyan:not(:disabled):not(.disabled).active:focus,
.btn-outline-cyan:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-cyan.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(79, 195, 247, 0.5);
}
.btn-outline-cyan {
    color: #4fc3f7;
    border-color: #4fc3f7;
}
.btn-outline-cyan:hover {
    color: #212529;
    background-color: #4fc3f7;
    border-color: #4fc3f7;
}
.btn-outline-cyan.disabled,
.btn-outline-cyan:disabled {
    color: #4fc3f7;
    background-color: transparent;
}
.btn-outline-cyan:not(:disabled):not(.disabled).active,
.btn-outline-cyan:not(:disabled):not(.disabled):active,
.show > .btn-outline-cyan.dropdown-toggle {
    color: #212529;
    background-color: #4fc3f7;
    border-color: #4fc3f7;
}
.btn-outline-orange.focus,
.btn-outline-orange:focus,
.btn-outline-orange:not(:disabled):not(.disabled).active:focus,
.btn-outline-orange:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-orange.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(251, 140, 0, 0.5);
}
.btn-outline-orange {
    color: #fb8c00;
    border-color: #fb8c00;
}
.btn-outline-orange:hover {
    color: #212529;
    background-color: #fb8c00;
    border-color: #fb8c00;
}
.btn-outline-orange.disabled,
.btn-outline-orange:disabled {
    color: #fb8c00;
    background-color: transparent;
}
.btn-outline-orange:not(:disabled):not(.disabled).active,
.btn-outline-orange:not(:disabled):not(.disabled):active,
.show > .btn-outline-orange.dropdown-toggle {
    color: #212529;
    background-color: #fb8c00;
    border-color: #fb8c00;
}
.btn-outline-purple.focus,
.btn-outline-purple:focus,
.btn-outline-purple:not(:disabled):not(.disabled).active:focus,
.btn-outline-purple:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-purple.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(116, 96, 238, 0.5);
}
.btn-outline-purple {
    color: #7460ee;
    border-color: #7460ee;
}
.btn-outline-purple:hover {
    color: #fff;
    background-color: #7460ee;
    border-color: #7460ee;
}
.btn-outline-purple.disabled,
.btn-outline-purple:disabled {
    color: #7460ee;
    background-color: transparent;
}
.btn-outline-purple:not(:disabled):not(.disabled).active,
.btn-outline-purple:not(:disabled):not(.disabled):active,
.show > .btn-outline-purple.dropdown-toggle {
    color: #fff;
    background-color: #7460ee;
    border-color: #7460ee;
}
.btn-link {
    color: #7460ee;
}
.btn-link:hover {
    color: #381be7;
    text-decoration: underline;
}
.btn-link.focus,
.btn-link:focus {
    text-decoration: underline;
    box-shadow: none;
}
.btn-link.disabled,
.btn-link:disabled {
    color: #6c757d;
    pointer-events: none;
}
.btn-group-lg > .btn,
.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.09375rem;
    line-height: 1.5;
    border-radius: 2px;
}
.btn-group-sm > .btn,
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.76563rem;
    line-height: 1.5;
    border-radius: 1px;
}
.btn-block {
    display: block;
}
.btn-block + .btn-block {
    margin-top: 0.5rem;
}
.fade {
    transition: opacity 0.15s linear;
}
@media screen and (prefers-reduced-motion: reduce) {
    .fade {
        transition: none;
    }
}
.fade:not(.show) {
    opacity: 0;
}
.collapse:not(.show) {
    display: none;
}
.collapsing {
    height: 0;
    transition: height 0.35s ease;
}
@media screen and (prefers-reduced-motion: reduce) {
    .collapsing {
        transition: none;
    }
}
.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
    margin-left: 0;
}
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.875rem;
    color: #3e5569;
    text-align: left;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
}
.dropdown-menu-right {
    right: 0;
    left: auto;
}
@media (min-width: 576px) {
    .dropdown-menu-sm-right {
        right: 0;
        left: auto;
    }
}
@media (min-width: 768px) {
    .dropdown-menu-md-right {
        right: 0;
        left: auto;
    }
}
@media (min-width: 992px) {
    .dropdown-menu-lg-right {
        right: 0;
        left: auto;
    }
}
@media (min-width: 1600px) {
    .dropdown-menu-xl-right {
        right: 0;
        left: auto;
    }
    .dropdown-menu-xl-left {
        right: auto;
        left: 0;
    }
}
.dropdown-menu-left {
    right: auto;
    left: 0;
}
@media (min-width: 576px) {
    .dropdown-menu-sm-left {
        right: auto;
        left: 0;
    }
}
@media (min-width: 768px) {
    .dropdown-menu-md-left {
        right: auto;
        left: 0;
    }
}
@media (min-width: 992px) {
    .dropdown-menu-lg-left {
        right: auto;
        left: 0;
    }
}
.dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: 0.125rem;
}
.dropup .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    border-top: 0;
    border-right: 0.3em solid transparent;
    border-bottom: 0.3em solid;
    border-left: 0.3em solid transparent;
}
.dropleft .dropdown-toggle::before,
.dropright .dropdown-toggle::after {
    content: "";
    border-top: 0.3em solid transparent;
    border-bottom: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
    margin-left: 0;
}
.dropright .dropdown-menu {
    top: 0;
    right: auto;
    left: 100%;
    margin-top: 0;
    margin-left: 0.125rem;
}
.dropright .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    border-right: 0;
    border-left: 0.3em solid;
    vertical-align: 0;
}
.dropright .dropdown-toggle:empty::after {
    margin-left: 0;
}
.dropleft .dropdown-menu {
    top: 0;
    right: 100%;
    left: auto;
    margin-top: 0;
    margin-right: 0.125rem;
}
.dropleft .dropdown-toggle::after {
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    display: none;
}
.dropleft .dropdown-toggle::before {
    display: inline-block;
    margin-right: 0.255em;
    border-right: 0.3em solid;
    vertical-align: 0;
}
.dropleft .dropdown-toggle:empty::after {
    margin-left: 0;
}
.dropdown-menu[x-placement^="bottom"],
.dropdown-menu[x-placement^="left"],
.dropdown-menu[x-placement^="right"],
.dropdown-menu[x-placement^="top"] {
    right: auto;
    bottom: auto;
}
.dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #f8f9fa;
}
.btn-group-toggle > .btn,
.btn-group-toggle > .btn-group > .btn,
.custom-control-label,
.custom-file,
.dropdown-header,
.input-group-text,
.nav {
    margin-bottom: 0;
}
.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.65rem 1rem;
    clear: both;
    color: #212529;
    text-align: inherit;
    background-color: transparent;
    border: 0;
}
.dropdown-item:first-child {
    border-top-left-radius: calc(2px - 1px);
    border-top-right-radius: calc(2px - 1px);
}
.btn-group > .btn-group:not(:first-child) > .btn,
.btn-group > .btn:not(:first-child),
.input-group > .custom-file:not(:first-child) .custom-file-label,
.input-group > .custom-select:not(:first-child),
.input-group > .form-control:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.btn-group > .btn-group:not(:last-child) > .btn,
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .custom-file:not(:last-child) .custom-file-label,
.input-group > .custom-file:not(:last-child) .custom-file-label::after,
.input-group > .custom-select:not(:last-child),
.input-group > .form-control:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.dropdown-item:last-child {
    border-bottom-right-radius: calc(2px - 1px);
    border-bottom-left-radius: calc(2px - 1px);
}
.dropdown-item:focus,
.dropdown-item:hover {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa;
}
.dropdown-item.active,
.dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #2962ff;
}
.dropdown-item.disabled,
.dropdown-item:disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: transparent;
}
.dropdown-menu.show {
    display: block;
}
.dropdown-header {
    display: block;
    padding: 0.5rem 1rem;
    font-size: 0.76563rem;
    color: #6c757d;
}
.dropdown-item-text {
    display: block;
    padding: 0.65rem 1rem;
    color: #212529;
}
.btn-group,
.btn-group-vertical {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
}
.btn-group-vertical > .btn,
.btn-group > .btn {
    position: relative;
    flex: 1 1 auto;
}
.btn-group-vertical > .btn.active,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:hover,
.btn-group > .btn.active,
.btn-group > .btn:active,
.btn-group > .btn:focus,
.btn-group > .btn:hover {
    z-index: 1;
}
.btn-toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
}
.btn-toolbar .input-group {
    width: auto;
}
.btn-group > .btn-group:not(:first-child),
.btn-group > .btn:not(:first-child) {
    margin-left: -1px;
}
.dropdown-toggle-split {
    padding-right: 0.5625rem;
    padding-left: 0.5625rem;
}
.dropdown-toggle-split::after,
.dropright .dropdown-toggle-split::after,
.dropup .dropdown-toggle-split::after {
    margin-left: 0;
}
.input-group-append,
.input-group-append .btn + .btn,
.input-group-append .btn + .input-group-text,
.input-group-append .input-group-text + .btn,
.input-group-append .input-group-text + .input-group-text,
.input-group-prepend .btn + .btn,
.input-group-prepend .btn + .input-group-text,
.input-group-prepend .input-group-text + .btn,
.input-group-prepend .input-group-text + .input-group-text,
.input-group > .custom-file + .custom-file,
.input-group > .custom-file + .custom-select,
.input-group > .custom-file + .form-control,
.input-group > .custom-select + .custom-file,
.input-group > .custom-select + .custom-select,
.input-group > .custom-select + .form-control,
.input-group > .form-control + .custom-file,
.input-group > .form-control + .custom-select,
.input-group > .form-control + .form-control,
.input-group > .form-control-plaintext + .custom-file,
.input-group > .form-control-plaintext + .custom-select,
.input-group > .form-control-plaintext + .form-control {
    margin-left: -1px;
}
.dropleft .dropdown-toggle-split::before {
    margin-right: 0;
}
.btn-group-sm > .btn + .dropdown-toggle-split,
.btn-sm + .dropdown-toggle-split {
    padding-right: 0.375rem;
    padding-left: 0.375rem;
}
.btn-group-lg > .btn + .dropdown-toggle-split,
.btn-lg + .dropdown-toggle-split {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}
.btn-group-vertical {
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
    width: 100%;
}
.btn-group-vertical > .btn-group:not(:first-child),
.btn-group-vertical > .btn:not(:first-child) {
    margin-top: -1px;
}
.btn-group-vertical > .btn-group:not(:last-child) > .btn,
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn-group:not(:first-child) > .btn,
.btn-group-vertical > .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.btn-group-toggle > .btn input[type="checkbox"],
.btn-group-toggle > .btn input[type="radio"],
.btn-group-toggle > .btn-group > .btn input[type="checkbox"],
.btn-group-toggle > .btn-group > .btn input[type="radio"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}
.input-group > .custom-file,
.input-group > .custom-select,
.input-group > .form-control,
.input-group > .form-control-plaintext {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    margin-bottom: 0;
}
.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label,
.input-group > .custom-select:focus,
.input-group > .form-control:focus {
    z-index: 3;
}
.input-group > .custom-file .custom-file-input:focus {
    z-index: 4;
}
.input-group > .custom-file {
    display: flex;
    align-items: center;
}
.input-group-append,
.input-group-prepend {
    display: flex;
}
.input-group-append .btn,
.input-group-prepend .btn {
    position: relative;
    z-index: 2;
}
.input-group-append .btn:focus,
.input-group-prepend .btn:focus {
    z-index: 3;
}
.input-group-prepend {
    margin-right: -1px;
}
.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #4f5467;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 2px;
}
.nav,
.navbar {
    flex-wrap: wrap;
}
.input-group-text input[type="checkbox"],
.input-group-text input[type="radio"] {
    margin-top: 0;
}
.input-group-lg > .custom-select,
.input-group-lg > .form-control:not(textarea) {
    height: calc(2.64063rem + 2px);
}
.input-group-lg > .custom-select,
.input-group-lg > .form-control,
.input-group-lg > .input-group-append > .btn,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-prepend > .input-group-text {
    padding: 0.5rem 1rem;
    font-size: 1.09375rem;
    line-height: 1.5;
    border-radius: 2px;
}
.input-group-sm > .custom-select,
.input-group-sm > .form-control:not(textarea) {
    height: calc(1.64844rem + 2px);
}
.input-group-sm > .custom-select,
.input-group-sm > .form-control,
.input-group-sm > .input-group-append > .btn,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-prepend > .input-group-text {
    padding: 0.25rem 0.5rem;
    font-size: 0.76563rem;
    line-height: 1.5;
    border-radius: 1px;
}
.input-group-lg > .custom-select,
.input-group-sm > .custom-select {
    padding-right: 1.75rem;
}
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child),
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child),
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.custom-control {
    position: relative;
    display: block;
    min-height: 1.3125rem;
    padding-left: 1.5rem;
}
.custom-control-inline {
    display: inline-flex;
    margin-right: 1rem;
}
.custom-control-input {
    position: absolute;
    z-index: -1;
    opacity: 0;
}
.custom-control-input:checked ~ .custom-control-label::before {
    color: #fff;
    border-color: #2962ff;
    background-color: #2962ff;
}
.custom-control-input:focus ~ .custom-control-label::before {
    box-shadow: transparent;
}
.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
    border-color: rgba(0, 0, 0, 0.25);
}
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
    color: #fff;
    background-color: #000;
    border-color: #000;
}
.custom-control-input:disabled ~ .custom-control-label {
    color: #6c757d;
}
.custom-control-input:disabled ~ .custom-control-label::before {
    background-color: #e9ecef;
}
.custom-control-label {
    position: relative;
    vertical-align: top;
}
.custom-control-label::after,
.custom-control-label::before {
    position: absolute;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    content: "";
}
.custom-control-label::before {
    pointer-events: none;
    background-color: #fff;
    border: 1px solid #a1aab2;
}
.custom-control-label::after {
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 50% 50%;
}
.custom-checkbox .custom-control-label::before {
    border-radius: 2px;
}
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e");
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
    border-color: #2962ff;
    background-color: #2962ff;
}
.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before,
.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before,
.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: rgba(116, 96, 238, 0.5);
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}
.custom-radio .custom-control-label::before {
    border-radius: 50%;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.custom-switch {
    padding-left: 2.25rem;
}
.custom-switch .custom-control-label::before {
    left: -2.25rem;
    width: 1.75rem;
    pointer-events: all;
    border-radius: 0.5rem;
}
.custom-switch .custom-control-label::after {
    top: calc(0.15625rem + 2px);
    left: calc(-2.25rem + 2px);
    width: calc(1rem - 4px);
    height: calc(1rem - 4px);
    background-color: #a1aab2;
    border-radius: 0.5rem;
    transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media screen and (prefers-reduced-motion: reduce) {
    .custom-switch .custom-control-label::after {
        transition: none;
    }
}
.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    background-color: #fff;
    transform: translateX(0.75rem);
}
.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: rgba(116, 96, 238, 0.5);
}
.custom-select {
    display: inline-block;
    width: 100%;
    height: calc(2.0625rem + 2px);
    padding: 0.375rem 1.75rem 0.375rem 0.75rem;
    line-height: 1.5;
    color: #4f5467;
    vertical-align: middle;
    background: url(../assets/images/custom-select.png) right 0.75rem center/8px 10px no-repeat #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    appearance: none;
}
.custom-file-input:focus ~ .custom-file-label,
.custom-select:focus {
    border-color: rgba(0, 0, 0, 0.25);
    box-shadow: transparent;
}
.custom-select:focus {
    outline: 0;
}
.custom-select:focus::-ms-value {
    color: #4f5467;
    background-color: #fff;
}
.custom-file-input:disabled ~ .custom-file-label,
.custom-select:disabled {
    background-color: #e9ecef;
}
.custom-select[multiple],
.custom-select[size]:not([size="1"]) {
    height: auto;
    padding-right: 0.75rem;
    background-image: none;
}
.custom-select:disabled {
    color: #6c757d;
}
.custom-select::-ms-expand {
    opacity: 0;
}
.custom-select-sm {
    height: calc(1.64844rem + 2px);
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    padding-left: 0.5rem;
    font-size: 0.76563rem;
}
.custom-select-lg {
    height: calc(2.64063rem + 2px);
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 1rem;
    font-size: 1.09375rem;
}
.custom-file,
.custom-file-input,
.custom-file-label {
    height: calc(2.0625rem + 2px);
}
.custom-file {
    position: relative;
    display: inline-block;
    width: 100%;
}
.custom-file-input {
    position: relative;
    z-index: 2;
    width: 100%;
    margin: 0;
    opacity: 0;
}
.custom-file-label,
.custom-file-label::after {
    position: absolute;
    padding: 0.375rem 0.75rem;
    line-height: 1.5;
    color: #4f5467;
    top: 0;
    right: 0;
}
.custom-file-input:lang(en) ~ .custom-file-label::after {
    content: "Browse";
}
.custom-file-input ~ .custom-file-label[data-browse]::after {
    content: attr(data-browse);
}
.custom-file-label {
    left: 0;
    z-index: 1;
    font-weight: 400;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 2px;
}
.alert-link,
.close {
    font-weight: 800;
}
.custom-file-label::after {
    bottom: 0;
    z-index: 3;
    display: block;
    height: 2.0625rem;
    content: "Browse";
    background-color: #f8f9fa;
    border-left: inherit;
    border-radius: 0 2px 2px 0;
}
.custom-range {
    width: 100%;
    height: calc(1rem + 0.4rem);
    padding: 0;
    background-color: transparent;
    appearance: none;
}
.custom-range:focus {
    outline: 0;
}
.custom-range:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 1px #191821, transparent;
}
.custom-range:focus::-moz-range-thumb {
    box-shadow: 0 0 0 1px #191821, transparent;
}
.custom-range:focus::-ms-thumb {
    box-shadow: 0 0 0 1px #191821, transparent;
}
.custom-range::-moz-focus-outer {
    border: 0;
}
.custom-range::-webkit-slider-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: -0.25rem;
    background-color: #2962ff;
    border: 0;
    border-radius: 1rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    appearance: none;
}
@media screen and (prefers-reduced-motion: reduce) {
    .custom-range::-webkit-slider-thumb {
        transition: none;
    }
}
.custom-range::-webkit-slider-thumb:active {
    background-color: #dce5ff;
}
.custom-range::-webkit-slider-runnable-track {
    width: 100%;
    height: 0.5rem;
    color: transparent;
    cursor: pointer;
    background-color: #dee2e6;
    border-color: transparent;
    border-radius: 1rem;
}
.custom-range::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background-color: #2962ff;
    border: 0;
    border-radius: 1rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    appearance: none;
}
@media screen and (prefers-reduced-motion: reduce) {
    .custom-range::-moz-range-thumb {
        transition: none;
    }
}
.custom-range::-moz-range-thumb:active {
    background-color: #dce5ff;
}
.custom-range::-moz-range-track {
    width: 100%;
    height: 0.5rem;
    color: transparent;
    cursor: pointer;
    background-color: #dee2e6;
    border-color: transparent;
    border-radius: 1rem;
}
.custom-range::-ms-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: 0;
    margin-right: 0.2rem;
    margin-left: 0.2rem;
    background-color: #2962ff;
    border: 0;
    border-radius: 1rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    appearance: none;
}
@media screen and (prefers-reduced-motion: reduce) {
    .custom-range::-ms-thumb {
        transition: none;
    }
}
.custom-range::-ms-thumb:active {
    background-color: #dce5ff;
}
.custom-range::-ms-track {
    width: 100%;
    height: 0.5rem;
    color: transparent;
    cursor: pointer;
    background-color: transparent;
    border-color: transparent;
    border-width: 0.5rem;
}
.custom-range::-ms-fill-lower {
    background-color: #dee2e6;
    border-radius: 1rem;
}
.custom-range::-ms-fill-upper {
    margin-right: 15px;
    background-color: #dee2e6;
    border-radius: 1rem;
}
.custom-range:disabled::-webkit-slider-thumb {
    background-color: #a1aab2;
}
.custom-range:disabled::-webkit-slider-runnable-track {
    cursor: default;
}
.custom-range:disabled::-moz-range-thumb {
    background-color: #a1aab2;
}
.custom-range:disabled::-moz-range-track {
    cursor: default;
}
.custom-range:disabled::-ms-thumb {
    background-color: #a1aab2;
}
.custom-control-label::before,
.custom-file-label,
.custom-select {
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media screen and (prefers-reduced-motion: reduce) {
    .custom-control-label::before,
    .custom-file-label,
    .custom-select {
        transition: none;
    }
}
.nav {
    display: flex;
    padding-left: 0;
}
.nav-link,
.navbar {
    padding: 0.5rem 1rem;
}
.nav-link {
    display: block;
}
.nav-link:focus,
.nav-link:hover {
    text-decoration: none;
}
.nav-link.disabled {
    color: #6c757d;
    pointer-events: none;
    cursor: default;
}
.navbar-toggler:not(:disabled):not(.disabled),
.page-link:not(:disabled):not(.disabled) {
    cursor: pointer;
}
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-item {
    margin-bottom: -1px;
}
.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}
.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
}
.nav-tabs .nav-link.disabled {
    color: #6c757d;
    background-color: transparent;
    border-color: transparent;
}
.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
    color: #4f5467;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.nav-pills .nav-link {
    border-radius: 2px;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
    color: #fff;
    background-color: #2962ff;
}
.nav-fill .nav-item {
    flex: 1 1 auto;
    text-align: center;
}
.nav-justified .nav-item {
    flex-basis: 0;
    flex-grow: 1;
    text-align: center;
}
.tab-content > .tab-pane {
    display: none;
}
.tab-content > .active {
    display: block;
}
.navbar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.navbar > .container,
.navbar > .container-fluid {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}
.navbar-brand {
    display: inline-block;
    padding-top: 0.33594rem;
    padding-bottom: 0.33594rem;
    margin-right: 1rem;
    font-size: 1.09375rem;
    line-height: inherit;
}
.navbar-brand:focus,
.navbar-brand:hover {
    text-decoration: none;
}
.navbar-nav {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
}
.navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0;
}
.navbar-nav .dropdown-menu {
    position: static;
    float: none;
}
.navbar-text {
    display: inline-block;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}
.navbar-collapse {
    flex-basis: 100%;
    flex-grow: 1;
    align-items: center;
}
.navbar-toggler {
    padding: 0.25rem 0.75rem;
    font-size: 1.09375rem;
    line-height: 1;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 2px;
}
.navbar-toggler:focus,
.navbar-toggler:hover {
    text-decoration: none;
}
.navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    content: "";
    background: center center no-repeat;
    background-size: 100% 100%;
}
@media (max-width: 575.98px) {
    .navbar-expand-sm > .container,
    .navbar-expand-sm > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}
@media (min-width: 576px) {
    .navbar-expand-sm {
        flex-flow: row nowrap;
        justify-content: flex-start;
    }
    .navbar-expand-sm .navbar-nav {
        flex-direction: row;
    }
    .navbar-expand-sm .navbar-nav .dropdown-menu {
        position: absolute;
    }
    .navbar-expand-sm .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
    .navbar-expand-sm > .container,
    .navbar-expand-sm > .container-fluid {
        flex-wrap: nowrap;
    }
    .navbar-expand-sm .navbar-collapse {
        display: flex !important;
        flex-basis: auto;
    }
    .navbar-expand-sm .navbar-toggler {
        display: none;
    }
}
@media (max-width: 767.98px) {
    .navbar-expand-md > .container,
    .navbar-expand-md > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}
@media (min-width: 768px) {
    .navbar-expand-md {
        flex-flow: row nowrap;
        justify-content: flex-start;
    }
    .navbar-expand-md .navbar-nav {
        flex-direction: row;
    }
    .navbar-expand-md .navbar-nav .dropdown-menu {
        position: absolute;
    }
    .navbar-expand-md .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
    .navbar-expand-md > .container,
    .navbar-expand-md > .container-fluid {
        flex-wrap: nowrap;
    }
    .navbar-expand-md .navbar-collapse {
        display: flex !important;
        flex-basis: auto;
    }
    .navbar-expand-md .navbar-toggler {
        display: none;
    }
}
@media (max-width: 991.98px) {
    .navbar-expand-lg > .container,
    .navbar-expand-lg > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}
@media (min-width: 992px) {
    .navbar-expand-lg {
        flex-flow: row nowrap;
        justify-content: flex-start;
    }
    .navbar-expand-lg .navbar-nav {
        flex-direction: row;
    }
    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute;
    }
    .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
    .navbar-expand-lg > .container,
    .navbar-expand-lg > .container-fluid {
        flex-wrap: nowrap;
    }
    .navbar-expand-lg .navbar-collapse {
        display: flex !important;
        flex-basis: auto;
    }
    .navbar-expand-lg .navbar-toggler {
        display: none;
    }
}
@media (max-width: 1599.98px) {
    .navbar-expand-xl > .container,
    .navbar-expand-xl > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}
@media (min-width: 1600px) {
    .navbar-expand-xl {
        flex-flow: row nowrap;
        justify-content: flex-start;
    }
    .navbar-expand-xl .navbar-nav {
        flex-direction: row;
    }
    .navbar-expand-xl .navbar-nav .dropdown-menu {
        position: absolute;
    }
    .navbar-expand-xl .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
    .navbar-expand-xl > .container,
    .navbar-expand-xl > .container-fluid {
        flex-wrap: nowrap;
    }
    .navbar-expand-xl .navbar-collapse {
        display: flex !important;
        flex-basis: auto;
    }
    .navbar-expand-xl .navbar-toggler {
        display: none;
    }
}
.navbar-expand {
    flex-flow: row nowrap;
    justify-content: flex-start;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid {
    padding-right: 0;
    padding-left: 0;
}
.navbar-expand .navbar-nav {
    flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
    position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid {
    flex-wrap: nowrap;
}
.navbar-expand .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
}
.navbar-expand .navbar-toggler {
    display: none;
}
.navbar-light .navbar-brand,
.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover {
    color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-nav .nav-link {
    color: rgba(0, 0, 0, 0.5);
}
.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
    color: rgba(0, 0, 0, 0.7);
}
.navbar-light .navbar-nav .nav-link.disabled {
    color: rgba(0, 0, 0, 0.3);
}
.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .show > .nav-link {
    color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-toggler {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.1);
}
.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-light .navbar-text {
    color: rgba(0, 0, 0, 0.5);
}
.navbar-light .navbar-text a,
.navbar-light .navbar-text a:focus,
.navbar-light .navbar-text a:hover {
    color: rgba(0, 0, 0, 0.9);
}
.navbar-dark .navbar-brand,
.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover {
    color: #fff;
}
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
}
.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
}
.navbar-dark .navbar-nav .nav-link.disabled {
    color: rgba(255, 255, 255, 0.25);
}
.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .show > .nav-link {
    color: #fff;
}
.navbar-dark .navbar-toggler {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}
.navbar-dark .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.8)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-dark .navbar-text {
    color: rgba(255, 255, 255, 0.8);
}
.navbar-dark .navbar-text a,
.navbar-dark .navbar-text a:focus,
.navbar-dark .navbar-text a:hover {
    color: #fff;
}
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 0 solid transparent;
    border-radius: 0;
}
.card > hr {
    margin-right: 0;
    margin-left: 0;
}
.card > .list-group:first-child .list-group-item:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.card > .list-group:last-child .list-group-item:last-child {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}
.card-footer,
.card-header {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.03);
}
.card-title {
    margin-bottom: 0.75rem;
}
.card-header,
.card-subtitle,
.card-text:last-child {
    margin-bottom: 0;
}
.card-subtitle {
    margin-top: -0.375rem;
}
.card-link:hover {
    text-decoration: none;
}
.card-link + .card-link {
    margin-left: 1.25rem;
}
.card-header-pills,
.card-header-tabs {
    margin-right: -0.625rem;
    margin-left: -0.625rem;
}
.card-header {
    color: inherit;
    border-bottom: 0 solid transparent;
}
.card-header:first-child {
    border-radius: calc(0px - 0px) calc(0px - 0px) 0 0;
}
.card-header + .list-group .list-group-item:first-child {
    border-top: 0;
}
.card-footer {
    border-top: 0 solid transparent;
}
.card-footer:last-child {
    border-radius: 0 0 calc(0px - 0px) calc(0px - 0px);
}
.card-header-tabs {
    margin-bottom: -0.75rem;
    border-bottom: 0;
}
.card-img-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 1.25rem;
}
.alert,
.btn .badge,
.page-link {
    position: relative;
}
.card-img {
    width: 100%;
    border-radius: calc(0px - 0px);
}
.card-img-top {
    width: 100%;
    border-top-left-radius: calc(0px - 0px);
    border-top-right-radius: calc(0px - 0px);
}
.card-img-bottom {
    width: 100%;
    border-bottom-right-radius: calc(0px - 0px);
    border-bottom-left-radius: calc(0px - 0px);
}
.card-deck {
    display: flex;
    flex-direction: column;
}
.card-deck .card {
    margin-bottom: 10px;
}
@media (min-width: 576px) {
    .card-deck {
        flex-flow: row wrap;
        margin-right: -10px;
        margin-left: -10px;
    }
    .card-deck .card {
        display: flex;
        flex: 1 0 0%;
        flex-direction: column;
        margin-right: 10px;
        margin-bottom: 0;
        margin-left: 10px;
    }
}
.card-group {
    display: flex;
    flex-direction: column;
}
.card-group > .card {
    margin-bottom: 10px;
}
@media (min-width: 576px) {
    .card-group > .card:not(:first-child):not(:last-child):not(:only-child),
    .card-group > .card:not(:first-child):not(:last-child):not(:only-child) .card-footer,
    .card-group > .card:not(:first-child):not(:last-child):not(:only-child) .card-header,
    .card-group > .card:not(:first-child):not(:last-child):not(:only-child) .card-img-bottom,
    .card-group > .card:not(:first-child):not(:last-child):not(:only-child) .card-img-top,
    .card-group > .card:only-child {
        border-radius: 0;
    }
    .card-group {
        flex-flow: row wrap;
    }
    .card-group > .card {
        flex: 1 0 0%;
        margin-bottom: 0;
    }
    .card-group > .card + .card {
        margin-left: 0;
        border-left: 0;
    }
    .card-group > .card:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    .card-group > .card:first-child .card-header,
    .card-group > .card:first-child .card-img-top {
        border-top-right-radius: 0;
    }
    .card-group > .card:first-child .card-footer,
    .card-group > .card:first-child .card-img-bottom {
        border-bottom-right-radius: 0;
    }
    .card-group > .card:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    .card-group > .card:last-child .card-header,
    .card-group > .card:last-child .card-img-top {
        border-top-left-radius: 0;
    }
    .card-group > .card:last-child .card-footer,
    .card-group > .card:last-child .card-img-bottom {
        border-bottom-left-radius: 0;
    }
    .card-group > .card:only-child .card-header,
    .card-group > .card:only-child .card-img-top {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }
    .card-group > .card:only-child .card-footer,
    .card-group > .card:only-child .card-img-bottom {
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
    }
    .card-columns {
        column-count: 3;
        column-gap: 1.25rem;
        orphans: 1;
        widows: 1;
    }
    .card-columns .card {
        display: inline-block;
        width: 100%;
    }
}
.card-columns .card {
    margin-bottom: 0.75rem;
}
.accordion .card:not(:first-of-type) .card-header:first-child {
    border-radius: 0;
}
.accordion .card:not(:first-of-type):not(:last-of-type) {
    border-bottom: 0;
    border-radius: 0;
}
.accordion .card:first-of-type {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.accordion .card:last-of-type {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.breadcrumb,
.pagination {
    border-radius: 2px;
    list-style: none;
}
.accordion .card .card-header {
    margin-bottom: 0;
}
.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    padding: 0.75rem 1rem;
    margin-bottom: 1.5rem;
    background-color: #191821;
}
.breadcrumb-item + .breadcrumb-item {
    padding-left: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
    display: inline-block;
    padding-right: 0.5rem;
    color: #6c757d;
    content: "/";
}
.carousel-inner::after,
.clearfix::after,
.embed-responsive::before,
.modal-dialog-centered::before,
.popover .arrow::after,
.popover .arrow::before,
.tooltip .arrow::before {
    content: "";
}
.breadcrumb-item + .breadcrumb-item:hover::before {
    text-decoration: none;
}
.breadcrumb-item.active {
    color: #6c757d;
}
.pagination {
    display: flex;
    padding-left: 0;
}
.page-link {
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #7460ee;
    background-color: #fff;
    border: 1px solid #dee2e6;
}
.page-item:first-child .page-link,
.pagination-lg .page-item:first-child .page-link {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
.page-item:last-child .page-link,
.pagination-lg .page-item:last-child .page-link {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
.page-link:hover {
    z-index: 2;
    color: #381be7;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6;
}
.page-link:focus {
    z-index: 2;
    outline: 0;
    box-shadow: transparent;
}
.page-item:first-child .page-link {
    margin-left: 0;
}
.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: #2962ff;
    border-color: #2962ff;
}
.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}
.pagination-lg .page-link {
    padding: 0.75rem 1.5rem;
    font-size: 1.09375rem;
    line-height: 1.5;
}
.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.76563rem;
    line-height: 1.5;
}
.pagination-sm .page-item:first-child .page-link {
    border-top-left-radius: 1px;
    border-bottom-left-radius: 1px;
}
.pagination-sm .page-item:last-child .page-link {
    border-top-right-radius: 1px;
    border-bottom-right-radius: 1px;
}
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    text-align: center;
    vertical-align: baseline;
    border-radius: 2px;
}
a.badge:focus,
a.badge:hover {
    text-decoration: none;
}
.badge:empty {
    display: none;
}
.btn .badge {
    top: -1px;
}
.badge-pill {
    padding-right: 0.2em;
    padding-left: 0.2em;
    border-radius: 10rem;
}
.badge-primary {
    color: #fff;
    background-color: #7460ee;
}
a.badge-primary:focus,
a.badge-primary:hover {
    color: #fff;
    background-color: #4c32e9;
}
.badge-secondary {
    color: #fff;
    background-color: #6c757d;
}
a.badge-secondary:focus,
a.badge-secondary:hover {
    color: #fff;
    background-color: #545b62;
}
.badge-success {
    color: #fff;
    background-color: #36bea6;
}
a.badge-success:focus,
a.badge-success:hover {
    color: #fff;
    background-color: #2b9683;
}
.badge-info {
    color: #fff;
    background-color: #2962ff;
}
a.badge-info:focus,
a.badge-info:hover {
    color: #fff;
    background-color: #0041f5;
}
.badge-warning {
    color: #212529;
    background-color: #ffbc34;
}
a.badge-warning:focus,
a.badge-warning:hover {
    color: #212529;
    background-color: #ffab01;
}
.badge-danger {
    color: #fff;
    background-color: #f62d51;
}
a.badge-danger:focus,
a.badge-danger:hover {
    color: #fff;
    background-color: #e60a31;
}
.badge-light {
    color: #212529;
    background-color: #f8f9fa;
}
a.badge-light:focus,
a.badge-light:hover {
    color: #212529;
    background-color: #dae0e5;
}
.badge-dark {
    color: #fff;
    background-color: #343a40;
}
a.badge-dark:focus,
a.badge-dark:hover {
    color: #fff;
    background-color: #1d2124;
}
.badge-cyan {
    color: #212529;
    background-color: #4fc3f7;
}
a.badge-cyan:focus,
a.badge-cyan:hover {
    color: #212529;
    background-color: #1eb2f5;
}
.badge-orange {
    color: #212529;
    background-color: #fb8c00;
}
a.badge-orange:focus,
a.badge-orange:hover {
    color: #212529;
    background-color: #c87000;
}
.badge-purple {
    color: #fff;
    background-color: #7460ee;
}
a.badge-purple:focus,
a.badge-purple:hover {
    color: #fff;
    background-color: #4c32e9;
}
.jumbotron {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    background-color: #e9ecef;
    border-radius: 2px;
}
@media (min-width: 576px) {
    .jumbotron {
        padding: 4rem 2rem;
    }
}
.jumbotron-fluid {
    padding-right: 0;
    padding-left: 0;
    border-radius: 0;
}
.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 2px;
}
.alert-heading {
    color: inherit;
}
.alert-dismissible {
    padding-right: 3.8125rem;
}
.alert-dismissible .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.75rem 1.25rem;
    color: inherit;
}
.alert-primary {
    color: #3c327c;
    background-color: #e3dffc;
    border-color: #d8d2fa;
}
.alert-primary hr {
    border-top-color: #c4bbf7;
}
.alert-primary .alert-link {
    color: #2a2358;
}
.alert-secondary {
    color: #383d41;
    background-color: #e2e3e5;
    border-color: #d6d8db;
}
.alert-secondary hr {
    border-top-color: #c8cbcf;
}
.alert-secondary .alert-link {
    color: #202326;
}
.alert-success {
    color: #1c6356;
    background-color: #d7f2ed;
    border-color: #c7ede6;
}
.alert-success hr {
    border-top-color: #b4e7dd;
}
.alert-success .alert-link {
    color: #113b33;
}
.alert-info {
    color: #153385;
    background-color: #d4e0ff;
    border-color: #c3d3ff;
}
.alert-info hr {
    border-top-color: #aac0ff;
}
.alert-info .alert-link {
    color: #0e2259;
}
.alert-warning {
    color: #85621b;
    background-color: #fff2d6;
    border-color: #ffecc6;
}
.alert-warning hr {
    border-top-color: #ffe4ad;
}
.alert-warning .alert-link {
    color: #5b4312;
}
.alert-danger {
    color: #80172a;
    background-color: #fdd5dc;
    border-color: #fcc4ce;
}
.alert-danger hr {
    border-top-color: #fbacba;
}
.alert-danger .alert-link {
    color: #550f1c;
}
.alert-light {
    color: #818182;
    background-color: #fefefe;
    border-color: #fdfdfe;
}
.alert-light hr {
    border-top-color: #ececf6;
}
.alert-light .alert-link {
    color: #686868;
}
.alert-dark {
    color: #1b1e21;
    background-color: #d6d8d9;
    border-color: #c6c8ca;
}
.alert-dark hr {
    border-top-color: #b9bbbe;
}
.alert-dark .alert-link {
    color: #040505;
}
.alert-cyan {
    color: #296580;
    background-color: #dcf3fd;
    border-color: #ceeefd;
}
.alert-cyan hr {
    border-top-color: #b6e6fc;
}
.alert-cyan .alert-link {
    color: #1d4759;
}
.alert-orange {
    color: #834900;
    background-color: #fee8cc;
    border-color: #fedfb8;
}
.alert-orange hr {
    border-top-color: #fed49f;
}
.alert-orange .alert-link {
    color: #502d00;
}
.alert-purple {
    color: #3c327c;
    background-color: #e3dffc;
    border-color: #d8d2fa;
}
.alert-purple hr {
    border-top-color: #c4bbf7;
}
.alert-purple .alert-link {
    color: #2a2358;
}
@keyframes progress-bar-stripes {
    from {
        background-position: 5px 0;
    }
    to {
        background-position: 0 0;
    }
}
.progress {
    display: flex;
    height: 5px;
    font-size: 0.65625rem;
    background-color: #f8f9fa;
    border-radius: 2px;
}
.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #fff;
    text-align: center;
    background-color: #7460ee;
    transition: width 0.6s ease;
}
@media screen and (prefers-reduced-motion: reduce) {
    .progress-bar {
        transition: none;
    }
}
.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 5px 5px;
}
.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}
.media {
    display: flex;
    align-items: flex-start;
}
.media-body {
    flex: 1;
}
.list-group {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
}
.list-group-item-action {
    width: 100%;
    color: #4f5467;
    text-align: inherit;
}
.list-group-item-action:focus,
.list-group-item-action:hover {
    color: #4f5467;
    text-decoration: none;
    background-color: #f8f9fa;
}
.list-group-item-action:active {
    color: #3e5569;
    background-color: #e9ecef;
}
.list-group-item {
    position: relative;
    display: block;
    padding: 0.75rem 1.25rem;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.125);
}
.list-group-item:first-child {
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}
.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
}
.list-group-item:focus,
.list-group-item:hover {
    z-index: 1;
    text-decoration: none;
}
.list-group-item.disabled,
.list-group-item:disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
}
.list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: #2962ff;
    border-color: #2962ff;
}
.list-group-flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0;
}
.list-group-flush .list-group-item:last-child {
    margin-bottom: -1px;
}
.list-group-flush:first-child .list-group-item:first-child {
    border-top: 0;
}
.list-group-flush:last-child .list-group-item:last-child {
    margin-bottom: 0;
    border-bottom: 0;
}
.list-group-item-primary {
    color: #3c327c;
    background-color: #d8d2fa;
}
.list-group-item-primary.list-group-item-action:focus,
.list-group-item-primary.list-group-item-action:hover {
    color: #3c327c;
    background-color: #c4bbf7;
}
.list-group-item-primary.list-group-item-action.active {
    color: #fff;
    background-color: #3c327c;
    border-color: #3c327c;
}
.list-group-item-secondary {
    color: #383d41;
    background-color: #d6d8db;
}
.list-group-item-secondary.list-group-item-action:focus,
.list-group-item-secondary.list-group-item-action:hover {
    color: #383d41;
    background-color: #c8cbcf;
}
.list-group-item-secondary.list-group-item-action.active {
    color: #fff;
    background-color: #383d41;
    border-color: #383d41;
}
.list-group-item-success {
    color: #1c6356;
    background-color: #c7ede6;
}
.list-group-item-success.list-group-item-action:focus,
.list-group-item-success.list-group-item-action:hover {
    color: #1c6356;
    background-color: #b4e7dd;
}
.list-group-item-success.list-group-item-action.active {
    color: #fff;
    background-color: #1c6356;
    border-color: #1c6356;
}
.list-group-item-info {
    color: #153385;
    background-color: #c3d3ff;
}
.list-group-item-info.list-group-item-action:focus,
.list-group-item-info.list-group-item-action:hover {
    color: #153385;
    background-color: #aac0ff;
}
.list-group-item-info.list-group-item-action.active {
    color: #fff;
    background-color: #153385;
    border-color: #153385;
}
.list-group-item-warning {
    color: #85621b;
    background-color: #ffecc6;
}
.list-group-item-warning.list-group-item-action:focus,
.list-group-item-warning.list-group-item-action:hover {
    color: #85621b;
    background-color: #ffe4ad;
}
.list-group-item-warning.list-group-item-action.active {
    color: #fff;
    background-color: #85621b;
    border-color: #85621b;
}
.list-group-item-danger {
    color: #80172a;
    background-color: #fcc4ce;
}
.list-group-item-danger.list-group-item-action:focus,
.list-group-item-danger.list-group-item-action:hover {
    color: #80172a;
    background-color: #fbacba;
}
.list-group-item-danger.list-group-item-action.active {
    color: #fff;
    background-color: #80172a;
    border-color: #80172a;
}
.list-group-item-light {
    color: #818182;
    background-color: #fdfdfe;
}
.list-group-item-light.list-group-item-action:focus,
.list-group-item-light.list-group-item-action:hover {
    color: #818182;
    background-color: #ececf6;
}
.list-group-item-light.list-group-item-action.active {
    color: #fff;
    background-color: #818182;
    border-color: #818182;
}
.list-group-item-dark {
    color: #1b1e21;
    background-color: #c6c8ca;
}
.list-group-item-dark.list-group-item-action:focus,
.list-group-item-dark.list-group-item-action:hover {
    color: #1b1e21;
    background-color: #b9bbbe;
}
.list-group-item-dark.list-group-item-action.active {
    color: #fff;
    background-color: #1b1e21;
    border-color: #1b1e21;
}
.list-group-item-cyan {
    color: #296580;
    background-color: #ceeefd;
}
.list-group-item-cyan.list-group-item-action:focus,
.list-group-item-cyan.list-group-item-action:hover {
    color: #296580;
    background-color: #b6e6fc;
}
.list-group-item-cyan.list-group-item-action.active {
    color: #fff;
    background-color: #296580;
    border-color: #296580;
}
.list-group-item-orange {
    color: #834900;
    background-color: #fedfb8;
}
.list-group-item-orange.list-group-item-action:focus,
.list-group-item-orange.list-group-item-action:hover {
    color: #834900;
    background-color: #fed49f;
}
.list-group-item-orange.list-group-item-action.active {
    color: #fff;
    background-color: #834900;
    border-color: #834900;
}
.list-group-item-purple {
    color: #3c327c;
    background-color: #d8d2fa;
}
.list-group-item-purple.list-group-item-action:focus,
.list-group-item-purple.list-group-item-action:hover {
    color: #3c327c;
    background-color: #c4bbf7;
}
.list-group-item-purple.list-group-item-action.active {
    color: #fff;
    background-color: #3c327c;
    border-color: #3c327c;
}
.close {
    float: right;
    font-size: 1.3125rem;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: 0.5;
}
.close:hover {
    color: #000;
    text-decoration: none;
}
.close:not(:disabled):not(.disabled) {
    cursor: pointer;
}
.close:not(:disabled):not(.disabled):focus,
.close:not(:disabled):not(.disabled):hover {
    opacity: 0.75;
}
button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    appearance: none;
}
.toast,
.toast-header {
    background-color: rgba(255, 255, 255, 0.85);
}
a.close.disabled {
    pointer-events: none;
}
.toast {
    max-width: 350px;
    font-size: 0.875rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.25rem;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    opacity: 0;
}
.popover,
.tooltip {
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: 0.76563rem;
    word-wrap: break-word;
    text-decoration: none;
    text-shadow: none;
}
.toast:not(:last-child) {
    margin-bottom: 0.75rem;
}
.toast.showing {
    opacity: 1;
}
.toast.show {
    display: block;
    opacity: 1;
}
.toast.hide {
    display: none;
}
.toast-header {
    display: flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    color: #6c757d;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.toast-body {
    padding: 0.75rem;
}
.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
}
.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}
@media screen and (prefers-reduced-motion: reduce) {
    .modal.fade .modal-dialog {
        transition: none;
    }
}
.modal.show .modal-dialog {
    transform: none;
}
.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - (0.5rem * 2));
}
.modal-dialog-centered::before {
    display: block;
    height: calc(100vh - (0.5rem * 2));
}
.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 2px;
    outline: 0;
}
.chartist-tooltip,
.ct-area,
.ct-line,
.dd-dragel,
.waves-effect .waves-ripple {
    pointer-events: none;
}
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}
.modal-backdrop.fade {
    opacity: 0;
}
.modal-backdrop.show {
    opacity: 0.5;
}
.modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}
.modal-header .close {
    padding: 1rem;
    margin: -1rem -1rem -1rem auto;
}
.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
}
.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
}
.popover,
.popover .arrow,
.popover .arrow::after,
.popover .arrow::before,
.tooltip,
.tooltip .arrow {
    position: absolute;
    display: block;
}
.modal-footer > :not(:first-child) {
    margin-left: 0.25rem;
}
.modal-footer > :not(:last-child) {
    margin-right: 0.25rem;
}
.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll;
}
@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
    .modal-dialog-centered {
        min-height: calc(100% - (1.75rem * 2));
    }
    .modal-dialog-centered::before {
        height: calc(100vh - (1.75rem * 2));
    }
    .modal-sm {
        max-width: 300px;
    }
}
@media (min-width: 992px) {
    .modal-lg,
    .modal-xl {
        max-width: 800px;
    }
}
@media (min-width: 1600px) {
    .modal-xl {
        max-width: 1140px;
    }
}
.tooltip {
    z-index: 1070;
    margin: 0;
    text-align: left;
    text-align: start;
    opacity: 0;
}
.tooltip.show {
    opacity: 0.9;
}
.tooltip .arrow {
    width: 0.8rem;
    height: 0.4rem;
}
.tooltip .arrow::before {
    position: absolute;
    border-color: transparent;
    border-style: solid;
}
.bs-tooltip-auto[x-placement^="top"],
.bs-tooltip-top {
    padding: 0.4rem 0;
}
.bs-tooltip-auto[x-placement^="top"] .arrow,
.bs-tooltip-top .arrow {
    bottom: 0;
}
.bs-tooltip-auto[x-placement^="top"] .arrow::before,
.bs-tooltip-top .arrow::before {
    top: 0;
    border-width: 0.4rem 0.4rem 0;
    border-top-color: #000;
}
.bs-tooltip-auto[x-placement^="right"],
.bs-tooltip-right {
    padding: 0 0.4rem;
}
.bs-tooltip-auto[x-placement^="right"] .arrow,
.bs-tooltip-right .arrow {
    left: 0;
    width: 0.4rem;
    height: 0.8rem;
}
.bs-tooltip-auto[x-placement^="right"] .arrow::before,
.bs-tooltip-right .arrow::before {
    right: 0;
    border-width: 0.4rem 0.4rem 0.4rem 0;
    border-right-color: #000;
}
.bs-tooltip-auto[x-placement^="bottom"],
.bs-tooltip-bottom {
    padding: 0.4rem 0;
}
.bs-tooltip-auto[x-placement^="bottom"] .arrow,
.bs-tooltip-bottom .arrow {
    top: 0;
}
.bs-tooltip-auto[x-placement^="bottom"] .arrow::before,
.bs-tooltip-bottom .arrow::before {
    bottom: 0;
    border-width: 0 0.4rem 0.4rem;
    border-bottom-color: #000;
}
.bs-tooltip-auto[x-placement^="left"],
.bs-tooltip-left {
    padding: 0 0.4rem;
}
.bs-tooltip-auto[x-placement^="left"] .arrow,
.bs-tooltip-left .arrow {
    right: 0;
    width: 0.4rem;
    height: 0.8rem;
}
.bs-tooltip-auto[x-placement^="left"] .arrow::before,
.bs-tooltip-left .arrow::before {
    left: 0;
    border-width: 0.4rem 0 0.4rem 0.4rem;
    border-left-color: #000;
}
.tooltip-inner {
    max-width: 200px;
    padding: 0.25rem 0.5rem;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 2px;
}
.popover {
    top: 0;
    left: 0;
    z-index: 1060;
    max-width: 276px;
    text-align: left;
    text-align: start;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}
.popover .arrow {
    width: 1rem;
    height: 0.5rem;
    margin: 0 2px;
}
.popover .arrow::after,
.popover .arrow::before {
    border-color: transparent;
    border-style: solid;
}
.bs-popover-auto[x-placement^="top"],
.bs-popover-top {
    margin-bottom: 0.5rem;
}
.bs-popover-auto[x-placement^="top"] .arrow,
.bs-popover-top .arrow {
    bottom: calc((0.5rem + 1px) * -1);
}
.bs-popover-auto[x-placement^="top"] .arrow::after,
.bs-popover-auto[x-placement^="top"] .arrow::before,
.bs-popover-top .arrow::after,
.bs-popover-top .arrow::before {
    border-width: 0.5rem 0.5rem 0;
}
.bs-popover-auto[x-placement^="top"] .arrow::before,
.bs-popover-top .arrow::before {
    bottom: 0;
    border-top-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-auto[x-placement^="top"] .arrow::after,
.bs-popover-top .arrow::after {
    bottom: 1px;
    border-top-color: #fff;
}
.bs-popover-auto[x-placement^="right"],
.bs-popover-right {
    margin-left: 0.5rem;
}
.bs-popover-auto[x-placement^="right"] .arrow,
.bs-popover-right .arrow {
    left: calc((0.5rem + 1px) * -1);
    width: 0.5rem;
    height: 1rem;
    margin: 2px 0;
}
.bs-popover-auto[x-placement^="right"] .arrow::after,
.bs-popover-auto[x-placement^="right"] .arrow::before,
.bs-popover-right .arrow::after,
.bs-popover-right .arrow::before {
    border-width: 0.5rem 0.5rem 0.5rem 0;
}
.bs-popover-auto[x-placement^="right"] .arrow::before,
.bs-popover-right .arrow::before {
    left: 0;
    border-right-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-auto[x-placement^="right"] .arrow::after,
.bs-popover-right .arrow::after {
    left: 1px;
    border-right-color: #fff;
}
.bs-popover-auto[x-placement^="bottom"],
.bs-popover-bottom {
    margin-top: 0.5rem;
}
.bs-popover-auto[x-placement^="bottom"] .arrow,
.bs-popover-bottom .arrow {
    top: calc((0.5rem + 1px) * -1);
}
.bs-popover-auto[x-placement^="bottom"] .arrow::after,
.bs-popover-auto[x-placement^="bottom"] .arrow::before,
.bs-popover-bottom .arrow::after,
.bs-popover-bottom .arrow::before {
    border-width: 0 0.5rem 0.5rem;
}
.bs-popover-auto[x-placement^="bottom"] .arrow::before,
.bs-popover-bottom .arrow::before {
    top: 0;
    border-bottom-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-auto[x-placement^="bottom"] .arrow::after,
.bs-popover-bottom .arrow::after {
    top: 1px;
    border-bottom-color: #fff;
}
.bs-popover-auto[x-placement^="bottom"] .popover-header::before,
.bs-popover-bottom .popover-header::before {
    position: absolute;
    top: 0;
    left: 50%;
    display: block;
    width: 1rem;
    margin-left: -0.5rem;
    content: "";
    border-bottom: 1px solid #f7f7f7;
}
.carousel,
.carousel-inner,
.carousel-item {
    position: relative;
}
.bs-popover-auto[x-placement^="left"],
.bs-popover-left {
    margin-right: 0.5rem;
}
.bs-popover-auto[x-placement^="left"] .arrow,
.bs-popover-left .arrow {
    right: calc((0.5rem + 1px) * -1);
    width: 0.5rem;
    height: 1rem;
    margin: 2px 0;
}
.bs-popover-auto[x-placement^="left"] .arrow::after,
.bs-popover-auto[x-placement^="left"] .arrow::before,
.bs-popover-left .arrow::after,
.bs-popover-left .arrow::before {
    border-width: 0.5rem 0 0.5rem 0.5rem;
}
.bs-popover-auto[x-placement^="left"] .arrow::before,
.bs-popover-left .arrow::before {
    right: 0;
    border-left-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-auto[x-placement^="left"] .arrow::after,
.bs-popover-left .arrow::after {
    right: 1px;
    border-left-color: #fff;
}
.popover-header {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: inherit;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-top-left-radius: calc(2px - 1px);
    border-top-right-radius: calc(2px - 1px);
}
.popover-header:empty {
    display: none;
}
.popover-body {
    padding: 0.5rem 0.75rem;
    color: #3e5569;
}
.carousel.pointer-event {
    touch-action: pan-y;
}
.carousel-inner {
    width: 100%;
    overflow: hidden;
}
.carousel-inner::after {
    display: block;
    clear: both;
}
.carousel-item {
    display: none;
    float: left;
    width: 100%;
    margin-right: -100%;
    backface-visibility: hidden;
    transition: transform 0.6s ease-in-out;
}
@media screen and (prefers-reduced-motion: reduce) {
    .carousel-item {
        transition: none;
    }
}
.carousel-item-next,
.carousel-item-prev,
.carousel-item.active {
    display: block;
}
.active.carousel-item-right,
.carousel-item-next:not(.carousel-item-left) {
    transform: translateX(100%);
}
.active.carousel-item-left,
.carousel-item-prev:not(.carousel-item-right) {
    transform: translateX(-100%);
}
.carousel-fade .carousel-item {
    opacity: 0;
    transition-property: opacity;
    transform: none;
}
.carousel-fade .carousel-item-next.carousel-item-left,
.carousel-fade .carousel-item-prev.carousel-item-right,
.carousel-fade .carousel-item.active {
    z-index: 1;
    opacity: 1;
}
.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
    z-index: 0;
    opacity: 0;
    transition: 0s 0.6s opacity;
}
@media screen and (prefers-reduced-motion: reduce) {
    .carousel-fade .active.carousel-item-left,
    .carousel-fade .active.carousel-item-right {
        transition: none;
    }
}
.carousel-control-next,
.carousel-control-prev {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 15%;
    color: #fff;
    text-align: center;
    opacity: 0.5;
    transition: opacity 0.15s ease;
}
@media screen and (prefers-reduced-motion: reduce) {
    .carousel-control-next,
    .carousel-control-prev {
        transition: none;
    }
}
.carousel-control-next:focus,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-prev:hover {
    color: #fff;
    text-decoration: none;
    outline: 0;
    opacity: 0.9;
}
.carousel-control-prev {
    left: 0;
}
.carousel-control-next {
    right: 0;
}
.carousel-control-next-icon,
.carousel-control-prev-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: center center no-repeat;
    background-size: 100% 100%;
}
.carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e");
}
.carousel-control-next-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e");
}
.carousel-indicators {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 15;
    display: flex;
    justify-content: center;
    padding-left: 0;
    margin-right: 15%;
    margin-left: 15%;
    list-style: none;
}
.spinner-border,
.spinner-grow {
    display: inline-block;
    vertical-align: text-bottom;
}
.carousel-indicators li {
    box-sizing: content-box;
    flex: 0 1 auto;
    width: 30px;
    height: 3px;
    margin-right: 3px;
    margin-left: 3px;
    text-indent: -999px;
    cursor: pointer;
    background-color: #fff;
    background-clip: padding-box;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    opacity: 0.5;
    transition: opacity 0.6s ease;
}
@media screen and (prefers-reduced-motion: reduce) {
    .carousel-indicators li {
        transition: none;
    }
}
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header,
.marketking_dashboard_page_wrapper {
    transition: 0.2s ease-in;
}
.carousel-indicators .active {
    opacity: 1;
}
.carousel-caption {
    position: absolute;
    right: 15%;
    bottom: 20px;
    left: 15%;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #fff;
    text-align: center;
}
@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}
.spinner-border {
    width: 2rem;
    height: 2rem;
    border: 0.25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.2em;
}
@keyframes spinner-grow {
    0% {
        transform: scale(0);
    }
    50% {
        opacity: 1;
    }
}
.spinner-grow {
    width: 2rem;
    height: 2rem;
    background-color: currentColor;
    border-radius: 50%;
    opacity: 0;
    animation: spinner-grow 0.75s linear infinite;
}
.spinner-grow-sm {
    width: 1rem;
    height: 1rem;
}
.align-baseline {
    vertical-align: baseline !important;
}
.align-top {
    vertical-align: top !important;
}
.align-middle {
    vertical-align: middle !important;
}
.align-bottom {
    vertical-align: bottom !important;
}
.align-text-bottom {
    vertical-align: text-bottom !important;
}
.align-text-top {
    vertical-align: text-top !important;
}
.cell,
.v-middle td,
.v-middle th,
.vm.table td,
.vm.table th,
.waves-effect {
    vertical-align: middle;
}
.bg-primary {
    background-color: #7460ee !important;
}
a.bg-primary:focus,
a.bg-primary:hover,
button.bg-primary:focus,
button.bg-primary:hover {
    background-color: #4c32e9 !important;
}
.bg-secondary {
    background-color: #6c757d !important;
}
a.bg-secondary:focus,
a.bg-secondary:hover,
button.bg-secondary:focus,
button.bg-secondary:hover {
    background-color: #545b62 !important;
}
.bg-success {
    background-color: #36bea6 !important;
}
a.bg-success:focus,
a.bg-success:hover,
button.bg-success:focus,
button.bg-success:hover {
    background-color: #2b9683 !important;
}
.bg-info {
    background-color: #2962ff !important;
}
a.bg-info:focus,
a.bg-info:hover,
button.bg-info:focus,
button.bg-info:hover {
    background-color: #0041f5 !important;
}
.bg-warning {
    background-color: #ffbc34 !important;
}
a.bg-warning:focus,
a.bg-warning:hover,
button.bg-warning:focus,
button.bg-warning:hover {
    background-color: #ffab01 !important;
}
.bg-danger {
    background-color: #f62d51 !important;
}
a.bg-danger:focus,
a.bg-danger:hover,
button.bg-danger:focus,
button.bg-danger:hover {
    background-color: #e60a31 !important;
}
.bg-light {
    background-color: #f8f9fa !important;
}
a.bg-light:focus,
a.bg-light:hover,
button.bg-light:focus,
button.bg-light:hover {
    background-color: #dae0e5 !important;
}
.bg-dark {
    background-color: #343a40 !important;
}
a.bg-dark:focus,
a.bg-dark:hover,
button.bg-dark:focus,
button.bg-dark:hover {
    background-color: #1d2124 !important;
}
.bg-cyan {
    background-color: #4fc3f7 !important;
}
a.bg-cyan:focus,
a.bg-cyan:hover,
button.bg-cyan:focus,
button.bg-cyan:hover {
    background-color: #1eb2f5 !important;
}
.bg-orange {
    background-color: #fb8c00 !important;
}
a.bg-orange:focus,
a.bg-orange:hover,
button.bg-orange:focus,
button.bg-orange:hover {
    background-color: #c87000 !important;
}
.bg-purple {
    background-color: #7460ee !important;
}
a.bg-purple:focus,
a.bg-purple:hover,
button.bg-purple:focus,
button.bg-purple:hover {
    background-color: #4c32e9 !important;
}
.bg-white {
    background-color: #fff !important;
}
.bg-transparent {
    background-color: transparent !important;
}
.border {
    border: 1px solid #e9ecef !important;
}
.border-top {
    border-top: 1px solid #e9ecef !important;
}
.border-right {
    border-right: 1px solid #e9ecef !important;
}
.border-bottom {
    border-bottom: 1px solid #e9ecef !important;
}
.border-left {
    border-left: 1px solid #e9ecef !important;
}
.border-0 {
    border: 0 !important;
}
.rounded-right,
.rounded-top {
    border-top-right-radius: 2px !important;
}
.rounded-bottom,
.rounded-right {
    border-bottom-right-radius: 2px !important;
}
.rounded-left,
.rounded-top {
    border-top-left-radius: 2px !important;
}
.rounded-bottom,
.rounded-left {
    border-bottom-left-radius: 2px !important;
}
.border-top-0 {
    border-top: 0 !important;
}
.border-right-0 {
    border-right: 0 !important;
}
.border-bottom-0 {
    border-bottom: 0 !important;
}
.border-left-0 {
    border-left: 0 !important;
}
.border-primary {
    border-color: #7460ee !important;
}
.border-secondary {
    border-color: #6c757d !important;
}
.border-success {
    border-color: #36bea6 !important;
}
.border-info {
    border-color: #2962ff !important;
}
.border-warning {
    border-color: #ffbc34 !important;
}
.border-danger {
    border-color: #f62d51 !important;
}
.border-light {
    border-color: #f8f9fa !important;
}
.border-dark {
    border-color: #343a40 !important;
}
.border-cyan {
    border-color: #4fc3f7 !important;
}
.border-orange {
    border-color: #fb8c00 !important;
}
.border-purple {
    border-color: #7460ee !important;
}
.border-white {
    border-color: #fff !important;
}
.rounded {
    border-radius: 2px !important;
}
.rounded-circle {
    border-radius: 50% !important;
}
.rounded-pill {
    border-radius: 50rem !important;
}
.rounded-0 {
    border-radius: 0 !important;
}
.clearfix::after {
    display: block;
    clear: both;
}
.d-none {
    display: none !important;
}
.d-inline {
    display: inline !important;
}
.d-inline-block {
    display: inline-block !important;
}
.d-block {
    display: block !important;
}
.d-table {
    display: table !important;
}
.d-table-row {
    display: table-row !important;
}
.d-table-cell {
    display: table-cell !important;
}
.d-flex {
    display: flex !important;
}
.d-inline-flex {
    display: inline-flex !important;
}
@media (min-width: 576px) {
    .d-sm-none {
        display: none !important;
    }
    .d-sm-inline {
        display: inline !important;
    }
    .d-sm-inline-block {
        display: inline-block !important;
    }
    .d-sm-block {
        display: block !important;
    }
    .d-sm-table {
        display: table !important;
    }
    .d-sm-table-row {
        display: table-row !important;
    }
    .d-sm-table-cell {
        display: table-cell !important;
    }
    .d-sm-flex {
        display: flex !important;
    }
    .d-sm-inline-flex {
        display: inline-flex !important;
    }
}
@media (min-width: 768px) {
    .d-md-none {
        display: none !important;
    }
    .d-md-inline {
        display: inline !important;
    }
    .d-md-inline-block {
        display: inline-block !important;
    }
    .d-md-block {
        display: block !important;
    }
    .d-md-table {
        display: table !important;
    }
    .d-md-table-row {
        display: table-row !important;
    }
    .d-md-table-cell {
        display: table-cell !important;
    }
    .d-md-flex {
        display: flex !important;
    }
    .d-md-inline-flex {
        display: inline-flex !important;
    }
}
@media (min-width: 992px) {
    .d-lg-none {
        display: none !important;
    }
    .d-lg-inline {
        display: inline !important;
    }
    .d-lg-inline-block {
        display: inline-block !important;
    }
    .d-lg-block {
        display: block !important;
    }
    .d-lg-table {
        display: table !important;
    }
    .d-lg-table-row {
        display: table-row !important;
    }
    .d-lg-table-cell {
        display: table-cell !important;
    }
    .d-lg-flex {
        display: flex !important;
    }
    .d-lg-inline-flex {
        display: inline-flex !important;
    }
}
@media (min-width: 1600px) {
    .d-xl-none {
        display: none !important;
    }
    .d-xl-inline {
        display: inline !important;
    }
    .d-xl-inline-block {
        display: inline-block !important;
    }
    .d-xl-block {
        display: block !important;
    }
    .d-xl-table {
        display: table !important;
    }
    .d-xl-table-row {
        display: table-row !important;
    }
    .d-xl-table-cell {
        display: table-cell !important;
    }
    .d-xl-flex {
        display: flex !important;
    }
    .d-xl-inline-flex {
        display: inline-flex !important;
    }
}
@media print {
    .d-print-none {
        display: none !important;
    }
    .d-print-inline {
        display: inline !important;
    }
    .d-print-inline-block {
        display: inline-block !important;
    }
    .d-print-block {
        display: block !important;
    }
    .d-print-table {
        display: table !important;
    }
    .d-print-table-row {
        display: table-row !important;
    }
    .d-print-table-cell {
        display: table-cell !important;
    }
    .d-print-flex {
        display: flex !important;
    }
    .d-print-inline-flex {
        display: inline-flex !important;
    }
}
.embed-responsive {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    overflow: hidden;
}
.embed-responsive::before {
    display: block;
}
.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}
.embed-responsive-21by9::before {
    padding-top: 42.85714%;
}
.embed-responsive-16by9::before {
    padding-top: 56.25%;
}
.embed-responsive-3by4::before {
    padding-top: 133.33333%;
}
.embed-responsive-1by1::before {
    padding-top: 100%;
}
.flex-row {
    flex-direction: row !important;
}
.flex-column {
    flex-direction: column !important;
}
.flex-row-reverse {
    flex-direction: row-reverse !important;
}
.flex-column-reverse {
    flex-direction: column-reverse !important;
}
.flex-wrap {
    flex-wrap: wrap !important;
}
.flex-nowrap {
    flex-wrap: nowrap !important;
}
.flex-wrap-reverse {
    flex-wrap: wrap-reverse !important;
}
.flex-fill {
    flex: 1 1 auto !important;
}
.flex-grow-0 {
    flex-grow: 0 !important;
}
.flex-grow-1 {
    flex-grow: 1 !important;
}
.flex-shrink-0 {
    flex-shrink: 0 !important;
}
.flex-shrink-1 {
    flex-shrink: 1 !important;
}
.justify-content-start {
    justify-content: flex-start !important;
}
.justify-content-end {
    justify-content: flex-end !important;
}
.justify-content-center {
    justify-content: center !important;
}
.justify-content-between {
    justify-content: space-between !important;
}
.justify-content-around {
    justify-content: space-around !important;
}
.align-items-start {
    align-items: flex-start !important;
}
.align-items-end {
    align-items: flex-end !important;
}
.align-items-center {
    align-items: center !important;
}
.align-items-baseline {
    align-items: baseline !important;
}
.align-items-stretch {
    align-items: stretch !important;
}
.align-content-start {
    align-content: flex-start !important;
}
.align-content-end {
    align-content: flex-end !important;
}
.align-content-center {
    align-content: center !important;
}
.align-content-between {
    align-content: space-between !important;
}
.align-content-around {
    align-content: space-around !important;
}
.align-content-stretch {
    align-content: stretch !important;
}
.align-self-auto {
    align-self: auto !important;
}
.align-self-start {
    align-self: flex-start !important;
}
.align-self-end {
    align-self: flex-end !important;
}
.align-self-center {
    align-self: center !important;
}
.align-self-baseline {
    align-self: baseline !important;
}
.align-self-stretch {
    align-self: stretch !important;
}
@media (min-width: 576px) {
    .flex-sm-row {
        flex-direction: row !important;
    }
    .flex-sm-column {
        flex-direction: column !important;
    }
    .flex-sm-row-reverse {
        flex-direction: row-reverse !important;
    }
    .flex-sm-column-reverse {
        flex-direction: column-reverse !important;
    }
    .flex-sm-wrap {
        flex-wrap: wrap !important;
    }
    .flex-sm-nowrap {
        flex-wrap: nowrap !important;
    }
    .flex-sm-wrap-reverse {
        flex-wrap: wrap-reverse !important;
    }
    .flex-sm-fill {
        flex: 1 1 auto !important;
    }
    .flex-sm-grow-0 {
        flex-grow: 0 !important;
    }
    .flex-sm-grow-1 {
        flex-grow: 1 !important;
    }
    .flex-sm-shrink-0 {
        flex-shrink: 0 !important;
    }
    .flex-sm-shrink-1 {
        flex-shrink: 1 !important;
    }
    .justify-content-sm-start {
        justify-content: flex-start !important;
    }
    .justify-content-sm-end {
        justify-content: flex-end !important;
    }
    .justify-content-sm-center {
        justify-content: center !important;
    }
    .justify-content-sm-between {
        justify-content: space-between !important;
    }
    .justify-content-sm-around {
        justify-content: space-around !important;
    }
    .align-items-sm-start {
        align-items: flex-start !important;
    }
    .align-items-sm-end {
        align-items: flex-end !important;
    }
    .align-items-sm-center {
        align-items: center !important;
    }
    .align-items-sm-baseline {
        align-items: baseline !important;
    }
    .align-items-sm-stretch {
        align-items: stretch !important;
    }
    .align-content-sm-start {
        align-content: flex-start !important;
    }
    .align-content-sm-end {
        align-content: flex-end !important;
    }
    .align-content-sm-center {
        align-content: center !important;
    }
    .align-content-sm-between {
        align-content: space-between !important;
    }
    .align-content-sm-around {
        align-content: space-around !important;
    }
    .align-content-sm-stretch {
        align-content: stretch !important;
    }
    .align-self-sm-auto {
        align-self: auto !important;
    }
    .align-self-sm-start {
        align-self: flex-start !important;
    }
    .align-self-sm-end {
        align-self: flex-end !important;
    }
    .align-self-sm-center {
        align-self: center !important;
    }
    .align-self-sm-baseline {
        align-self: baseline !important;
    }
    .align-self-sm-stretch {
        align-self: stretch !important;
    }
}
@media (min-width: 768px) {
    .flex-md-row {
        flex-direction: row !important;
    }
    .flex-md-column {
        flex-direction: column !important;
    }
    .flex-md-row-reverse {
        flex-direction: row-reverse !important;
    }
    .flex-md-column-reverse {
        flex-direction: column-reverse !important;
    }
    .flex-md-wrap {
        flex-wrap: wrap !important;
    }
    .flex-md-nowrap {
        flex-wrap: nowrap !important;
    }
    .flex-md-wrap-reverse {
        flex-wrap: wrap-reverse !important;
    }
    .flex-md-fill {
        flex: 1 1 auto !important;
    }
    .flex-md-grow-0 {
        flex-grow: 0 !important;
    }
    .flex-md-grow-1 {
        flex-grow: 1 !important;
    }
    .flex-md-shrink-0 {
        flex-shrink: 0 !important;
    }
    .flex-md-shrink-1 {
        flex-shrink: 1 !important;
    }
    .justify-content-md-start {
        justify-content: flex-start !important;
    }
    .justify-content-md-end {
        justify-content: flex-end !important;
    }
    .justify-content-md-center {
        justify-content: center !important;
    }
    .justify-content-md-between {
        justify-content: space-between !important;
    }
    .justify-content-md-around {
        justify-content: space-around !important;
    }
    .align-items-md-start {
        align-items: flex-start !important;
    }
    .align-items-md-end {
        align-items: flex-end !important;
    }
    .align-items-md-center {
        align-items: center !important;
    }
    .align-items-md-baseline {
        align-items: baseline !important;
    }
    .align-items-md-stretch {
        align-items: stretch !important;
    }
    .align-content-md-start {
        align-content: flex-start !important;
    }
    .align-content-md-end {
        align-content: flex-end !important;
    }
    .align-content-md-center {
        align-content: center !important;
    }
    .align-content-md-between {
        align-content: space-between !important;
    }
    .align-content-md-around {
        align-content: space-around !important;
    }
    .align-content-md-stretch {
        align-content: stretch !important;
    }
    .align-self-md-auto {
        align-self: auto !important;
    }
    .align-self-md-start {
        align-self: flex-start !important;
    }
    .align-self-md-end {
        align-self: flex-end !important;
    }
    .align-self-md-center {
        align-self: center !important;
    }
    .align-self-md-baseline {
        align-self: baseline !important;
    }
    .align-self-md-stretch {
        align-self: stretch !important;
    }
}
.float-left {
    float: left !important;
}
.float-right,
html[dir="rtl"] .float-left {
    float: right !important;
}
.float-none {
    float: none !important;
}
@media (min-width: 576px) {
    .float-sm-left {
        float: left !important;
    }
    .float-sm-right {
        float: right !important;
    }
    .float-sm-none {
        float: none !important;
    }
}
@media (min-width: 768px) {
    .float-md-left {
        float: left !important;
    }
    .float-md-right {
        float: right !important;
    }
    .float-md-none {
        float: none !important;
    }
}
.overflow-auto {
    overflow: auto !important;
}
.overflow-hidden {
    overflow: hidden !important;
}
.position-static {
    position: static !important;
}
.position-relative {
    position: relative !important;
}
.position-absolute {
    position: absolute !important;
}
.position-fixed {
    position: fixed !important;
}
.position-sticky {
    position: sticky !important;
}
.fixed-bottom,
.fixed-top {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030;
}
.fixed-top {
    top: 0;
}
.fixed-bottom {
    bottom: 0;
}
@supports (position: sticky) {
    .sticky-top {
        position: sticky;
        top: 0;
        z-index: 1020;
    }
}
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    overflow: visible;
    clip: auto;
    white-space: normal;
}
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}
.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}
.shadow-none {
    box-shadow: none !important;
}
.w-25 {
    width: 25% !important;
}
.w-50 {
    width: 50% !important;
}
.w-75 {
    width: 75% !important;
}
.w-100 {
    width: 100% !important;
}
.w-auto {
    width: auto !important;
}
.h-25 {
    height: 25% !important;
}
.h-50 {
    height: 50% !important;
}
.h-75 {
    height: 75% !important;
}
.h-100 {
    height: 100% !important;
}
.h-auto {
    height: auto !important;
}
.mw-100 {
    max-width: 100% !important;
}
.mh-100 {
    max-height: 100% !important;
}
.min-vw-100 {
    min-width: 100vw !important;
}
.min-vh-100 {
    min-height: 100vh !important;
}
.vw-100 {
    width: 100vw !important;
}
.vh-100 {
    height: 100vh !important;
}
.m-0 {
    margin: 0 !important;
}
.mt-0,
.my-0 {
    margin-top: 0 !important;
}
.mr-0,
.mx-0 {
    margin-right: 0 !important;
}
.mb-0,
.my-0 {
    margin-bottom: 0 !important;
}
.ml-0,
.mx-0 {
    margin-left: 0 !important;
}
.m-1 {
    margin: 0.25rem !important;
}
.mt-1,
.my-1 {
    margin-top: 0.25rem !important;
}
.mr-1,
.mx-1 {
    margin-right: 0.25rem !important;
}
.mb-1,
.my-1 {
    margin-bottom: 0.25rem !important;
}
.ml-1,
.mx-1 {
    margin-left: 0.25rem !important;
}
.m-2 {
    margin: 0.5rem !important;
}
.mt-2,
.my-2 {
    margin-top: 0.5rem !important;
}
.mr-2,
.mx-2 {
    margin-right: 0.5rem !important;
}
.mb-2,
.my-2 {
    margin-bottom: 0.5rem !important;
}
.ml-2,
.mx-2 {
    margin-left: 0.5rem !important;
}
.m-3 {
    margin: 1rem !important;
}
.mt-3,
.my-3 {
    margin-top: 1rem !important;
}
.mr-3,
.mx-3 {
    margin-right: 1rem !important;
}
.mb-3,
.my-3 {
    margin-bottom: 1rem !important;
}
.ml-3,
.mx-3 {
    margin-left: 1rem !important;
}
.m-4 {
    margin: 1.5rem !important;
}
.mt-4,
.my-4 {
    margin-top: 1.5rem !important;
}
.mr-4,
.mx-4 {
    margin-right: 1.5rem !important;
}
.mb-4,
.my-4 {
    margin-bottom: 1.5rem !important;
}
.ml-4,
.mx-4 {
    margin-left: 1.5rem !important;
}
.m-5 {
    margin: 3rem !important;
}
.mt-5,
.my-5 {
    margin-top: 3rem !important;
}
.mr-5,
.mx-5 {
    margin-right: 3rem !important;
}
.mb-5,
.my-5 {
    margin-bottom: 3rem !important;
}
.ml-5,
.mx-5 {
    margin-left: 3rem !important;
}
.p-0 {
    padding: 0 !important;
}
.pt-0,
.py-0 {
    padding-top: 0 !important;
}
.pr-0,
.px-0 {
    padding-right: 0 !important;
}
.pb-0,
.py-0 {
    padding-bottom: 0 !important;
}
.pl-0,
.px-0 {
    padding-left: 0 !important;
}
.p-1 {
    padding: 0.25rem !important;
}
.pt-1,
.py-1 {
    padding-top: 0.25rem !important;
}
.pr-1,
.px-1 {
    padding-right: 0.25rem !important;
}
.pb-1,
.py-1 {
    padding-bottom: 0.25rem !important;
}
.pl-1,
.px-1 {
    padding-left: 0.25rem !important;
}
.p-2 {
    padding: 0.5rem !important;
}
.pt-2,
.py-2 {
    padding-top: 0.5rem !important;
}
.pr-2,
.px-2 {
    padding-right: 0.5rem !important;
}
.pb-2,
.py-2 {
    padding-bottom: 0.5rem !important;
}
.pl-2,
.px-2 {
    padding-left: 0.5rem !important;
}
.p-3 {
    padding: 1rem !important;
}
.pt-3,
.py-3 {
    padding-top: 1rem !important;
}
.pr-3,
.px-3 {
    padding-right: 1rem !important;
}
.pb-3,
.py-3 {
    padding-bottom: 1rem !important;
}
.pl-3,
.px-3 {
    padding-left: 1rem !important;
}
.p-4 {
    padding: 1.5rem !important;
}
.pt-4,
.py-4 {
    padding-top: 1.5rem !important;
}
.pr-4,
.px-4 {
    padding-right: 1.5rem !important;
}
.pb-4,
.py-4 {
    padding-bottom: 1.5rem !important;
}
.pl-4,
.px-4 {
    padding-left: 1.5rem !important;
}
.p-5 {
    padding: 3rem !important;
}
.pt-5,
.py-5 {
    padding-top: 3rem !important;
}
.pr-5,
.px-5 {
    padding-right: 3rem !important;
}
.pb-5,
.py-5 {
    padding-bottom: 3rem !important;
}
.pl-5,
.px-5 {
    padding-left: 3rem !important;
}
.m-n1 {
    margin: -0.25rem !important;
}
.mt-n1,
.my-n1 {
    margin-top: -0.25rem !important;
}
.mr-n1,
.mx-n1 {
    margin-right: -0.25rem !important;
}
.mb-n1,
.my-n1 {
    margin-bottom: -0.25rem !important;
}
.ml-n1,
.mx-n1 {
    margin-left: -0.25rem !important;
}
.m-n2 {
    margin: -0.5rem !important;
}
.mt-n2,
.my-n2 {
    margin-top: -0.5rem !important;
}
.mr-n2,
.mx-n2 {
    margin-right: -0.5rem !important;
}
.mb-n2,
.my-n2 {
    margin-bottom: -0.5rem !important;
}
.ml-n2,
.mx-n2 {
    margin-left: -0.5rem !important;
}
.m-n3 {
    margin: -1rem !important;
}
.mt-n3,
.my-n3 {
    margin-top: -1rem !important;
}
.mr-n3,
.mx-n3 {
    margin-right: -1rem !important;
}
.mb-n3,
.my-n3 {
    margin-bottom: -1rem !important;
}
.ml-n3,
.mx-n3 {
    margin-left: -1rem !important;
}
.m-n4 {
    margin: -1.5rem !important;
}
.mt-n4,
.my-n4 {
    margin-top: -1.5rem !important;
}
.mr-n4,
.mx-n4 {
    margin-right: -1.5rem !important;
}
.mb-n4,
.my-n4 {
    margin-bottom: -1.5rem !important;
}
.ml-n4,
.mx-n4 {
    margin-left: -1.5rem !important;
}
.m-n5 {
    margin: -3rem !important;
}
.mt-n5,
.my-n5 {
    margin-top: -3rem !important;
}
.mr-n5,
.mx-n5 {
    margin-right: -3rem !important;
}
.mb-n5,
.my-n5 {
    margin-bottom: -3rem !important;
}
.ml-n5,
.mx-n5 {
    margin-left: -3rem !important;
}
.m-auto {
    margin: auto !important;
}
.mt-auto,
.my-auto {
    margin-top: auto !important;
}
.mr-auto,
.mx-auto {
    margin-right: auto !important;
}
.mb-auto,
.my-auto {
    margin-bottom: auto !important;
}
.ml-auto,
.mx-auto {
    margin-left: auto !important;
}
@media (min-width: 576px) {
    .m-sm-0 {
        margin: 0 !important;
    }
    .mt-sm-0,
    .my-sm-0 {
        margin-top: 0 !important;
    }
    .mr-sm-0,
    .mx-sm-0 {
        margin-right: 0 !important;
    }
    .mb-sm-0,
    .my-sm-0 {
        margin-bottom: 0 !important;
    }
    .ml-sm-0,
    .mx-sm-0 {
        margin-left: 0 !important;
    }
    .m-sm-1 {
        margin: 0.25rem !important;
    }
    .mt-sm-1,
    .my-sm-1 {
        margin-top: 0.25rem !important;
    }
    .mr-sm-1,
    .mx-sm-1 {
        margin-right: 0.25rem !important;
    }
    .mb-sm-1,
    .my-sm-1 {
        margin-bottom: 0.25rem !important;
    }
    .ml-sm-1,
    .mx-sm-1 {
        margin-left: 0.25rem !important;
    }
    .m-sm-2 {
        margin: 0.5rem !important;
    }
    .mt-sm-2,
    .my-sm-2 {
        margin-top: 0.5rem !important;
    }
    .mr-sm-2,
    .mx-sm-2 {
        margin-right: 0.5rem !important;
    }
    .mb-sm-2,
    .my-sm-2 {
        margin-bottom: 0.5rem !important;
    }
    .ml-sm-2,
    .mx-sm-2 {
        margin-left: 0.5rem !important;
    }
    .m-sm-3 {
        margin: 1rem !important;
    }
    .mt-sm-3,
    .my-sm-3 {
        margin-top: 1rem !important;
    }
    .mr-sm-3,
    .mx-sm-3 {
        margin-right: 1rem !important;
    }
    .mb-sm-3,
    .my-sm-3 {
        margin-bottom: 1rem !important;
    }
    .ml-sm-3,
    .mx-sm-3 {
        margin-left: 1rem !important;
    }
    .m-sm-4 {
        margin: 1.5rem !important;
    }
    .mt-sm-4,
    .my-sm-4 {
        margin-top: 1.5rem !important;
    }
    .mr-sm-4,
    .mx-sm-4 {
        margin-right: 1.5rem !important;
    }
    .mb-sm-4,
    .my-sm-4 {
        margin-bottom: 1.5rem !important;
    }
    .ml-sm-4,
    .mx-sm-4 {
        margin-left: 1.5rem !important;
    }
    .m-sm-5 {
        margin: 3rem !important;
    }
    .mt-sm-5,
    .my-sm-5 {
        margin-top: 3rem !important;
    }
    .mr-sm-5,
    .mx-sm-5 {
        margin-right: 3rem !important;
    }
    .mb-sm-5,
    .my-sm-5 {
        margin-bottom: 3rem !important;
    }
    .ml-sm-5,
    .mx-sm-5 {
        margin-left: 3rem !important;
    }
    .p-sm-0 {
        padding: 0 !important;
    }
    .pt-sm-0,
    .py-sm-0 {
        padding-top: 0 !important;
    }
    .pr-sm-0,
    .px-sm-0 {
        padding-right: 0 !important;
    }
    .pb-sm-0,
    .py-sm-0 {
        padding-bottom: 0 !important;
    }
    .pl-sm-0,
    .px-sm-0 {
        padding-left: 0 !important;
    }
    .p-sm-1 {
        padding: 0.25rem !important;
    }
    .pt-sm-1,
    .py-sm-1 {
        padding-top: 0.25rem !important;
    }
    .pr-sm-1,
    .px-sm-1 {
        padding-right: 0.25rem !important;
    }
    .pb-sm-1,
    .py-sm-1 {
        padding-bottom: 0.25rem !important;
    }
    .pl-sm-1,
    .px-sm-1 {
        padding-left: 0.25rem !important;
    }
    .p-sm-2 {
        padding: 0.5rem !important;
    }
    .pt-sm-2,
    .py-sm-2 {
        padding-top: 0.5rem !important;
    }
    .pr-sm-2,
    .px-sm-2 {
        padding-right: 0.5rem !important;
    }
    .pb-sm-2,
    .py-sm-2 {
        padding-bottom: 0.5rem !important;
    }
    .pl-sm-2,
    .px-sm-2 {
        padding-left: 0.5rem !important;
    }
    .p-sm-3 {
        padding: 1rem !important;
    }
    .pt-sm-3,
    .py-sm-3 {
        padding-top: 1rem !important;
    }
    .pr-sm-3,
    .px-sm-3 {
        padding-right: 1rem !important;
    }
    .pb-sm-3,
    .py-sm-3 {
        padding-bottom: 1rem !important;
    }
    .pl-sm-3,
    .px-sm-3 {
        padding-left: 1rem !important;
    }
    .p-sm-4 {
        padding: 1.5rem !important;
    }
    .pt-sm-4,
    .py-sm-4 {
        padding-top: 1.5rem !important;
    }
    .pr-sm-4,
    .px-sm-4 {
        padding-right: 1.5rem !important;
    }
    .pb-sm-4,
    .py-sm-4 {
        padding-bottom: 1.5rem !important;
    }
    .pl-sm-4,
    .px-sm-4 {
        padding-left: 1.5rem !important;
    }
    .p-sm-5 {
        padding: 3rem !important;
    }
    .pt-sm-5,
    .py-sm-5 {
        padding-top: 3rem !important;
    }
    .pr-sm-5,
    .px-sm-5 {
        padding-right: 3rem !important;
    }
    .pb-sm-5,
    .py-sm-5 {
        padding-bottom: 3rem !important;
    }
    .pl-sm-5,
    .px-sm-5 {
        padding-left: 3rem !important;
    }
    .m-sm-n1 {
        margin: -0.25rem !important;
    }
    .mt-sm-n1,
    .my-sm-n1 {
        margin-top: -0.25rem !important;
    }
    .mr-sm-n1,
    .mx-sm-n1 {
        margin-right: -0.25rem !important;
    }
    .mb-sm-n1,
    .my-sm-n1 {
        margin-bottom: -0.25rem !important;
    }
    .ml-sm-n1,
    .mx-sm-n1 {
        margin-left: -0.25rem !important;
    }
    .m-sm-n2 {
        margin: -0.5rem !important;
    }
    .mt-sm-n2,
    .my-sm-n2 {
        margin-top: -0.5rem !important;
    }
    .mr-sm-n2,
    .mx-sm-n2 {
        margin-right: -0.5rem !important;
    }
    .mb-sm-n2,
    .my-sm-n2 {
        margin-bottom: -0.5rem !important;
    }
    .ml-sm-n2,
    .mx-sm-n2 {
        margin-left: -0.5rem !important;
    }
    .m-sm-n3 {
        margin: -1rem !important;
    }
    .mt-sm-n3,
    .my-sm-n3 {
        margin-top: -1rem !important;
    }
    .mr-sm-n3,
    .mx-sm-n3 {
        margin-right: -1rem !important;
    }
    .mb-sm-n3,
    .my-sm-n3 {
        margin-bottom: -1rem !important;
    }
    .ml-sm-n3,
    .mx-sm-n3 {
        margin-left: -1rem !important;
    }
    .m-sm-n4 {
        margin: -1.5rem !important;
    }
    .mt-sm-n4,
    .my-sm-n4 {
        margin-top: -1.5rem !important;
    }
    .mr-sm-n4,
    .mx-sm-n4 {
        margin-right: -1.5rem !important;
    }
    .mb-sm-n4,
    .my-sm-n4 {
        margin-bottom: -1.5rem !important;
    }
    .ml-sm-n4,
    .mx-sm-n4 {
        margin-left: -1.5rem !important;
    }
    .m-sm-n5 {
        margin: -3rem !important;
    }
    .mt-sm-n5,
    .my-sm-n5 {
        margin-top: -3rem !important;
    }
    .mr-sm-n5,
    .mx-sm-n5 {
        margin-right: -3rem !important;
    }
    .mb-sm-n5,
    .my-sm-n5 {
        margin-bottom: -3rem !important;
    }
    .ml-sm-n5,
    .mx-sm-n5 {
        margin-left: -3rem !important;
    }
    .m-sm-auto {
        margin: auto !important;
    }
    .mt-sm-auto,
    .my-sm-auto {
        margin-top: auto !important;
    }
    .mr-sm-auto,
    .mx-sm-auto {
        margin-right: auto !important;
    }
    .mb-sm-auto,
    .my-sm-auto {
        margin-bottom: auto !important;
    }
    .ml-sm-auto,
    .mx-sm-auto {
        margin-left: auto !important;
    }
}
@media (min-width: 768px) {
    .m-md-0 {
        margin: 0 !important;
    }
    .mt-md-0,
    .my-md-0 {
        margin-top: 0 !important;
    }
    .mr-md-0,
    .mx-md-0 {
        margin-right: 0 !important;
    }
    .mb-md-0,
    .my-md-0 {
        margin-bottom: 0 !important;
    }
    .ml-md-0,
    .mx-md-0 {
        margin-left: 0 !important;
    }
    .m-md-1 {
        margin: 0.25rem !important;
    }
    .mt-md-1,
    .my-md-1 {
        margin-top: 0.25rem !important;
    }
    .mr-md-1,
    .mx-md-1 {
        margin-right: 0.25rem !important;
    }
    .mb-md-1,
    .my-md-1 {
        margin-bottom: 0.25rem !important;
    }
    .ml-md-1,
    .mx-md-1 {
        margin-left: 0.25rem !important;
    }
    .m-md-2 {
        margin: 0.5rem !important;
    }
    .mt-md-2,
    .my-md-2 {
        margin-top: 0.5rem !important;
    }
    .mr-md-2,
    .mx-md-2 {
        margin-right: 0.5rem !important;
    }
    .mb-md-2,
    .my-md-2 {
        margin-bottom: 0.5rem !important;
    }
    .ml-md-2,
    .mx-md-2 {
        margin-left: 0.5rem !important;
    }
    .m-md-3 {
        margin: 1rem !important;
    }
    .mt-md-3,
    .my-md-3 {
        margin-top: 1rem !important;
    }
    .mr-md-3,
    .mx-md-3 {
        margin-right: 1rem !important;
    }
    .mb-md-3,
    .my-md-3 {
        margin-bottom: 1rem !important;
    }
    .ml-md-3,
    .mx-md-3 {
        margin-left: 1rem !important;
    }
    .m-md-4 {
        margin: 1.5rem !important;
    }
    .mt-md-4,
    .my-md-4 {
        margin-top: 1.5rem !important;
    }
    .mr-md-4,
    .mx-md-4 {
        margin-right: 1.5rem !important;
    }
    .mb-md-4,
    .my-md-4 {
        margin-bottom: 1.5rem !important;
    }
    .ml-md-4,
    .mx-md-4 {
        margin-left: 1.5rem !important;
    }
    .m-md-5 {
        margin: 3rem !important;
    }
    .mt-md-5,
    .my-md-5 {
        margin-top: 3rem !important;
    }
    .mr-md-5,
    .mx-md-5 {
        margin-right: 3rem !important;
    }
    .mb-md-5,
    .my-md-5 {
        margin-bottom: 3rem !important;
    }
    .ml-md-5,
    .mx-md-5 {
        margin-left: 3rem !important;
    }
    .p-md-0 {
        padding: 0 !important;
    }
    .pt-md-0,
    .py-md-0 {
        padding-top: 0 !important;
    }
    .pr-md-0,
    .px-md-0 {
        padding-right: 0 !important;
    }
    .pb-md-0,
    .py-md-0 {
        padding-bottom: 0 !important;
    }
    .pl-md-0,
    .px-md-0 {
        padding-left: 0 !important;
    }
    .p-md-1 {
        padding: 0.25rem !important;
    }
    .pt-md-1,
    .py-md-1 {
        padding-top: 0.25rem !important;
    }
    .pr-md-1,
    .px-md-1 {
        padding-right: 0.25rem !important;
    }
    .pb-md-1,
    .py-md-1 {
        padding-bottom: 0.25rem !important;
    }
    .pl-md-1,
    .px-md-1 {
        padding-left: 0.25rem !important;
    }
    .p-md-2 {
        padding: 0.5rem !important;
    }
    .pt-md-2,
    .py-md-2 {
        padding-top: 0.5rem !important;
    }
    .pr-md-2,
    .px-md-2 {
        padding-right: 0.5rem !important;
    }
    .pb-md-2,
    .py-md-2 {
        padding-bottom: 0.5rem !important;
    }
    .pl-md-2,
    .px-md-2 {
        padding-left: 0.5rem !important;
    }
    .p-md-3 {
        padding: 1rem !important;
    }
    .pt-md-3,
    .py-md-3 {
        padding-top: 1rem !important;
    }
    .pr-md-3,
    .px-md-3 {
        padding-right: 1rem !important;
    }
    .pb-md-3,
    .py-md-3 {
        padding-bottom: 1rem !important;
    }
    .pl-md-3,
    .px-md-3 {
        padding-left: 1rem !important;
    }
    .p-md-4 {
        padding: 1.5rem !important;
    }
    .pt-md-4,
    .py-md-4 {
        padding-top: 1.5rem !important;
    }
    .pr-md-4,
    .px-md-4 {
        padding-right: 1.5rem !important;
    }
    .pb-md-4,
    .py-md-4 {
        padding-bottom: 1.5rem !important;
    }
    .pl-md-4,
    .px-md-4 {
        padding-left: 1.5rem !important;
    }
    .p-md-5 {
        padding: 3rem !important;
    }
    .pt-md-5,
    .py-md-5 {
        padding-top: 3rem !important;
    }
    .pr-md-5,
    .px-md-5 {
        padding-right: 3rem !important;
    }
    .pb-md-5,
    .py-md-5 {
        padding-bottom: 3rem !important;
    }
    .pl-md-5,
    .px-md-5 {
        padding-left: 3rem !important;
    }
    .m-md-n1 {
        margin: -0.25rem !important;
    }
    .mt-md-n1,
    .my-md-n1 {
        margin-top: -0.25rem !important;
    }
    .mr-md-n1,
    .mx-md-n1 {
        margin-right: -0.25rem !important;
    }
    .mb-md-n1,
    .my-md-n1 {
        margin-bottom: -0.25rem !important;
    }
    .ml-md-n1,
    .mx-md-n1 {
        margin-left: -0.25rem !important;
    }
    .m-md-n2 {
        margin: -0.5rem !important;
    }
    .mt-md-n2,
    .my-md-n2 {
        margin-top: -0.5rem !important;
    }
    .mr-md-n2,
    .mx-md-n2 {
        margin-right: -0.5rem !important;
    }
    .mb-md-n2,
    .my-md-n2 {
        margin-bottom: -0.5rem !important;
    }
    .ml-md-n2,
    .mx-md-n2 {
        margin-left: -0.5rem !important;
    }
    .m-md-n3 {
        margin: -1rem !important;
    }
    .mt-md-n3,
    .my-md-n3 {
        margin-top: -1rem !important;
    }
    .mr-md-n3,
    .mx-md-n3 {
        margin-right: -1rem !important;
    }
    .mb-md-n3,
    .my-md-n3 {
        margin-bottom: -1rem !important;
    }
    .ml-md-n3,
    .mx-md-n3 {
        margin-left: -1rem !important;
    }
    .m-md-n4 {
        margin: -1.5rem !important;
    }
    .mt-md-n4,
    .my-md-n4 {
        margin-top: -1.5rem !important;
    }
    .mr-md-n4,
    .mx-md-n4 {
        margin-right: -1.5rem !important;
    }
    .mb-md-n4,
    .my-md-n4 {
        margin-bottom: -1.5rem !important;
    }
    .ml-md-n4,
    .mx-md-n4 {
        margin-left: -1.5rem !important;
    }
    .m-md-n5 {
        margin: -3rem !important;
    }
    .mt-md-n5,
    .my-md-n5 {
        margin-top: -3rem !important;
    }
    .mr-md-n5,
    .mx-md-n5 {
        margin-right: -3rem !important;
    }
    .mb-md-n5,
    .my-md-n5 {
        margin-bottom: -3rem !important;
    }
    .ml-md-n5,
    .mx-md-n5 {
        margin-left: -3rem !important;
    }
    .m-md-auto {
        margin: auto !important;
    }
    .mt-md-auto,
    .my-md-auto {
        margin-top: auto !important;
    }
    .mr-md-auto,
    .mx-md-auto {
        margin-right: auto !important;
    }
    .mb-md-auto,
    .my-md-auto {
        margin-bottom: auto !important;
    }
    .ml-md-auto,
    .mx-md-auto {
        margin-left: auto !important;
    }
}
@media (min-width: 992px) {
    .flex-lg-row {
        flex-direction: row !important;
    }
    .flex-lg-column {
        flex-direction: column !important;
    }
    .flex-lg-row-reverse {
        flex-direction: row-reverse !important;
    }
    .flex-lg-column-reverse {
        flex-direction: column-reverse !important;
    }
    .flex-lg-wrap {
        flex-wrap: wrap !important;
    }
    .flex-lg-nowrap {
        flex-wrap: nowrap !important;
    }
    .flex-lg-wrap-reverse {
        flex-wrap: wrap-reverse !important;
    }
    .flex-lg-fill {
        flex: 1 1 auto !important;
    }
    .flex-lg-grow-0 {
        flex-grow: 0 !important;
    }
    .flex-lg-grow-1 {
        flex-grow: 1 !important;
    }
    .flex-lg-shrink-0 {
        flex-shrink: 0 !important;
    }
    .flex-lg-shrink-1 {
        flex-shrink: 1 !important;
    }
    .justify-content-lg-start {
        justify-content: flex-start !important;
    }
    .justify-content-lg-end {
        justify-content: flex-end !important;
    }
    .justify-content-lg-center {
        justify-content: center !important;
    }
    .justify-content-lg-between {
        justify-content: space-between !important;
    }
    .justify-content-lg-around {
        justify-content: space-around !important;
    }
    .align-items-lg-start {
        align-items: flex-start !important;
    }
    .align-items-lg-end {
        align-items: flex-end !important;
    }
    .align-items-lg-center {
        align-items: center !important;
    }
    .align-items-lg-baseline {
        align-items: baseline !important;
    }
    .align-items-lg-stretch {
        align-items: stretch !important;
    }
    .align-content-lg-start {
        align-content: flex-start !important;
    }
    .align-content-lg-end {
        align-content: flex-end !important;
    }
    .align-content-lg-center {
        align-content: center !important;
    }
    .align-content-lg-between {
        align-content: space-between !important;
    }
    .align-content-lg-around {
        align-content: space-around !important;
    }
    .align-content-lg-stretch {
        align-content: stretch !important;
    }
    .align-self-lg-auto {
        align-self: auto !important;
    }
    .align-self-lg-start {
        align-self: flex-start !important;
    }
    .align-self-lg-end {
        align-self: flex-end !important;
    }
    .align-self-lg-center {
        align-self: center !important;
    }
    .align-self-lg-baseline {
        align-self: baseline !important;
    }
    .align-self-lg-stretch {
        align-self: stretch !important;
    }
    .float-lg-left {
        float: left !important;
    }
    .float-lg-right {
        float: right !important;
    }
    .float-lg-none {
        float: none !important;
    }
    .m-lg-0 {
        margin: 0 !important;
    }
    .mt-lg-0,
    .my-lg-0 {
        margin-top: 0 !important;
    }
    .mr-lg-0,
    .mx-lg-0 {
        margin-right: 0 !important;
    }
    .mb-lg-0,
    .my-lg-0 {
        margin-bottom: 0 !important;
    }
    .ml-lg-0,
    .mx-lg-0 {
        margin-left: 0 !important;
    }
    .m-lg-1 {
        margin: 0.25rem !important;
    }
    .mt-lg-1,
    .my-lg-1 {
        margin-top: 0.25rem !important;
    }
    .mr-lg-1,
    .mx-lg-1 {
        margin-right: 0.25rem !important;
    }
    .mb-lg-1,
    .my-lg-1 {
        margin-bottom: 0.25rem !important;
    }
    .ml-lg-1,
    .mx-lg-1 {
        margin-left: 0.25rem !important;
    }
    .m-lg-2 {
        margin: 0.5rem !important;
    }
    .mt-lg-2,
    .my-lg-2 {
        margin-top: 0.5rem !important;
    }
    .mr-lg-2,
    .mx-lg-2 {
        margin-right: 0.5rem !important;
    }
    .mb-lg-2,
    .my-lg-2 {
        margin-bottom: 0.5rem !important;
    }
    .ml-lg-2,
    .mx-lg-2 {
        margin-left: 0.5rem !important;
    }
    .m-lg-3 {
        margin: 1rem !important;
    }
    .mt-lg-3,
    .my-lg-3 {
        margin-top: 1rem !important;
    }
    .mr-lg-3,
    .mx-lg-3 {
        margin-right: 1rem !important;
    }
    .mb-lg-3,
    .my-lg-3 {
        margin-bottom: 1rem !important;
    }
    .ml-lg-3,
    .mx-lg-3 {
        margin-left: 1rem !important;
    }
    .m-lg-4 {
        margin: 1.5rem !important;
    }
    .mt-lg-4,
    .my-lg-4 {
        margin-top: 1.5rem !important;
    }
    .mr-lg-4,
    .mx-lg-4 {
        margin-right: 1.5rem !important;
    }
    .mb-lg-4,
    .my-lg-4 {
        margin-bottom: 1.5rem !important;
    }
    .ml-lg-4,
    .mx-lg-4 {
        margin-left: 1.5rem !important;
    }
    .m-lg-5 {
        margin: 3rem !important;
    }
    .mt-lg-5,
    .my-lg-5 {
        margin-top: 3rem !important;
    }
    .mr-lg-5,
    .mx-lg-5 {
        margin-right: 3rem !important;
    }
    .mb-lg-5,
    .my-lg-5 {
        margin-bottom: 3rem !important;
    }
    .ml-lg-5,
    .mx-lg-5 {
        margin-left: 3rem !important;
    }
    .p-lg-0 {
        padding: 0 !important;
    }
    .pt-lg-0,
    .py-lg-0 {
        padding-top: 0 !important;
    }
    .pr-lg-0,
    .px-lg-0 {
        padding-right: 0 !important;
    }
    .pb-lg-0,
    .py-lg-0 {
        padding-bottom: 0 !important;
    }
    .pl-lg-0,
    .px-lg-0 {
        padding-left: 0 !important;
    }
    .p-lg-1 {
        padding: 0.25rem !important;
    }
    .pt-lg-1,
    .py-lg-1 {
        padding-top: 0.25rem !important;
    }
    .pr-lg-1,
    .px-lg-1 {
        padding-right: 0.25rem !important;
    }
    .pb-lg-1,
    .py-lg-1 {
        padding-bottom: 0.25rem !important;
    }
    .pl-lg-1,
    .px-lg-1 {
        padding-left: 0.25rem !important;
    }
    .p-lg-2 {
        padding: 0.5rem !important;
    }
    .pt-lg-2,
    .py-lg-2 {
        padding-top: 0.5rem !important;
    }
    .pr-lg-2,
    .px-lg-2 {
        padding-right: 0.5rem !important;
    }
    .pb-lg-2,
    .py-lg-2 {
        padding-bottom: 0.5rem !important;
    }
    .pl-lg-2,
    .px-lg-2 {
        padding-left: 0.5rem !important;
    }
    .p-lg-3 {
        padding: 1rem !important;
    }
    .pt-lg-3,
    .py-lg-3 {
        padding-top: 1rem !important;
    }
    .pr-lg-3,
    .px-lg-3 {
        padding-right: 1rem !important;
    }
    .pb-lg-3,
    .py-lg-3 {
        padding-bottom: 1rem !important;
    }
    .pl-lg-3,
    .px-lg-3 {
        padding-left: 1rem !important;
    }
    .p-lg-4 {
        padding: 1.5rem !important;
    }
    .pt-lg-4,
    .py-lg-4 {
        padding-top: 1.5rem !important;
    }
    .pr-lg-4,
    .px-lg-4 {
        padding-right: 1.5rem !important;
    }
    .pb-lg-4,
    .py-lg-4 {
        padding-bottom: 1.5rem !important;
    }
    .pl-lg-4,
    .px-lg-4 {
        padding-left: 1.5rem !important;
    }
    .p-lg-5 {
        padding: 3rem !important;
    }
    .pt-lg-5,
    .py-lg-5 {
        padding-top: 3rem !important;
    }
    .pr-lg-5,
    .px-lg-5 {
        padding-right: 3rem !important;
    }
    .pb-lg-5,
    .py-lg-5 {
        padding-bottom: 3rem !important;
    }
    .pl-lg-5,
    .px-lg-5 {
        padding-left: 3rem !important;
    }
    .m-lg-n1 {
        margin: -0.25rem !important;
    }
    .mt-lg-n1,
    .my-lg-n1 {
        margin-top: -0.25rem !important;
    }
    .mr-lg-n1,
    .mx-lg-n1 {
        margin-right: -0.25rem !important;
    }
    .mb-lg-n1,
    .my-lg-n1 {
        margin-bottom: -0.25rem !important;
    }
    .ml-lg-n1,
    .mx-lg-n1 {
        margin-left: -0.25rem !important;
    }
    .m-lg-n2 {
        margin: -0.5rem !important;
    }
    .mt-lg-n2,
    .my-lg-n2 {
        margin-top: -0.5rem !important;
    }
    .mr-lg-n2,
    .mx-lg-n2 {
        margin-right: -0.5rem !important;
    }
    .mb-lg-n2,
    .my-lg-n2 {
        margin-bottom: -0.5rem !important;
    }
    .ml-lg-n2,
    .mx-lg-n2 {
        margin-left: -0.5rem !important;
    }
    .m-lg-n3 {
        margin: -1rem !important;
    }
    .mt-lg-n3,
    .my-lg-n3 {
        margin-top: -1rem !important;
    }
    .mr-lg-n3,
    .mx-lg-n3 {
        margin-right: -1rem !important;
    }
    .mb-lg-n3,
    .my-lg-n3 {
        margin-bottom: -1rem !important;
    }
    .ml-lg-n3,
    .mx-lg-n3 {
        margin-left: -1rem !important;
    }
    .m-lg-n4 {
        margin: -1.5rem !important;
    }
    .mt-lg-n4,
    .my-lg-n4 {
        margin-top: -1.5rem !important;
    }
    .mr-lg-n4,
    .mx-lg-n4 {
        margin-right: -1.5rem !important;
    }
    .mb-lg-n4,
    .my-lg-n4 {
        margin-bottom: -1.5rem !important;
    }
    .ml-lg-n4,
    .mx-lg-n4 {
        margin-left: -1.5rem !important;
    }
    .m-lg-n5 {
        margin: -3rem !important;
    }
    .mt-lg-n5,
    .my-lg-n5 {
        margin-top: -3rem !important;
    }
    .mr-lg-n5,
    .mx-lg-n5 {
        margin-right: -3rem !important;
    }
    .mb-lg-n5,
    .my-lg-n5 {
        margin-bottom: -3rem !important;
    }
    .ml-lg-n5,
    .mx-lg-n5 {
        margin-left: -3rem !important;
    }
    .m-lg-auto {
        margin: auto !important;
    }
    .mt-lg-auto,
    .my-lg-auto {
        margin-top: auto !important;
    }
    .mr-lg-auto,
    .mx-lg-auto {
        margin-right: auto !important;
    }
    .mb-lg-auto,
    .my-lg-auto {
        margin-bottom: auto !important;
    }
    .ml-lg-auto,
    .mx-lg-auto {
        margin-left: auto !important;
    }
}
@media (min-width: 1600px) {
    .flex-xl-row {
        flex-direction: row !important;
    }
    .flex-xl-column {
        flex-direction: column !important;
    }
    .flex-xl-row-reverse {
        flex-direction: row-reverse !important;
    }
    .flex-xl-column-reverse {
        flex-direction: column-reverse !important;
    }
    .flex-xl-wrap {
        flex-wrap: wrap !important;
    }
    .flex-xl-nowrap {
        flex-wrap: nowrap !important;
    }
    .flex-xl-wrap-reverse {
        flex-wrap: wrap-reverse !important;
    }
    .flex-xl-fill {
        flex: 1 1 auto !important;
    }
    .flex-xl-grow-0 {
        flex-grow: 0 !important;
    }
    .flex-xl-grow-1 {
        flex-grow: 1 !important;
    }
    .flex-xl-shrink-0 {
        flex-shrink: 0 !important;
    }
    .flex-xl-shrink-1 {
        flex-shrink: 1 !important;
    }
    .justify-content-xl-start {
        justify-content: flex-start !important;
    }
    .justify-content-xl-end {
        justify-content: flex-end !important;
    }
    .justify-content-xl-center {
        justify-content: center !important;
    }
    .justify-content-xl-between {
        justify-content: space-between !important;
    }
    .justify-content-xl-around {
        justify-content: space-around !important;
    }
    .align-items-xl-start {
        align-items: flex-start !important;
    }
    .align-items-xl-end {
        align-items: flex-end !important;
    }
    .align-items-xl-center {
        align-items: center !important;
    }
    .align-items-xl-baseline {
        align-items: baseline !important;
    }
    .align-items-xl-stretch {
        align-items: stretch !important;
    }
    .align-content-xl-start {
        align-content: flex-start !important;
    }
    .align-content-xl-end {
        align-content: flex-end !important;
    }
    .align-content-xl-center {
        align-content: center !important;
    }
    .align-content-xl-between {
        align-content: space-between !important;
    }
    .align-content-xl-around {
        align-content: space-around !important;
    }
    .align-content-xl-stretch {
        align-content: stretch !important;
    }
    .align-self-xl-auto {
        align-self: auto !important;
    }
    .align-self-xl-start {
        align-self: flex-start !important;
    }
    .align-self-xl-end {
        align-self: flex-end !important;
    }
    .align-self-xl-center {
        align-self: center !important;
    }
    .align-self-xl-baseline {
        align-self: baseline !important;
    }
    .align-self-xl-stretch {
        align-self: stretch !important;
    }
    .float-xl-left {
        float: left !important;
    }
    .float-xl-right {
        float: right !important;
    }
    .float-xl-none {
        float: none !important;
    }
    .m-xl-0 {
        margin: 0 !important;
    }
    .mt-xl-0,
    .my-xl-0 {
        margin-top: 0 !important;
    }
    .mr-xl-0,
    .mx-xl-0 {
        margin-right: 0 !important;
    }
    .mb-xl-0,
    .my-xl-0 {
        margin-bottom: 0 !important;
    }
    .ml-xl-0,
    .mx-xl-0 {
        margin-left: 0 !important;
    }
    .m-xl-1 {
        margin: 0.25rem !important;
    }
    .mt-xl-1,
    .my-xl-1 {
        margin-top: 0.25rem !important;
    }
    .mr-xl-1,
    .mx-xl-1 {
        margin-right: 0.25rem !important;
    }
    .mb-xl-1,
    .my-xl-1 {
        margin-bottom: 0.25rem !important;
    }
    .ml-xl-1,
    .mx-xl-1 {
        margin-left: 0.25rem !important;
    }
    .m-xl-2 {
        margin: 0.5rem !important;
    }
    .mt-xl-2,
    .my-xl-2 {
        margin-top: 0.5rem !important;
    }
    .mr-xl-2,
    .mx-xl-2 {
        margin-right: 0.5rem !important;
    }
    .mb-xl-2,
    .my-xl-2 {
        margin-bottom: 0.5rem !important;
    }
    .ml-xl-2,
    .mx-xl-2 {
        margin-left: 0.5rem !important;
    }
    .m-xl-3 {
        margin: 1rem !important;
    }
    .mt-xl-3,
    .my-xl-3 {
        margin-top: 1rem !important;
    }
    .mr-xl-3,
    .mx-xl-3 {
        margin-right: 1rem !important;
    }
    .mb-xl-3,
    .my-xl-3 {
        margin-bottom: 1rem !important;
    }
    .ml-xl-3,
    .mx-xl-3 {
        margin-left: 1rem !important;
    }
    .m-xl-4 {
        margin: 1.5rem !important;
    }
    .mt-xl-4,
    .my-xl-4 {
        margin-top: 1.5rem !important;
    }
    .mr-xl-4,
    .mx-xl-4 {
        margin-right: 1.5rem !important;
    }
    .mb-xl-4,
    .my-xl-4 {
        margin-bottom: 1.5rem !important;
    }
    .ml-xl-4,
    .mx-xl-4 {
        margin-left: 1.5rem !important;
    }
    .m-xl-5 {
        margin: 3rem !important;
    }
    .mt-xl-5,
    .my-xl-5 {
        margin-top: 3rem !important;
    }
    .mr-xl-5,
    .mx-xl-5 {
        margin-right: 3rem !important;
    }
    .mb-xl-5,
    .my-xl-5 {
        margin-bottom: 3rem !important;
    }
    .ml-xl-5,
    .mx-xl-5 {
        margin-left: 3rem !important;
    }
    .p-xl-0 {
        padding: 0 !important;
    }
    .pt-xl-0,
    .py-xl-0 {
        padding-top: 0 !important;
    }
    .pr-xl-0,
    .px-xl-0 {
        padding-right: 0 !important;
    }
    .pb-xl-0,
    .py-xl-0 {
        padding-bottom: 0 !important;
    }
    .pl-xl-0,
    .px-xl-0 {
        padding-left: 0 !important;
    }
    .p-xl-1 {
        padding: 0.25rem !important;
    }
    .pt-xl-1,
    .py-xl-1 {
        padding-top: 0.25rem !important;
    }
    .pr-xl-1,
    .px-xl-1 {
        padding-right: 0.25rem !important;
    }
    .pb-xl-1,
    .py-xl-1 {
        padding-bottom: 0.25rem !important;
    }
    .pl-xl-1,
    .px-xl-1 {
        padding-left: 0.25rem !important;
    }
    .p-xl-2 {
        padding: 0.5rem !important;
    }
    .pt-xl-2,
    .py-xl-2 {
        padding-top: 0.5rem !important;
    }
    .pr-xl-2,
    .px-xl-2 {
        padding-right: 0.5rem !important;
    }
    .pb-xl-2,
    .py-xl-2 {
        padding-bottom: 0.5rem !important;
    }
    .pl-xl-2,
    .px-xl-2 {
        padding-left: 0.5rem !important;
    }
    .p-xl-3 {
        padding: 1rem !important;
    }
    .pt-xl-3,
    .py-xl-3 {
        padding-top: 1rem !important;
    }
    .pr-xl-3,
    .px-xl-3 {
        padding-right: 1rem !important;
    }
    .pb-xl-3,
    .py-xl-3 {
        padding-bottom: 1rem !important;
    }
    .pl-xl-3,
    .px-xl-3 {
        padding-left: 1rem !important;
    }
    .p-xl-4 {
        padding: 1.5rem !important;
    }
    .pt-xl-4,
    .py-xl-4 {
        padding-top: 1.5rem !important;
    }
    .pr-xl-4,
    .px-xl-4 {
        padding-right: 1.5rem !important;
    }
    .pb-xl-4,
    .py-xl-4 {
        padding-bottom: 1.5rem !important;
    }
    .pl-xl-4,
    .px-xl-4 {
        padding-left: 1.5rem !important;
    }
    .p-xl-5 {
        padding: 3rem !important;
    }
    .pt-xl-5,
    .py-xl-5 {
        padding-top: 3rem !important;
    }
    .pr-xl-5,
    .px-xl-5 {
        padding-right: 3rem !important;
    }
    .pb-xl-5,
    .py-xl-5 {
        padding-bottom: 3rem !important;
    }
    .pl-xl-5,
    .px-xl-5 {
        padding-left: 3rem !important;
    }
    .m-xl-n1 {
        margin: -0.25rem !important;
    }
    .mt-xl-n1,
    .my-xl-n1 {
        margin-top: -0.25rem !important;
    }
    .mr-xl-n1,
    .mx-xl-n1 {
        margin-right: -0.25rem !important;
    }
    .mb-xl-n1,
    .my-xl-n1 {
        margin-bottom: -0.25rem !important;
    }
    .ml-xl-n1,
    .mx-xl-n1 {
        margin-left: -0.25rem !important;
    }
    .m-xl-n2 {
        margin: -0.5rem !important;
    }
    .mt-xl-n2,
    .my-xl-n2 {
        margin-top: -0.5rem !important;
    }
    .mr-xl-n2,
    .mx-xl-n2 {
        margin-right: -0.5rem !important;
    }
    .mb-xl-n2,
    .my-xl-n2 {
        margin-bottom: -0.5rem !important;
    }
    .ml-xl-n2,
    .mx-xl-n2 {
        margin-left: -0.5rem !important;
    }
    .m-xl-n3 {
        margin: -1rem !important;
    }
    .mt-xl-n3,
    .my-xl-n3 {
        margin-top: -1rem !important;
    }
    .mr-xl-n3,
    .mx-xl-n3 {
        margin-right: -1rem !important;
    }
    .mb-xl-n3,
    .my-xl-n3 {
        margin-bottom: -1rem !important;
    }
    .ml-xl-n3,
    .mx-xl-n3 {
        margin-left: -1rem !important;
    }
    .m-xl-n4 {
        margin: -1.5rem !important;
    }
    .mt-xl-n4,
    .my-xl-n4 {
        margin-top: -1.5rem !important;
    }
    .mr-xl-n4,
    .mx-xl-n4 {
        margin-right: -1.5rem !important;
    }
    .mb-xl-n4,
    .my-xl-n4 {
        margin-bottom: -1.5rem !important;
    }
    .ml-xl-n4,
    .mx-xl-n4 {
        margin-left: -1.5rem !important;
    }
    .m-xl-n5 {
        margin: -3rem !important;
    }
    .mt-xl-n5,
    .my-xl-n5 {
        margin-top: -3rem !important;
    }
    .mr-xl-n5,
    .mx-xl-n5 {
        margin-right: -3rem !important;
    }
    .mb-xl-n5,
    .my-xl-n5 {
        margin-bottom: -3rem !important;
    }
    .ml-xl-n5,
    .mx-xl-n5 {
        margin-left: -3rem !important;
    }
    .m-xl-auto {
        margin: auto !important;
    }
    .mt-xl-auto,
    .my-xl-auto {
        margin-top: auto !important;
    }
    .mr-xl-auto,
    .mx-xl-auto {
        margin-right: auto !important;
    }
    .mb-xl-auto,
    .my-xl-auto {
        margin-bottom: auto !important;
    }
    .ml-xl-auto,
    .mx-xl-auto {
        margin-left: auto !important;
    }
}
html[dir="ltr"] body .m-t-5,
html[dir="rtl"] body .m-t-5 {
    margin-top: 5px;
}
.text-justify {
    text-align: justify !important;
}
.text-wrap {
    white-space: normal !important;
}
.text-nowrap {
    white-space: nowrap !important;
}
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.text-left {
    text-align: left !important;
}
.text-right {
    text-align: right !important;
}
.text-center {
    text-align: center !important;
}
@media (min-width: 576px) {
    .text-sm-left {
        text-align: left !important;
    }
    .text-sm-right {
        text-align: right !important;
    }
    .text-sm-center {
        text-align: center !important;
    }
}
@media (min-width: 768px) {
    .text-md-left {
        text-align: left !important;
    }
    .text-md-right {
        text-align: right !important;
    }
    .text-md-center {
        text-align: center !important;
    }
}
@media (min-width: 992px) {
    .text-lg-left {
        text-align: left !important;
    }
    .text-lg-right {
        text-align: right !important;
    }
    .text-lg-center {
        text-align: center !important;
    }
}
@media (min-width: 1600px) {
    .text-xl-left {
        text-align: left !important;
    }
    .text-xl-right {
        text-align: right !important;
    }
    .text-xl-center {
        text-align: center !important;
    }
}
.text-lowercase {
    text-transform: lowercase !important;
}
.text-uppercase {
    text-transform: uppercase !important;
}
.text-capitalize {
    text-transform: capitalize !important;
}
.font-weight-light {
    font-weight: 300 !important;
}
.font-weight-lighter {
    font-weight: lighter !important;
}
.font-weight-normal {
    font-weight: 400 !important;
}
.font-weight-bold {
    font-weight: 800 !important;
}
.font-weight-bolder {
    font-weight: bolder !important;
}
.font-italic {
    font-style: italic !important;
}
.text-white {
    color: #fff !important;
}
.text-primary {
    color: #7460ee !important;
}
a.text-primary:focus,
a.text-primary:hover {
    color: #381be7 !important;
}
.text-secondary {
    color: #6c757d !important;
}
a.text-secondary:focus,
a.text-secondary:hover {
    color: #494f54 !important;
}
.text-success {
    color: #36bea6 !important;
}
a.text-success:focus,
a.text-success:hover {
    color: #258272 !important;
}
.text-info {
    color: #2962ff !important;
}
a.text-info:focus,
a.text-info:hover {
    color: #003adc !important;
}
.text-warning {
    color: #ffbc34 !important;
}
a.text-warning:focus,
a.text-warning:hover {
    color: #e79a00 !important;
}
.text-danger {
    color: #f62d51 !important;
}
a.text-danger:focus,
a.text-danger:hover {
    color: #ce092c !important;
}
.text-light {
    color: #f8f9fa !important;
}
a.text-light:focus,
a.text-light:hover {
    color: #cbd3da !important;
}
.text-dark {
    color: #343a40 !important;
}
a.text-dark:focus,
a.text-dark:hover {
    color: #121416 !important;
}
.text-cyan {
    color: #4fc3f7 !important;
}
a.text-cyan:focus,
a.text-cyan:hover {
    color: #0ba8ef !important;
}
.text-orange {
    color: #fb8c00 !important;
}
a.text-orange:focus,
a.text-orange:hover {
    color: #af6100 !important;
}
.text-purple {
    color: #7460ee !important;
}
a.text-purple:focus,
a.text-purple:hover {
    color: #381be7 !important;
}
.text-body {
    color: #3e5569 !important;
}
.text-muted {
    color: #a1aab2 !important;
}
.text-black-50 {
    color: rgba(0, 0, 0, 0.5) !important;
}
.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}
.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0;
}
.text-decoration-none {
    text-decoration: none !important;
}
.text-reset {
    color: inherit !important;
}
.visible {
    visibility: visible !important;
}
.invisible {
    visibility: hidden !important;
}
@media print {
    blockquote,
    img,
    pre,
    tr {
        page-break-inside: avoid;
    }
    *,
    ::after,
    ::before {
        text-shadow: none !important;
        box-shadow: none !important;
    }
    a:not(.btn) {
        text-decoration: underline;
    }
    abbr[title]::after {
        content: " (" attr(title) ")";
    }
    pre {
        white-space: pre-wrap !important;
    }
    blockquote,
    pre {
        border: 1px solid #a1aab2;
    }
    thead {
        display: table-header-group;
    }
    h2,
    h3,
    p {
        orphans: 3;
        widows: 3;
    }
    h2,
    h3 {
        page-break-after: avoid;
    }
    @page {
        size: a3;
    }
    .container,
    body {
        min-width: 992px !important;
    }
    .navbar {
        display: none;
    }
    .badge {
        border: 1px solid #000;
    }
    .table {
        border-collapse: collapse !important;
    }
    .table td,
    .table th {
        background-color: #fff !important;
    }
    .table-bordered td,
    .table-bordered th {
        border: 1px solid #dee2e6 !important;
    }
    .table-dark {
        color: inherit;
    }
    .table-dark tbody + tbody,
    .table-dark td,
    .table-dark th,
    .table-dark thead th {
        border-color: #dee2e6;
    }
    .table .thead-dark th {
        color: inherit;
        border-color: #dee2e6;
    }
}
html[dir="ltr"] body .m-b-5 {
    margin-bottom: 5px;
}
html[dir="ltr"] body .m-r-5 {
    margin-right: 5px;
}
html[dir="ltr"] body .m-l-5 {
    margin-left: 5px;
}
html[dir="ltr"] body .p-t-5 {
    padding-top: 5px;
}
html[dir="ltr"] body .p-b-5 {
    padding-bottom: 5px;
}
html[dir="ltr"] body .p-r-5 {
    padding-right: 5px;
}
html[dir="ltr"] body .p-l-5 {
    padding-left: 5px;
}
html[dir="ltr"] body .p-5 {
    padding: 5px;
}
html[dir="ltr"] body .m-5 {
    margin: 5px;
}
html[dir="ltr"] body .m-t-10 {
    margin-top: 10px;
}
html[dir="ltr"] body .m-b-10 {
    margin-bottom: 10px;
}
html[dir="ltr"] body .m-r-10 {
    margin-right: 10px;
}
html[dir="ltr"] body .m-l-10 {
    margin-left: 10px;
}
html[dir="ltr"] body .p-t-10 {
    padding-top: 10px;
}
html[dir="ltr"] body .p-b-10 {
    padding-bottom: 10px;
}
html[dir="ltr"] body .p-r-10 {
    padding-right: 10px;
}
html[dir="ltr"] body .p-l-10 {
    padding-left: 10px;
}
html[dir="ltr"] body .p-10 {
    padding: 10px;
}
html[dir="ltr"] body .m-10 {
    margin: 10px;
}
html[dir="ltr"] body .m-t-15 {
    margin-top: 15px;
}
html[dir="ltr"] body .m-b-15 {
    margin-bottom: 15px;
}
html[dir="ltr"] body .m-r-15 {
    margin-right: 15px;
}
html[dir="ltr"] body .m-l-15 {
    margin-left: 15px;
}
html[dir="ltr"] body .p-t-15 {
    padding-top: 15px;
}
html[dir="ltr"] body .p-b-15 {
    padding-bottom: 15px;
}
html[dir="ltr"] body .p-r-15 {
    padding-right: 15px;
}
html[dir="ltr"] body .p-l-15 {
    padding-left: 15px;
}
html[dir="ltr"] body .p-15 {
    padding: 15px;
}
html[dir="ltr"] body .m-15 {
    margin: 15px;
}
html[dir="ltr"] body .m-t-20 {
    margin-top: 20px;
}
html[dir="ltr"] body .m-b-20 {
    margin-bottom: 20px;
}
html[dir="ltr"] body .m-r-20 {
    margin-right: 20px;
}
html[dir="ltr"] body .m-l-20 {
    margin-left: 20px;
}
html[dir="ltr"] body .p-t-20 {
    padding-top: 20px;
}
html[dir="ltr"] body .p-b-20 {
    padding-bottom: 20px;
}
html[dir="ltr"] body .p-r-20 {
    padding-right: 20px;
}
html[dir="ltr"] body .p-l-20 {
    padding-left: 20px;
}
html[dir="ltr"] body .p-20 {
    padding: 20px;
}
html[dir="ltr"] body .m-20 {
    margin: 20px;
}
html[dir="ltr"] body .m-t-25 {
    margin-top: 25px;
}
html[dir="ltr"] body .m-b-25 {
    margin-bottom: 25px;
}
html[dir="ltr"] body .m-r-25 {
    margin-right: 25px;
}
html[dir="ltr"] body .m-l-25 {
    margin-left: 25px;
}
html[dir="ltr"] body .p-t-25 {
    padding-top: 25px;
}
html[dir="ltr"] body .p-b-25 {
    padding-bottom: 25px;
}
html[dir="ltr"] body .p-r-25 {
    padding-right: 25px;
}
html[dir="ltr"] body .p-l-25 {
    padding-left: 25px;
}
html[dir="ltr"] body .p-25 {
    padding: 25px;
}
html[dir="ltr"] body .m-25 {
    margin: 25px;
}
html[dir="ltr"] body .m-t-30 {
    margin-top: 30px;
}
html[dir="ltr"] body .m-b-30 {
    margin-bottom: 30px;
}
html[dir="ltr"] body .m-r-30 {
    margin-right: 30px;
}
html[dir="ltr"] body .m-l-30 {
    margin-left: 30px;
}
html[dir="ltr"] body .p-t-30 {
    padding-top: 30px;
}
html[dir="ltr"] body .p-b-30 {
    padding-bottom: 30px;
}
html[dir="ltr"] body .p-r-30 {
    padding-right: 30px;
}
html[dir="ltr"] body .p-l-30 {
    padding-left: 30px;
}
html[dir="ltr"] body .p-30 {
    padding: 30px;
}
html[dir="ltr"] body .m-30 {
    margin: 30px;
}
html[dir="ltr"] body .m-t-40 {
    margin-top: 40px;
}
html[dir="ltr"] body .m-b-40 {
    margin-bottom: 40px;
}
html[dir="ltr"] body .m-r-40 {
    margin-right: 40px;
}
html[dir="ltr"] body .m-l-40 {
    margin-left: 40px;
}
html[dir="ltr"] body .p-t-40 {
    padding-top: 40px;
}
html[dir="ltr"] body .p-b-40 {
    padding-bottom: 40px;
}
html[dir="ltr"] body .p-r-40 {
    padding-right: 40px;
}
html[dir="ltr"] body .p-l-40 {
    padding-left: 40px;
}
html[dir="ltr"] body .p-40 {
    padding: 40px;
}
html[dir="ltr"] body .m-40 {
    margin: 40px;
}
html[dir="ltr"] body .m-0-0 {
    margin: 0;
}
html[dir="ltr"] body .m-t-0 {
    margin-top: 0;
}
html[dir="ltr"] body .m-r-0 {
    margin-right: 0;
}
html[dir="ltr"] body .m-b-0 {
    margin-bottom: 0;
}
html[dir="ltr"] body .m-l-0 {
    margin-left: 0;
}
html[dir="ltr"] body .p-0-0 {
    padding: 0;
}
html[dir="ltr"] body .p-t-0 {
    padding-top: 0;
}
html[dir="ltr"] body .p-r-0 {
    padding-right: 0;
}
html[dir="ltr"] body .p-b-0 {
    padding-bottom: 0;
}
html[dir="ltr"] body .p-l-0 {
    padding-left: 0;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin1"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin1"] {
    background: #2962ff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin2"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin2"] {
    background: #fe5419;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin3"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin3"] {
    background: #00b0ff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin4"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin4"] {
    background: #6659f7;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin5"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin5"] {
    background: #414755;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin6"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] {
    background: #fff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] .navbar-brand .dark-logo,
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] .navbar-brand .dark-logo {
    display: inline;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] .navbar-brand .light-logo,
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] .navbar-brand .light-logo {
    display: none;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] .nav-toggler,
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] .topbartoggler,
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] .nav-toggler,
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .top-navbar .navbar-header[data-logobg="skin6"] .topbartoggler {
    color: #3e5569;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .navbar-collapse[data-navbarbg="skin1"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar[data-navbarbg="skin1"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .navbar-collapse[data-navbarbg="skin1"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar[data-navbarbg="skin1"] {
    background: #2962ff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .navbar-collapse[data-navbarbg="skin2"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar[data-navbarbg="skin2"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .navbar-collapse[data-navbarbg="skin2"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar[data-navbarbg="skin2"] {
    background: #fe5419;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .navbar-collapse[data-navbarbg="skin3"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar[data-navbarbg="skin3"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .navbar-collapse[data-navbarbg="skin3"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar[data-navbarbg="skin3"] {
    background: #00b0ff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .navbar-collapse[data-navbarbg="skin4"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar[data-navbarbg="skin4"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .navbar-collapse[data-navbarbg="skin4"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar[data-navbarbg="skin4"] {
    background: #6659f7;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .navbar-collapse[data-navbarbg="skin5"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar[data-navbarbg="skin5"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .navbar-collapse[data-navbarbg="skin5"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar[data-navbarbg="skin5"] {
    background: #414755;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .navbar-collapse[data-navbarbg="skin6"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .topbar[data-navbarbg="skin6"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar .navbar-collapse[data-navbarbg="skin6"],
#marketking_dashboard_wrapper[data-layout="vertical"] .topbar[data-navbarbg="skin6"] {
    background: #fff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin1"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin1"] ul,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin1"],
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin1"] ul {
    background: #2962ff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin1"] .create-btn,
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin1"] ul .create-btn,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin1"] .create-btn,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin1"] ul .create-btn {
    background: #4fc3f7;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin2"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin2"] ul,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin2"],
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin2"] ul {
    background: #fe5419;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin2"] ul::after,
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin2"]::after,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin2"] ul::after,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin2"]::after {
    background: url(../assets/images/background/sidebarbg.png) no-repeat;
    position: absolute;
    top: 0;
    content: "";
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: -1;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin3"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin3"] ul,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin3"],
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin3"] ul {
    background: #00b0ff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin4"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin4"] ul,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin4"],
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin4"] ul {
    background: #6659f7;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin5"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin5"] ul,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin5"],
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin5"] ul {
    background: #414755;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin5"] .create-btn,
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin5"] ul .create-btn,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin5"] .create-btn,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin5"] ul .create-btn {
    background: #4fc3f7;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"],
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav ul,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"],
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav ul {
    background: #fff;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav ul .sidebar-item .sidebar-link,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav ul .sidebar-item .sidebar-link {
    color: #212529;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav ul .sidebar-item .sidebar-link i,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav ul .sidebar-item .sidebar-link i {
    color: #4f5467;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav ul .nav-small-cap,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav ul .nav-small-cap {
    color: #212529;
    opacity: 0.7;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav .has-arrow::after,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav .has-arrow::after {
    border-color: #212529;
}
.card-group .card,
.left-part {
    border-right: 1px solid #e9ecef;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav .user-content,
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav .user-email,
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav .user-name,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav .user-content,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav .user-email,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .sidebar-nav .user-name {
    color: #212529;
}
#marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar[data-sidebarbg="skin6"] .create-btn,
#marketking_dashboard_wrapper[data-layout="vertical"] .left-sidebar[data-sidebarbg="skin6"] .create-btn {
    background: #4fc3f7;
}
#marketking_dashboard_wrapper {
    overflow: hidden;
}
.marketking_dashboard_page_wrapper {
    background: #202224;
    position: relative;
    display: none;
}
.marketking_dashboard_page_wrapper > .container-fluid {
    padding: 20px;
    min-height: calc(100vh - 180px);
}
.footer {
    padding: 15px 20px;
}
.left-part {
    height: 100%;
    width: 260px;
    position: absolute;
}
.left-part.fixed-left-part {
    position: fixed;
    top: 0;
    padding-top: 10px;
}
.left-part .show-left-part {
    position: absolute;
    top: 45%;
    right: -41px;
}
.right-part {
    width: calc(100% - 260px);
    min-height: calc(100vh - 64px);
    overflow: auto;
    margin-left: 260px;
}
.reverse-mode .left-part {
    right: 0;
    border-left: 1px solid #e9ecef;
}
.reverse-mode .show-left-part {
    right: auto;
    left: -41px;
}
.reverse-mode .right-part {
    margin-left: 0;
    margin-right: 260px;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="full"] .marketking_dashboard_page_wrapper {
    margin-left: 250px;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="iconbar"] .marketking_dashboard_page_wrapper {
    margin-left: 180px;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="iconbar"] .left-sidebar,
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="iconbar"] .sidebar-nav ul .sidebar-item,
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="iconbar"] .topbar .top-navbar .navbar-header {
    width: 180px;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="iconbar"] .sidebar-nav ul .sidebar-item .sidebar-link {
    display: block;
    text-align: center;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="iconbar"] .sidebar-nav ul .sidebar-item .sidebar-link i {
    display: block;
    width: auto;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="overlay"] .topbar .top-navbar .navbar-header {
    width: 250px;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="overlay"] .left-sidebar {
    left: -250px;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="overlay"].show-sidebar .left-sidebar {
    left: 0;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebar-position="fixed"] .topbar .top-navbar .navbar-header {
    position: fixed;
    z-index: 10;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-sidebar-position="fixed"] .left-sidebar {
    position: fixed;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-header-position="fixed"] .topbar {
    position: fixed;
    width: 100%;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-header-position="fixed"] .marketking_dashboard_page_wrapper {
    padding-top: 64px;
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-boxed-layout="boxed"] {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    box-shadow: 1px 0 80px rgba(0, 0, 0, 0.2);
}
#marketking_dashboard_wrapper[data-layout="vertical"][data-boxed-layout="boxed"][data-header-position="fixed"] .topbar {
    max-width: 1200px;
}
@media (min-width: 768px) {
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="full"] .topbar .top-navbar .navbar-header {
        width: 250px;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebar-position="fixed"][data-sidebartype="full"] .topbar .top-navbar .navbar-collapse,
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebar-position="fixed"][data-sidebartype="overlay"] .topbar .top-navbar .navbar-collapse {
        margin-left: 250px;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebar-position="fixed"][data-sidebartype="iconbar"] .topbar .top-navbar .navbar-collapse {
        margin-left: 180px;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .topbar .top-navbar .navbar-header {
        width: 65px;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .topbar .top-navbar .navbar-header .logo-text {
        display: none;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .topbar .top-navbar .navbar-header.expand-logo {
        width: 250px;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .topbar .top-navbar .navbar-header.expand-logo .logo-text {
        display: block;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebar-position="fixed"][data-sidebartype="mini-sidebar"] .topbar .top-navbar .navbar-collapse,
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .marketking_dashboard_page_wrapper {
        margin-left: 65px;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .sidebar-nav .has-arrow:after,
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .sidebar-nav .hide-menu {
        display: none;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .sidebar-nav .nav-small-cap {
        justify-content: center;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .left-sidebar {
        width: 65px;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .left-sidebar:hover {
        width: 250px;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .left-sidebar:hover .sidebar-nav .has-arrow:after,
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .left-sidebar:hover .sidebar-nav .hide-menu {
        display: block;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"] .left-sidebar:hover .sidebar-nav .nav-small-cap {
        justify-content: flex-start;
    }
}
@media (max-width: 766px) {
    #marketking_dashboard_wrapper[data-sidebartype="mini-sidebar"] .left-sidebar {
        left: -250px;
    }
    #marketking_dashboard_wrapper.show-sidebar .left-sidebar {
        left: 0;
    }
    #marketking_dashboard_wrapper[data-layout="vertical"][data-header-position="fixed"][data-sidebar-position="fixed"] .topbar .top-navbar .navbar-collapse,
    #marketking_dashboard_wrapper[data-layout="vertical"][data-sidebar-position="fixed"] .topbar .top-navbar .navbar-collapse {
        position: relative;
        top: 64px;
    }
    .left-part {
        left: -260px;
        background: #191821;
        z-index: 1;
        position: fixed;
        transition: 0.1s ease-in;
    }
    .left-part.show-panel {
        left: 0;
    }
    .reverse-mode .left-part {
        right: -260px;
        left: auto;
    }
    .reverse-mode .left-part.show-panel {
        right: 0;
    }
    .right-part {
        width: 100%;
        margin-left: 0;
    }
}
* {
    outline: 0;
}
body {
    margin: 0;
    overflow-x: hidden;
    color: #3e5569;
    background: #fff;
}
html {
    position: relative;
    min-height: 100%;
}
a:focus,
a:hover {
    text-decoration: none;
}
a.link {
    color: #3e5569;
}
a.link:focus,
a.link:hover {
    color: #2962ff;
}
.box {
    border-radius: 2px;
    padding: 10px;
}
.no-wrap td,
.no-wrap th {
    white-space: nowrap;
}
html body blockquote {
    border: 1px solid #e9ecef;
    padding: 15px;
}
.clear {
    clear: both;
}
ol li {
    margin: 5px 0;
}
.thumb-sm {
    height: 32px;
    width: 32px;
}
.thumb-md {
    height: 48px;
    width: 48px;
}
.thumb-lg {
    height: 88px;
    width: 88px;
}
.hide {
    display: none;
}
.radius {
    border-radius: 2px;
}
.round,
.round img {
    border-radius: 100%;
}
.op-5 {
    opacity: 0.5;
}
.op-3 {
    opacity: 0.3;
}
html body .font-bold {
    font-weight: 800;
}
html body .font-normal {
    font-weight: 400;
}
html body .font-light {
    font-weight: 300;
}
html body .font-medium {
    font-weight: 600;
}
html body .font-16 {
    font-size: 16px;
}
html body .font-12 {
    font-size: 12px;
}
html body .font-14 {
    font-size: 14px;
}
html body .font-10 {
    font-size: 10px;
}
html body .font-18 {
    font-size: 18px;
}
html body .font-20 {
    font-size: 20px;
}
html body .font-22 {
    font-size: 22px;
}
html body .font-24 {
    font-size: 24px;
}
html body .display-5 {
    font-size: 3rem;
}
html body .display-6 {
    font-size: 2.5rem;
}
html body .display-7 {
    font-size: 2rem;
}
html body .bg-megna {
    background-color: #4fc3f7;
}
html body .bg-theme {
    background-color: #2962ff;
}
html body .bg-inverse {
    background-color: #212529;
}
html body .bg-purple {
    background-color: #7460ee;
}
html body .bg-light {
    background-color: #e9ecef;
}
html body .bg-white {
    background-color: #fff;
}
.round,
.round.round-info {
    background: #2962ff;
}
.round {
    color: #fff;
    width: 50px;
    height: 50px;
    display: inline-block;
    font-weight: 400;
    text-align: center;
    line-height: 52px;
}
.round.round-warning {
    background: #ffbc34;
}
.round.round-danger {
    background: #f62d51;
}
.round.round-success {
    background: #36bea6;
}
.round.round-primary {
    background: #7460ee;
}
.round-lg {
    line-height: 65px;
    width: 60px;
    height: 60px;
    font-size: 30px;
}
.badge {
    font-weight: 300;
    line-height: normal;
}
.badge.badge-pill {
    padding: 0.2em 0.6em;
}
.badge-xs {
    font-size: 9px;
}
.badge-sm,
.badge-xs {
    -webkit-transform: translate(0, -2px);
    -ms-transform: translate(0, -2px);
    -o-transform: translate(0, -2px);
    transform: translate(0, -2px);
}
ul.list-style-none {
    margin: 0;
    padding: 0;
}
ul.list-style-none li {
    list-style: none;
}
ul.list-style-none li a {
    color: #3e5569;
    padding: 8px 0;
    display: block;
    text-decoration: none;
}
ul.list-style-none li a:hover {
    color: #2962ff;
}
.card {
    margin-bottom: 20px;
}
.card .card-subtitle {
    font-weight: 300;
    margin-bottom: 10px;
    color: #a1aab2;
}
.card .card-title {
    position: relative;
    font-weight: 600;
    margin-bottom: 10px;
}
.card .card-actions,
.card-fullscreen {
    float: right;
}
.card .card-actions a {
    padding: 0 5px;
    cursor: pointer;
}
.card .card-header .card-title {
    margin-bottom: 0;
}
.card-alt {
    margin: 0 -20px;
    background: #e4e9ef;
}
.card-group {
    margin-bottom: 20px;
}
.card-fullscreen {
    left: 0;
    z-index: 9999;
    overflow: auto;
}
.oh {
    overflow: hidden;
}
.card-hover {
    -webkit-transition: all 0.25s ease;
    -o-transition: all 0.25s ease;
    -moz-transition: all 0.25s ease;
    transition: all 0.25s ease;
}
.left-sidebar,
.topbar,
.topbar .top-navbar .navbar-nav > .nav-item {
    transition: 0.2s ease-in;
}
.card-hover:hover {
    webkit-transform: translateY(-4px) scale(1.01);
    -moz-transform: translateY(-4px) scale(1.01);
    -ms-transform: translateY(-4px) scale(1.01);
    -o-transform: translateY(-4px) scale(1.01);
    transform: translateY(-4px) scale(1.01);
    -webkit-box-shadow: 0 14px 24px rgba(62, 57, 107, 0.1);
    box-shadow: 0 14px 24px rgba(62, 57, 107, 0.1);
}
.draggable-cards .card-header {
    cursor: move;
}
.card-moved .card {
    background: #2962ff;
    color: #fff;
}
.label {
    padding: 3px 10px;
    line-height: 13px;
    color: #fff;
    font-weight: 400;
    border-radius: 2px;
    font-size: 75%;
}
.label-rounded {
    border-radius: 60px;
}
.label-custom {
    background-color: #4fc3f7;
}
.label-success {
    background-color: #36bea6;
}
.label-info {
    background-color: #2962ff;
}
.label-warning {
    background-color: #ffbc34;
}
.label-danger {
    background-color: #f62d51;
}
.label-megna {
    background-color: #4fc3f7;
}
.label-primary,
.label-purple {
    background-color: #7460ee;
}
.label-red {
    background-color: #f62d51;
}
.label-inverse {
    background-color: #343a40;
}
.label-default {
    background-color: #f8f9fa;
}
.lds-ripple {
    display: inline-block;
    width: 64px;
    height: 64px;
    position: absolute;
    top: calc(50% - 3.5px);
    left: calc(50% - 3.5px);
}
.lds-ripple .lds-pos {
    position: absolute;
    border: 2px solid #2962ff;
    opacity: 1;
    border-radius: 50%;
    animation: lds-ripple 1s cubic-bezier(0, 0.1, 0.5, 1) infinite;
}
.lds-ripple .lds-pos:nth-child(2) {
    animation-delay: -0.5s;
}
@keyframes lds-ripple {
    0% {
        top: 28px;
        left: 28px;
        width: 0;
        height: 0;
        opacity: 0;
    }
    5% {
        top: 28px;
        left: 28px;
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        top: -1px;
        left: -1px;
        width: 58px;
        height: 58px;
        opacity: 0;
    }
}
.notify {
    position: relative;
    top: -18px;
    right: -11px;
}
.notify .heartbit {
    position: absolute;
    top: -20px;
    right: -4px;
    height: 25px;
    width: 25px;
    z-index: 10;
    border: 5px solid #f62d51;
    border-radius: 70px;
    -moz-animation: heartbit 1s ease-out;
    -moz-animation-iteration-count: infinite;
    -o-animation: heartbit 1s ease-out;
    -o-animation-iteration-count: infinite;
    -webkit-animation: heartbit 1s ease-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}
.notify .point {
    width: 6px;
    height: 6px;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    border-radius: 30px;
    background-color: #f62d51;
    position: absolute;
    right: 6px;
    top: -10px;
}
@-moz-keyframes heartbit {
    0% {
        -moz-transform: scale(0);
        opacity: 0;
    }
    25% {
        -moz-transform: scale(0.1);
        opacity: 0.1;
    }
    50% {
        -moz-transform: scale(0.5);
        opacity: 0.3;
    }
    75% {
        -moz-transform: scale(0.8);
        opacity: 0.5;
    }
    100% {
        -moz-transform: scale(1);
        opacity: 0;
    }
}
@-webkit-keyframes heartbit {
    0% {
        -webkit-transform: scale(0);
        opacity: 0;
    }
    25% {
        -webkit-transform: scale(0.1);
        opacity: 0.1;
    }
    50% {
        -webkit-transform: scale(0.5);
        opacity: 0.3;
    }
    75% {
        -webkit-transform: scale(0.8);
        opacity: 0.5;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 0;
    }
}
.topbar {
    position: relative;
    z-index: 50;
    box-shadow: 1px 0 7px rgba(0, 0, 0, 0.05);
    background: #fff;
    height: 64px;
}
.topbar .navbar-collapse {
    padding: 0 10px 0 0;
}
.topbar .top-navbar {
    min-height: 64px;
    padding: 0;
}
.topbar .top-navbar .dropdown-toggle::after {
    display: none;
}
.topbar .top-navbar .navbar-header {
    line-height: 64px;
}
.topbar .top-navbar .navbar-header .navbar-brand {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0 10px;
}
.topbar .top-navbar .navbar-header .navbar-brand .dark-logo {
    display: none;
}
.topbar .top-navbar .navbar-header .navbar-brand .logo-icon {
    margin-right: 5px;
}
.topbar .top-navbar .navbar-nav > .nav-item > .nav-link {
    padding: 0 15px;
    font-size: 0.875rem;
    line-height: 64px;
    height: 64px;
}
.topbar .top-navbar .navbar-nav > .nav-item:hover {
    background: rgba(0, 0, 0, 0.05);
}
.topbar .mailbox,
.topbar .user-dd {
    min-width: 280px;
}
.topbar .nav-toggler,
.topbar .topbartoggler {
    color: #fff;
    padding: 0 15px;
}
.search-box .app-search {
    z-index: 110;
    width: 100%;
    top: -1px;
    box-shadow: 1px 0 20px rgba(0, 0, 0, 0.08);
    display: none;
    left: 0;
}
.search-box .app-search .form-control {
    padding: 23px 40px 20px 23px;
    transition: 0.2s ease-in;
    height: 65px;
}
.search-box .app-search .form-control:focus {
    border-color: transparent;
}
.search-box .app-search .srh-btn {
    position: absolute;
    top: 23px;
    cursor: pointer;
    right: 20px;
}
.topbar .mega-dropdown {
    position: static;
}
.topbar .mega-dropdown .dropdown-menu {
    padding: 30px;
    width: 100%;
    max-height: 480px;
    overflow: auto;
}
.topbar .dropdown-menu {
    padding-top: 0;
    border: 0;
    box-shadow: 1px 1px 15px rgba(0, 0, 0, 0.1);
}
.customizer,
.left-sidebar {
    box-shadow: 1px 0 20px rgba(0, 0, 0, 0.08);
}
.topbar .dropdown-menu .with-arrow {
    position: absolute;
    height: 10px;
    overflow: hidden;
    width: 40px;
    top: -10px;
}
.topbar .dropdown-menu .with-arrow > span {
    background-color: #fff;
    width: 15px;
    height: 15px;
    top: 3px;
    left: 15px;
    position: absolute;
    content: "";
    -moz-border-radius: 6px 0 0;
    border-radius: 6px 0 0;
}
.topbar .dropdown-menu.dropdown-menu-right .with-arrow {
    right: 0;
}
.topbar .dropdown-menu.dropdown-menu-right .with-arrow > span {
    right: 20px;
    left: auto;
}
@media (max-width: 767px) {
    .topbar .top-navbar .navbar-collapse.collapsing,
    .topbar .top-navbar .navbar-collapse.show {
        display: block;
        width: 100%;
        border-top: 1px solid #e9ecef;
    }
    .topbar .top-navbar .navbar-nav {
        flex-direction: row;
    }
    .topbar .top-navbar .navbar-nav .dropdown {
        position: static;
    }
    .topbar .top-navbar .navbar-nav > .nav-item > .nav-link {
        padding: 0 10px;
    }
    .topbar .top-navbar .navbar-header {
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: space-between;
    }
    .topbar .top-navbar .dropdown-menu {
        position: absolute;
        width: 100%;
    }
    .topbar .top-navbar .dropdown-menu .with-arrow {
        display: none;
    }
}
@media only screen and (max-width: 767px) and (orientation: landscape) {
    .mailbox .message-center {
        height: 110px !important;
    }
    .user-dd .profile-dis {
        height: 110px;
    }
}
.table-box {
    display: table;
    width: 100%;
}
.cell {
    display: table-cell;
}
.table th,
.table thead th {
    font-weight: 500;
}
.nowrap {
    white-space: nowrap;
}
.lite-padding td {
    padding: 5px;
}
.no-th-brd.table th,
.table.no-border tbody td {
    border: 0;
}
.jsgrid-pager-current-page,
.jsgrid-pager-nav-button a,
.jsgrid-pager-page a {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    min-width: 1.5em;
    padding: 0.5em 1em;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    color: #67757c;
    border: 1px solid #ddd;
}
.jsgrid-pager-current-page,
.jsgrid-pager-nav-button a:hover,
.jsgrid-pager-page a:hover {
    background-color: #2962ff;
    color: #fff;
}
.jsgrid-pager-nav-button,
.jsgrid-pager-page {
    padding: 0;
}
.jsgrid-pager-page.jsgrid-pager-current-page {
    padding: 0.5em 1em !important;
}
.page-breadcrumb {
    padding: 20px 20px 0;
}
.page-breadcrumb .page-title {
    margin-bottom: 0;
}
.page-breadcrumb .breadcrumb {
    padding: 0;
    margin: 0;
    background: 0 0;
    font-size: 12px;
}
.page-breadcrumb .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "\e649";
    font-family: themify;
    color: #ced4da;
    font-size: 11px;
}
.left-sidebar {
    position: absolute;
    width: 250px;
    height: 100%;
    top: 0;
    z-index: 10;
    padding-top: 64px;
    background: #fff;
}
.scroll-sidebar,
.sidebar-nav .has-arrow {
    position: relative;
}
.scroll-sidebar {
    height: calc(100% - 20px);
}
.sidebar-nav ul .sidebar-item {
    width: 250px;
}
.sidebar-nav ul .sidebar-item .sidebar-link {
    color: #fff;
    padding: 12px 15px;
    display: flex;
    white-space: nowrap;
    align-items: center;
    line-height: 25px;
    opacity: 0.6;
}
.sidebar-nav ul .sidebar-item .first-level .sidebar-item.active .sidebar-link,
.sidebar-nav ul .sidebar-item .sidebar-link.active,
.sidebar-nav ul .sidebar-item .sidebar-link:hover,
.sidebar-nav ul .sidebar-item.selected > .sidebar-link {
    opacity: 1;
}
.sidebar-nav ul .sidebar-item .sidebar-link i {
    font-style: normal;
    width: 35px;
    line-height: 25px;
    font-size: 23px;
    color: #fff;
    display: inline-block;
    text-align: center;
}
.sidebar-nav ul .sidebar-item .first-level {
    padding: 0 0 10px;
}
.sidebar-nav ul .sidebar-item .first-level .sidebar-item .sidebar-link {
    padding: 10px 15px;
}
.sidebar-nav ul .sidebar-item .first-level .sidebar-item .sidebar-link i {
    font-size: 14px;
}
.sidebar-nav ul .nav-small-cap {
    font-size: 12px;
    padding: 12px 15px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    line-height: 30px;
    margin-top: 10px;
    color: #fff;
    opacity: 1;
    text-transform: uppercase;
}
.sidebar-nav ul .nav-small-cap i {
    line-height: 30px;
    margin: 0 5px;
}
.sidebar-nav ul {
    margin: 0;
    padding: 0;
}
.sidebar-nav ul li {
    list-style: none;
}
.collapse.in {
    display: block;
}
.sidebar-nav .has-arrow::after {
    position: absolute;
    content: "";
    width: 7px;
    height: 7px;
    border-width: 1px 0 0 1px;
    border-style: solid;
    border-color: #fff;
    margin-left: 10px;
    -webkit-transform: rotate(135deg) translate(0, -50%);
    -ms-transform: rotate(135deg) translate(0, -50%);
    -o-transform: rotate(135deg) translate(0, -50%);
    transform: rotate(135deg) translate(0, -50%);
    -webkit-transform-origin: top;
    -ms-transform-origin: top;
    -o-transform-origin: top;
    transform-origin: top;
    top: 26px;
    right: 15px;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}
.sidebar-nav .has-arrow[aria-expanded="true"]::after,
.sidebar-nav li.active > .has-arrow::after,
.sidebar-nav li > .has-arrow.active::after {
    -webkit-transform: rotate(-135deg) translate(0, -50%);
    -ms-transform: rotate(-135deg) translate(0, -50%);
    -o-transform: rotate(-135deg) translate(0, -50%);
    transform: rotate(-135deg) translate(0, -50%);
}
.user-profile {
    padding: 15px;
}
.user-profile .user-content,
.user-profile .user-email,
.user-profile .user-name {
    color: #fff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.create-btn {
    height: 40px;
    background: #343a40;
}
.waves-effect {
    position: relative;
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    z-index: 1;
    will-change: opacity, transform;
    -webkit-transition: all 0.1s ease-out;
    -moz-transition: all 0.1s ease-out;
    -o-transition: all 0.1s ease-out;
    -ms-transition: all 0.1s ease-out;
    transition: all 0.1s ease-out;
}
.waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    opacity: 0;
    background: rgba(0, 0, 0, 0.2);
    -webkit-transition: all 0.7s ease-out;
    -moz-transition: all 0.7s ease-out;
    -o-transition: all 0.7s ease-out;
    -ms-transition: all 0.7s ease-out;
    transition: all 0.7s ease-out;
    -webkit-transition-property: -webkit-transform, opacity;
    -moz-transition-property: -moz-transform, opacity;
    -o-transition-property: -o-transform, opacity;
    transition-property: transform, opacity;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
}
.waves-effect.waves-light .waves-ripple {
    background-color: rgba(255, 255, 255, 0.45);
}
.waves-effect.waves-red .waves-ripple {
    background-color: rgba(244, 67, 54, 0.7);
}
.waves-effect.waves-yellow .waves-ripple {
    background-color: rgba(255, 235, 59, 0.7);
}
.waves-effect.waves-orange .waves-ripple {
    background-color: rgba(255, 152, 0, 0.7);
}
.waves-effect.waves-purple .waves-ripple {
    background-color: rgba(156, 39, 176, 0.7);
}
.waves-effect.waves-green .waves-ripple {
    background-color: rgba(76, 175, 80, 0.7);
}
.waves-effect.waves-teal .waves-ripple {
    background-color: rgba(0, 150, 136, 0.7);
}
html body .waves-notransition {
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    -ms-transition: none;
    transition: none;
}
.waves-circle {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
    text-align: center;
    width: 2.5em;
    height: 2.5em;
    line-height: 2.5em;
    border-radius: 50%;
    -webkit-mask-image: none;
}
.waves-input-wrapper {
    border-radius: 0.2em;
    vertical-align: bottom;
}
.waves-input-wrapper .waves-button-input {
    position: relative;
    top: 0;
    left: 0;
    z-index: 1;
}
.waves-block {
    display: block;
}
.btn-circle {
    border-radius: 100%;
    width: 40px;
    height: 40px;
    padding: 5px;
    line-height: 30px;
}
.btn-circle.btn-sm,
.btn-group-sm > .btn-circle.btn {
    width: 35px;
    height: 35px;
    padding: 8px 10px;
    font-size: 14px;
}
.btn-circle.btn-lg,
.btn-group-lg > .btn-circle.btn {
    width: 50px;
    height: 50px;
    padding: 14px 10px;
    font-size: 18px;
    line-height: 23px;
}
.btn-circle.btn-xl {
    width: 70px;
    height: 70px;
    padding: 14px 15px;
    font-size: 24px;
}
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 10px;
}
.btn-rounded {
    border-radius: 60px;
    padding: 7px 18px;
}
.btn-group-lg > .btn-rounded.btn,
.btn-rounded.btn-lg {
    padding: 0.75rem 1.5rem;
}
.btn-group-sm > .btn-rounded.btn,
.btn-rounded.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 12px;
}
.btn-rounded.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 10px;
}
.btn-rounded.btn-md {
    padding: 12px 35px;
    font-size: 16px;
}
.btn-default,
.btn-default:hover,
.btn-outline-primary:hover,
.btn-outline-primary:hover:hover,
.btn-outline-warning:hover,
.btn-outline-warning:hover:hover,
.btn-primary,
.btn-primary:hover,
.btn-warning,
.btn-warning:hover {
    color: #fff;
}
.btn-default {
    background-color: #2962ff;
}
.btn-facebook {
    color: #fff;
    background-color: #3b5998;
}
.btn-facebook:hover {
    color: #fff;
}
.btn-twitter {
    color: #fff;
    background-color: #55acee;
}
.btn-twitter:hover {
    color: #fff;
}
.btn-linkedin {
    color: #fff;
    background-color: #007bb6;
}
.btn-linkedin:hover {
    color: #fff;
}
.btn-dribbble {
    color: #fff;
    background-color: #ea4c89;
}
.btn-dribbble:hover {
    color: #fff;
}
.btn-googleplus {
    color: #fff;
    background-color: #dd4b39;
}
.btn-googleplus:hover {
    color: #fff;
}
.btn-instagram {
    color: #fff;
    background-color: #3f729b;
}
.btn-pinterest {
    color: #fff;
    background-color: #cb2027;
}
.btn-dropbox {
    color: #fff;
    background-color: #007ee5;
}
.btn-flickr {
    color: #fff;
    background-color: #ff0084;
}
.btn-tumblr {
    color: #fff;
    background-color: #32506d;
}
.btn-skype {
    color: #fff;
    background-color: #00aff0;
}
.btn-youtube {
    color: #fff;
    background-color: #b00;
}
.btn-github {
    color: #fff;
    background-color: #171515;
}
.button-group .btn {
    margin-bottom: 5px;
    margin-right: 5px;
}
.no-button-group .btn {
    margin-bottom: 5px;
    margin-right: 0;
}
.btn .text-active {
    display: none;
}
.btn.active .text-active {
    display: inline-block;
}
.btn.active .text {
    display: none;
}
.customizer {
    background: #fff;
    position: fixed;
    height: 100%;
    width: 280px;
    top: 0;
    right: -280px;
    z-index: 100;
    transition: 0.3s ease-in;
}
.customizer .service-panel-toggle {
    background: #f62d51;
    padding: 12px 20px;
    color: #fff;
    position: absolute;
    top: 30%;
    left: -54px;
}
.customizer.show-service-panel {
    right: 0;
}
.customizer .customizer-body {
    position: relative;
    height: 100%;
}
.customizer .customizer-tab {
    display: flex;
}
.customizer .customizer-tab .nav-item {
    width: 33.33%;
    text-align: center;
}
.customizer .customizer-tab .nav-item .nav-link {
    padding: 15px 12px;
    color: #3e5569;
    border-bottom: 3px solid transparent;
}
.customizer .customizer-tab .nav-item .nav-link.active,
.customizer .customizer-tab .nav-item .nav-link:hover {
    border-bottom: 3px solid #2962ff;
    color: #2962ff;
}
.custom-control-label::after,
.custom-control-label::before {
    top: 0.15rem;
}
.theme-color {
    padding: 0;
    margin: 0;
    list-style: none;
}
.theme-color .theme-item {
    list-style: none;
    display: inline-block;
    margin-right: 5px;
}
.theme-color .theme-item .theme-link {
    border-radius: 100%;
    width: 20px;
    height: 20px;
    display: block;
}
.theme-color .theme-item .theme-link[data-logobg="skin1"],
.theme-color .theme-item .theme-link[data-navbarbg="skin1"],
.theme-color .theme-item .theme-link[data-sidebarbg="skin1"] {
    background: #2962ff;
}
.theme-color .theme-item .theme-link[data-logobg="skin2"],
.theme-color .theme-item .theme-link[data-navbarbg="skin2"],
.theme-color .theme-item .theme-link[data-sidebarbg="skin2"] {
    background: #fe5419;
}
.theme-color .theme-item .theme-link[data-logobg="skin3"],
.theme-color .theme-item .theme-link[data-navbarbg="skin3"],
.theme-color .theme-item .theme-link[data-sidebarbg="skin3"] {
    background: #00b0ff;
}
.theme-color .theme-item .theme-link[data-logobg="skin4"],
.theme-color .theme-item .theme-link[data-navbarbg="skin4"],
.theme-color .theme-item .theme-link[data-sidebarbg="skin4"] {
    background: #6659f7;
}
.theme-color .theme-item .theme-link[data-logobg="skin5"],
.theme-color .theme-item .theme-link[data-navbarbg="skin5"],
.theme-color .theme-item .theme-link[data-sidebarbg="skin5"] {
    background: #414755;
}
.theme-color .theme-item .theme-link[data-logobg="skin6"],
.theme-color .theme-item .theme-link[data-navbarbg="skin6"],
.theme-color .theme-item .theme-link[data-sidebarbg="skin6"] {
    background: #e9ecef;
} 
.slideOutUp {
    -webkit-animation-name: slideOutUp;
    animation-name: slideOutUp;
}
.ps-container {
    -ms-touch-action: auto;
    touch-action: auto;
    overflow: hidden !important;
    -ms-overflow-style: none;
}
@supports (-ms-overflow-style: none) {
    .ps-container {
        overflow: auto !important;
    }
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .ps-container {
        overflow: auto !important;
    }
}
.ps-container.ps-active-x > .ps-scrollbar-x-rail,
.ps-container.ps-active-y > .ps-scrollbar-y-rail {
    display: block;
    background-color: transparent;
}
.ps-container.ps-in-scrolling.ps-x > .ps-scrollbar-x-rail {
    background-color: transparent;
    opacity: 0.9;
}
.ps-container.ps-in-scrolling.ps-x > .ps-scrollbar-x-rail > .ps-scrollbar-x {
    background-color: rgba(0, 0, 0, 0.2);
    height: 11px;
}
.ps-container.ps-in-scrolling.ps-y > .ps-scrollbar-y-rail {
    background-color: transparent;
    opacity: 0.9;
}
.ps-container.ps-in-scrolling.ps-y > .ps-scrollbar-y-rail > .ps-scrollbar-y {
    background-color: rgba(0, 0, 0, 0.2);
    width: 11px;
}
.ps-container > .ps-scrollbar-x-rail {
    display: none;
    position: absolute;
    opacity: 0;
    -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
    -o-transition: background-color 0.2s linear, opacity 0.2s linear;
    -moz-transition: background-color 0.2s linear, opacity 0.2s linear;
    transition: background-color 0.2s linear, opacity 0.2s linear;
    bottom: 0;
    height: 15px;
}
.ps-container > .ps-scrollbar-x-rail > .ps-scrollbar-x {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.2);
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, -webkit-border-radius 0.2s ease-in-out;
    -o-transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, border-radius 0.2s ease-in-out;
    -moz-transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, border-radius 0.2s ease-in-out, -moz-border-radius 0.2s ease-in-out;
    transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, border-radius 0.2s ease-in-out;
    transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, border-radius 0.2s ease-in-out, -webkit-border-radius 0.2s ease-in-out, -moz-border-radius 0.2s ease-in-out;
    bottom: 2px;
    height: 6px;
}
.ps-container > .ps-scrollbar-x-rail:active > .ps-scrollbar-x,
.ps-container > .ps-scrollbar-x-rail:hover > .ps-scrollbar-x {
    height: 6px;
}
.ps-container > .ps-scrollbar-y-rail {
    display: none;
    position: absolute;
    opacity: 0;
    -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
    -o-transition: background-color 0.2s linear, opacity 0.2s linear;
    -moz-transition: background-color 0.2s linear, opacity 0.2s linear;
    transition: background-color 0.2s linear, opacity 0.2s linear;
    right: 0;
    width: 15px;
}
.ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.2);
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, -webkit-border-radius 0.2s ease-in-out;
    -o-transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, border-radius 0.2s ease-in-out;
    -moz-transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, border-radius 0.2s ease-in-out, -moz-border-radius 0.2s ease-in-out;
    transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, border-radius 0.2s ease-in-out;
    transition: background-color 0.2s linear, height 0.2s linear, width 0.2s ease-in-out, border-radius 0.2s ease-in-out, -webkit-border-radius 0.2s ease-in-out, -moz-border-radius 0.2s ease-in-out;
    right: 2px;
    width: 6px;
}
.ps-container > .ps-scrollbar-y-rail:active > .ps-scrollbar-y,
.ps-container > .ps-scrollbar-y-rail:hover > .ps-scrollbar-y {
    width: 6px;
}
.ps-container:hover.ps-in-scrolling.ps-x > .ps-scrollbar-x-rail {
    background-color: transparent;
    opacity: 0.9;
}
.ps-container:hover.ps-in-scrolling.ps-x > .ps-scrollbar-x-rail > .ps-scrollbar-x {
    background-color: rgba(0, 0, 0, 0.2);
    height: 6px;
}
.ps-container:hover.ps-in-scrolling.ps-y > .ps-scrollbar-y-rail {
    background-color: transparent;
    opacity: 0.9;
}
.ps-container:hover.ps-in-scrolling.ps-y > .ps-scrollbar-y-rail > .ps-scrollbar-y {
    background-color: rgba(0, 0, 0, 0.2);
    width: 6px;
}
.ps-container:hover > .ps-scrollbar-x-rail,
.ps-container:hover > .ps-scrollbar-y-rail {
    opacity: 0.6;
}
.ps-container:hover > .ps-scrollbar-x-rail:hover {
    background-color: transparent;
    opacity: 0.9;
}
.ps-container:hover > .ps-scrollbar-x-rail:hover > .ps-scrollbar-x {
    background-color: rgba(0, 0, 0, 0.2);
}
.ps-container:hover > .ps-scrollbar-y-rail:hover {
    background-color: transparent;
    opacity: 0.9;
}
.ps-container:hover > .ps-scrollbar-y-rail:hover > .ps-scrollbar-y {
    background-color: rgba(0, 0, 0, 0.2);
}
.ps-container .ps-scrollbar-y-rail {
    position: absolute;
    right: 3px;
    width: 8px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    opacity: 0;
    -o-transition: background-color 0.2s linear, opacity 0.2s linear;
    -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
    -moz-transition: background-color 0.2s linear, opacity 0.2s linear;
    transition: background-color 0.2s linear, opacity 0.2s linear;
}
html body .flotTip,
html body .jqstooltip {
    width: auto !important;
    height: auto !important;
    background: #212529;
    color: #fff;
    padding: 5px 10px;
}
body .jqstooltip {
    border-color: transparent;
    border-radius: 60px;
}
.chartist-tooltip {
    position: absolute;
    display: inline-block;
    opacity: 0;
    border-radius: 2px;
    padding: 10px 20px;
    background: #2962ff;
    color: #fff;
    ext-align: center;
    z-index: 1;
    -webkit-transition: opacity 0.2s linear;
    -moz-transition: opacity 0.2s linear;
    -o-transition: opacity 0.2s linear;
    transition: opacity 0.2s linear;
}
.chartist-tooltip:before {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -5px;
    border: 5px solid transparent;
    border-top-color: #2962ff;
}
.chartist-tooltip.tooltip-show {
    opacity: 1;
}
.c3-chart-arcs-title {
    fill: #a1aab2;
}
.c3 line,
.c3 path {
    stroke: #e9ecef;
}
.c3-chart-arc path {
    stroke: transparent;
}
.popover-item {
    margin-left: -15px;
}
.popover-item:hover {
    z-index: 10;
    position: relative;
}
.custom-select {
    -moz-appearance: none;
    -webkit-appearance: none;
    -o-appearance: none;
    background-size: auto;
}
.select2-container--classic .select2-selection--single,
.select2-container--default .select2-selection--multiple,
.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__arrow,
.select2-container--default .select2-selection--single .select2-selection__rendered {
    border-color: #e9ecef;
    height: 40px;
    color: #3e5569;
    line-height: 40px;
}
.select2-container--default .select2-selection--multiple {
    line-height: 27px;
}
.select2-container--classic .select2-selection--multiple .select2-selection__choice,
.select2-container--default .select2-selection--multiple .select2-selection__choice,
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    background-color: #2962ff;
    border-color: #2962ff;
    color: #fff;
}
.m-icon {
    width: 33%;
    display: inline-block;
}
@media (max-width: 767.98px) {
    .m-icon {
        width: 100%;
    }
}
.f-icon,
.if-icon,
.m-icon,
.sl-icon,
.t-icon,
.w-icon {
    cursor: pointer;
    padding: 13px 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.f-icon:hover,
.if-icon:hover,
.m-icon:hover,
.sl-icon:hover,
.t-icon:hover,
.w-icon:hover {
    background-color: #f8f9fa;
}
.table.dataTable {
    border-collapse: collapse !important;
}
.dataTables_wrapper {
    padding: 0;
}
.dropzone,
.note-editor.note-frame {
    border-color: #e9ecef;
}
.note-toolbar-wrapper {
    height: 33px !important;
}
.dropzone {
    border-style: dotted;
}
.note-toolbar {
    z-index: 1;
}
.nav-pills.custom-pills .nav-link {
    border-radius: 0;
    opacity: 0.7;
}
.nav-pills.custom-pills .nav-link.active {
    color: #2962ff;
    opacity: 1;
    background-color: transparent;
    border-bottom: 2px solid #2962ff;
}
label {
    font-weight: 600;
}
.r-separator .form-group {
    border-bottom: 1px solid #e9ecef;
}
.r-separator .form-group:last-child {
    border-bottom: none;
}
.striped-rows .row:nth-child(odd) {
    background-color: #f8f9fa;
}
.b-form .row {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
    padding: 15px 0;
}
.b-form .row:last-child {
    border-bottom: none;
}
.b-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.error .form-control {
    border-color: #f62d51;
}
.error .help-block {
    color: #f62d51;
}
.validate .form-control {
    border-color: #36bea6;
}
.validate .help-block {
    color: #36bea6;
}
@media (min-height: 33.875em) {
    .picker--opened .picker__frame {
        top: 30%;
        bottom: auto;
    }
}
.dtp > .dtp-content > .dtp-date-view > header.dtp-header {
    background: #1d50de;
}
.dtp div.dtp-date,
.dtp div.dtp-time,
.dtp table.dtp-picker-days tr > td > a.selected {
    background: #2962ff;
}
.dtp .p10 > a {
    color: #fff;
}
.datepicker .day,
.datepicker .dow {
    padding: 5px 10px;
}
.docs-buttons .btn,
.docs-data .input-group {
    margin-bottom: 5px;
}
.scrollable {
    position: relative;
}
.error-box {
    height: 100%;
    position: fixed;
    background: url(../assets/images/background/error-bg.jpg) center center no-repeat #fff;
    width: 100%;
}
.error-box .error-body {
    padding-top: 5%;
}
.error-box .error-title {
    font-size: 210px;
    font-weight: 900;
    text-shadow: 4px 4px 0 #fff, 6px 6px 0 #343a40;
    line-height: 210px;
}
.price-label {
    position: absolute;
    top: -10px;
    margin: 0 auto;
    left: 0;
    right: 0;
    width: 100px;
    padding: 5px 10px;
}
.price-sign {
    position: absolute;
    font-size: 15px;
    top: 5px;
    margin-left: -10px;
}
.gaugejs-box {
    position: relative;
    margin: 0 auto;
}
.gaugejs-box canvas.gaugejs {
    width: 100% !important;
    height: auto !important;
}
#demo-foo-accordion .open > .dropdown-menu,
#demo-foo-accordion2 .open > .dropdown-menu,
#footable-addrow .open > .dropdown-menu {
    display: block;
}
table.footable thead tr.footable-filtering th ul.dropdown-menu li a.checkbox {
    padding-left: 0;
}
table.footable thead tr.footable-filtering th ul.dropdown-menu li a.checkbox input[type="checkbox"] {
    position: relative;
    margin-right: 10px;
    margin-left: -20px;
    left: 0;
    opacity: 1;
}
table.footable thead tr.footable-filtering th ul.dropdown-menu li .checkbox label::before {
    display: none;
}
.footable .pagination li a {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #0275d8;
    background-color: #fff;
    border: 1px solid #ddd;
}
.auth-wrapper #recoverform,
.jvectormap-zoomin,
.jvectormap-zoomout,
html[dir="rtl"] .dz-hidden-input {
    display: none;
}
.footable .pagination li.active a {
    z-index: 2;
    color: #fff;
    background-color: #2962ff;
    border-color: #2962ff;
}
.footable .pagination li.disabled a {
    color: #e9ecef;
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #f8f9fa;
}
.footable .pagination li:first-child a {
    margin-left: 0;
    border-bottom-left-radius: 0.25rem;
    border-top-left-radius: 0.25rem;
}
html[dir="rtl"] body .m-b-5 {
    margin-bottom: 5px;
}
html[dir="rtl"] body .m-r-5 {
    margin-left: 5px;
}
html[dir="rtl"] body .m-l-5 {
    margin-right: 5px;
}
html[dir="rtl"] body .p-t-5 {
    padding-top: 5px;
}
html[dir="rtl"] body .p-b-5 {
    padding-bottom: 5px;
}
html[dir="rtl"] body .p-r-5 {
    padding-left: 5px;
}
html[dir="rtl"] body .p-l-5 {
    padding-right: 5px;
}
html[dir="rtl"] body .p-5 {
    padding: 5px;
}
html[dir="rtl"] body .m-5 {
    margin: 5px;
}
html[dir="rtl"] body .m-t-10 {
    margin-top: 10px;
}
html[dir="rtl"] body .m-b-10 {
    margin-bottom: 10px;
}
html[dir="rtl"] body .m-r-10 {
    margin-left: 10px;
}
html[dir="rtl"] body .m-l-10 {
    margin-right: 10px;
}
html[dir="rtl"] body .p-t-10 {
    padding-top: 10px;
}
html[dir="rtl"] body .p-b-10 {
    padding-bottom: 10px;
}
html[dir="rtl"] body .p-r-10 {
    padding-left: 10px;
}
html[dir="rtl"] body .p-l-10 {
    padding-right: 10px;
}
html[dir="rtl"] body .p-10 {
    padding: 10px;
}
html[dir="rtl"] body .m-10 {
    margin: 10px;
}
html[dir="rtl"] body .m-t-15 {
    margin-top: 15px;
}
html[dir="rtl"] body .m-b-15 {
    margin-bottom: 15px;
}
html[dir="rtl"] body .m-r-15 {
    margin-left: 15px;
}
html[dir="rtl"] body .m-l-15 {
    margin-right: 15px;
}
html[dir="rtl"] body .p-t-15 {
    padding-top: 15px;
}
html[dir="rtl"] body .p-b-15 {
    padding-bottom: 15px;
}
html[dir="rtl"] body .p-r-15 {
    padding-left: 15px;
}
html[dir="rtl"] body .p-l-15 {
    padding-right: 15px;
}
html[dir="rtl"] body .p-15 {
    padding: 15px;
}
html[dir="rtl"] body .m-15 {
    margin: 15px;
}
html[dir="rtl"] body .m-t-20 {
    margin-top: 20px;
}
html[dir="rtl"] body .m-b-20 {
    margin-bottom: 20px;
}
html[dir="rtl"] body .m-r-20 {
    margin-left: 20px;
}
html[dir="rtl"] body .m-l-20 {
    margin-right: 20px;
}
html[dir="rtl"] body .p-t-20 {
    padding-top: 20px;
}
html[dir="rtl"] body .p-b-20 {
    padding-bottom: 20px;
}
html[dir="rtl"] body .p-r-20 {
    padding-left: 20px;
}
html[dir="rtl"] body .p-l-20 {
    padding-right: 20px;
}
html[dir="rtl"] body .p-20 {
    padding: 20px;
}
html[dir="rtl"] body .m-20 {
    margin: 20px;
}
html[dir="rtl"] body .m-t-25 {
    margin-top: 25px;
}
html[dir="rtl"] body .m-b-25 {
    margin-bottom: 25px;
}
html[dir="rtl"] body .m-r-25 {
    margin-left: 25px;
}
html[dir="rtl"] body .m-l-25 {
    margin-right: 25px;
}
html[dir="rtl"] body .p-t-25 {
    padding-top: 25px;
}
html[dir="rtl"] body .p-b-25 {
    padding-bottom: 25px;
}
html[dir="rtl"] body .p-r-25 {
    padding-left: 25px;
}
html[dir="rtl"] body .p-l-25 {
    padding-right: 25px;
}
html[dir="rtl"] body .p-25 {
    padding: 25px;
}
html[dir="rtl"] body .m-25 {
    margin: 25px;
}
html[dir="rtl"] body .m-t-30 {
    margin-top: 30px;
}
html[dir="rtl"] body .m-b-30 {
    margin-bottom: 30px;
}
html[dir="rtl"] body .m-r-30 {
    margin-left: 30px;
}
html[dir="rtl"] body .m-l-30 {
    margin-right: 30px;
}
html[dir="rtl"] body .p-t-30 {
    padding-top: 30px;
}
html[dir="rtl"] body .p-b-30 {
    padding-bottom: 30px;
}
html[dir="rtl"] body .p-r-30 {
    padding-left: 30px;
}
html[dir="rtl"] body .p-l-30 {
    padding-right: 30px;
}
html[dir="rtl"] body .p-30 {
    padding: 30px;
}
html[dir="rtl"] body .m-30 {
    margin: 30px;
}
html[dir="rtl"] body .m-t-40 {
    margin-top: 40px;
}
html[dir="rtl"] body .m-b-40 {
    margin-bottom: 40px;
}
html[dir="rtl"] body .m-r-40 {
    margin-left: 40px;
}
html[dir="rtl"] body .m-l-40 {
    margin-right: 40px;
}
html[dir="rtl"] body .p-t-40 {
    padding-top: 40px;
}
html[dir="rtl"] body .p-b-40 {
    padding-bottom: 40px;
}
html[dir="rtl"] body .p-r-40 {
    padding-left: 40px;
}
html[dir="rtl"] body .p-l-40 {
    padding-right: 40px;
}
html[dir="rtl"] body .p-40 {
    padding: 40px;
}
html[dir="rtl"] body .m-40 {
    margin: 40px;
}
html[dir="rtl"] body .m-0-0 {
    margin: 0;
}
html[dir="rtl"] body .m-t-0 {
    margin-top: 0;
}
html[dir="rtl"] body .m-r-0 {
    margin-left: 0;
}
html[dir="rtl"] body .m-b-0 {
    margin-bottom: 0;
}
html[dir="rtl"] body .m-l-0 {
    margin-right: 0;
}
html[dir="rtl"] body .p-0-0 {
    padding: 0;
}
html[dir="rtl"] body .p-t-0 {
    padding-top: 0;
}
html[dir="rtl"] body .p-r-0 {
    padding-left: 0;
}
html[dir="rtl"] body .p-b-0 {
    padding-bottom: 0;
}
html[dir="rtl"] body .p-l-0 {
    padding-right: 0;
}
html[dir="rtl"] body {
    text-align: right;
    direction: rtl;
}
html[dir="rtl"] .page-breadcrumb .breadcrumb .breadcrumb-item + .breadcrumb-item {
    padding-right: 0.5rem;
    padding-left: 0;
}
html[dir="rtl"] .page-breadcrumb .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "\e64a";
    padding-right: 0;
    padding-left: 0.5rem;
}
html[dir="rtl"] .custom-select {
    padding: 0.375rem 0.75rem 0.375rem 1.75rem;
    background: url(../assets/images/custom-select.png) left 0.35rem center no-repeat #fff;
}
html[dir="rtl"] .custom-control {
    padding-left: 0;
    padding-right: 1.5rem;
}
html[dir="rtl"] .custom-control-label::after,
html[dir="rtl"] .custom-control-label::before {
    left: auto;
    right: -25px;
}
html[dir="rtl"] .list-group {
    padding: 0;
}
html[dir="rtl"] .float-right {
    float: left !important;
}
html[dir="rtl"] .text-left {
    text-align: right !important;
}
html[dir="rtl"] .text-right {
    text-align: left !important;
}
html[dir="rtl"] .close {
    float: left;
}
html[dir="rtl"] .modal-header .close {
    float: left;
    margin: -1rem auto -1rem -1rem;
}
html[dir="rtl"] .wizard-content .wizard.vertical > .steps,
html[dir="rtl"] .wizard > .actions > ul > li,
html[dir="rtl"] .wizard > .steps > ul > li {
    float: right;
}
html[dir="rtl"] .modal-footer > :not(:last-child) {
    margin-left: 0.25rem;
    margin-right: 0;
}
html[dir="rtl"] .ml-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}
html[dir="rtl"] .mr-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}
html[dir="rtl"] .border-right {
    border-right: none !important;
    border-left: 1px solid #e9ecef !important;
}
html[dir="rtl"] .border-left {
    border-left: none !important;
    border-right: 1px solid #e9ecef !important;
}
html[dir="rtl"] .alert-dismissible {
    padding-left: 3.8125rem;
    padding-right: 1.25rem;
}
html[dir="rtl"] .alert-dismissible .close {
    left: 0;
    right: auto;
}
html[dir="rtl"] .custom-file-label {
    left: auto;
    right: 0;
    width: 100%;
}
html[dir="rtl"] .custom-file-label::after,
html[dir="rtl"] .dropdown-menu-right {
    right: auto;
    left: 0;
}
html[dir="rtl"] .dropdown-menu {
    text-align: right;
}
html[dir="rtl"] .topbar .navbar-collapse {
    padding: 0 0 0 10px;
}
html[dir="rtl"] .list-unstyled,
html[dir="rtl"] .nav,
html[dir="rtl"] .navbar-nav,
html[dir="rtl"] .pagination {
    padding-right: 0;
}
html[dir="rtl"] .topbar .top-navbar .navbar-header .navbar-brand .logo-icon {
    margin-right: 0;
}
html[dir="rtl"] .topbar .dropdown-menu.dropdown-menu-right .with-arrow {
    left: 0;
    right: auto;
}
html[dir="rtl"] .search-box .app-search .srh-btn,
html[dir="rtl"] .topbar .dropdown-menu.dropdown-menu-right .with-arrow > span {
    left: 20px;
    right: auto;
}
html[dir="rtl"] .mailbox .message-center .message-item .mail-contnet {
    padding-right: 10px;
    padding-left: 0;
}
html[dir="rtl"] .customizer {
    left: -280px;
    right: auto;
}
html[dir="rtl"] .customizer.show-service-panel {
    left: 0;
    right: auto;
}
html[dir="rtl"] .customizer .service-panel-toggle {
    right: -54px;
    left: auto;
}
html[dir="rtl"] .sidebar-nav .has-arrow::after {
    margin-left: 0;
    margin-right: 10px;
    right: auto;
    left: 15px;
}
html[dir="rtl"] .left-part {
    border-left: 1px solid #e9ecef;
    border-right: none;
}
html[dir="rtl"] .left-part .show-left-part {
    left: -41px;
    right: auto;
}
html[dir="rtl"] .right-part {
    margin-right: 260px;
    margin-left: 0;
}
html[dir="rtl"] .reverse-mode .left-part {
    right: auto;
    left: 0;
}
html[dir="rtl"] .reverse-mode .show-left-part {
    left: auto;
    right: -41px;
}
html[dir="rtl"] .reverse-mode .right-part {
    margin-right: 0;
    margin-left: 260px;
}
html[dir="rtl"] .profiletimeline {
    padding-right: 40px;
    padding-left: 0;
    margin: 40px 30px 0 10px;
    border-right: 1px solid #e9ecef;
    border-left: none;
}
html[dir="rtl"] .profiletimeline .sl-left {
    float: right;
    margin-right: -60px;
    margin-left: 15px;
}
html[dir="rtl"] .feed-widget .feed-body .feed-item > .feed-icon {
    margin-right: 0;
    margin-left: 10px;
}
html[dir="rtl"] div.dataTables_wrapper div.dataTables_filter {
    text-align: left;
}
html[dir="rtl"] table.table-bordered.dataTable td,
html[dir="rtl"] table.table-bordered.dataTable th {
    border-left-width: 1px;
}
html[dir="rtl"] div.dataTables_wrapper div.dataTables_filter input {
    margin-right: 0.5rem;
    margin-left: 0;
}
html[dir="rtl"] div.table-responsive > div.dataTables_wrapper > div.row > div[class^="col-"]:last-child {
    padding-left: 1px;
}
html[dir="rtl"] .lobilists .lobilist-actions {
    right: auto;
    left: 8px;
}
html[dir="rtl"] .lobilists .lobilist-item .todo-actions {
    right: auto;
    left: 4px;
}
html[dir="rtl"] .lobilists .lobilist-check {
    left: auto;
    right: 12px;
}
html[dir="rtl"] .lobilists .lobilist-item,
html[dir="rtl"] .lobilists .lobilist-item-placeholder {
    padding-left: 0;
    padding-right: 35px;
}
html[dir="rtl"] .lobilists .lobilist-item .drag-handler {
    left: auto;
    right: 0;
}
html[dir="rtl"] .lobilists .lobilist-placeholder,
html[dir="rtl"] .lobilists .lobilist-wrapper {
    margin-left: 16px;
    margin-right: 0;
}
html[dir="rtl"] .datepicker {
    direction: rtl;
}
html[dir="rtl"] .minicolors .minicolors-grid {
    right: 22px;
    left: auto;
}
html[dir="rtl"] .wizard-content .wizard > .steps > ul > li:after {
    right: auto;
    left: 0;
}
html[dir="rtl"] .wizard-content .wizard > .steps > ul > li:before {
    left: auto;
    right: 0;
}
html[dir="rtl"] .css-bar > i {
    margin-left: 0;
    margin-right: 5px;
}
html[dir="rtl"] .treeview span.icon {
    margin-right: 0;
    margin-left: 5px;
}
html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebartype="full"] .marketking_dashboard_page_wrapper {
    margin-right: 250px;
    margin-left: 0;
}
html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebartype="iconbar"] .marketking_dashboard_page_wrapper {
    margin-right: 180px;
    margin-left: 0;
}
html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebartype="overlay"] .left-sidebar {
    right: -250px;
    left: auto;
}
html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebartype="overlay"].show-sidebar .left-sidebar {
    right: 0;
    left: auto;
}
@media (min-width: 768px) {
    html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebar-position="fixed"][data-sidebartype="full"] .topbar .top-navbar .navbar-collapse,
    html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebar-position="fixed"][data-sidebartype="overlay"] .topbar .top-navbar .navbar-collapse {
        margin-right: 250px;
        margin-left: 0;
    }
    html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebar-position="fixed"][data-sidebartype="iconbar"] .topbar .top-navbar .navbar-collapse {
        margin-right: 180px;
        margin-left: 0;
    }
    html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebar-position="fixed"][data-sidebartype="mini-sidebar"] .topbar .top-navbar .navbar-collapse,
    html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebartype="mini-sidebar"] .marketking_dashboard_page_wrapper {
        margin-right: 65px;
        margin-left: 0;
    }
}
@media (max-width: 766px) {
    html[dir="rtl"] #marketking_dashboard_wrapper[data-sidebartype="mini-sidebar"] .left-sidebar {
        right: -250px;
        left: auto;
    }
    html[dir="rtl"] #marketking_dashboard_wrapper.show-sidebar .left-sidebar {
        right: 0;
        left: auto;
    }
    html[dir="rtl"] .left-part {
        right: -260px;
        left: auto;
    }
    html[dir="rtl"] .left-part.show-panel {
        right: 0;
        left: auto;
    }
    html[dir="rtl"] .right-part {
        margin-right: 0;
    }
}
.campaign2 {
    position: relative;
    height: 150px;
}
.campaign {
    position: relative;
    height: 285px;
}
.campaign .ct-series-a .ct-area {
    fill-opacity: 0.2;
    fill: url(#gradient);
}
.campaign .ct-series-a .ct-line,
.campaign .ct-series-a .ct-point {
    stroke: #4fc3f7;
    stroke-width: 2px;
}
.campaign .ct-series-b .ct-area {
    fill: #7460ee;
    fill-opacity: 0.1;
}
.campaign .ct-series-b .ct-line,
.campaign .ct-series-b .ct-point {
    stroke: #7460ee;
    stroke-width: 2px;
}
.campaign .ct-series-a .ct-point,
.campaign .ct-series-b .ct-point {
    stroke-width: 6px;
}
.earningsbox .c3-line {
    stroke-width: 2px;
}
.product-sales.c3 line,
.product-sales.c3 path {
    stroke: #e9ecef;
}
.e-campaign .css-bar .data-text {
    margin: 0 auto;
    position: absolute;
    left: 0;
    z-index: 200;
    right: 0;
    top: 70px;
    text-align: center;
}
.e-campaign .css-bar .data-text .success-rate {
    font-size: 60px;
}
.e-campaign .css-bar .data-text .rate-label {
    margin-top: -20px;
}
.e-campaign .c-cost {
    padding: 10px 20px;
    border: 1px solid #e9ecef;
    border-radius: 5px;
}
.e-campaign .c-cost i {
    font-size: 48px;
}
.poll-widget .collapsible {
    border: none;
    box-shadow: none;
}
.poll-widget .collapsible .collapsible-body,
.poll-widget .collapsible .collapsible-header {
    border-bottom: none;
    padding: 1rem 0;
}
#calendar .fc-toolbar {
    padding: 0 15px 24px;
}
@media (max-width: 767px) {
    .e-campaign .c-cost {
        margin: 0 auto;
        width: 100%;
    }
    .e-campaign .stats {
        text-align: center;
    }
}
.css-bar-xlg {
    width: 223px;
    height: 223px;
    font-size: 20px;
}
.css-bar-xlg:after,
.css-bar-xlg > img {
    width: 213px;
    height: 213px;
    margin-left: 5px;
    margin-top: 5px;
    line-height: 30px;
}
.gredient-info-bg {
    background: linear-gradient(to right, #41a6f6 0, #425be0 100%);
}
.product-sales .c3-shape {
    stroke: transparent !important;
    stroke-width: 3px;
}
.earnings {
    position: relative;
    height: 250px;
}
.earnings .ct-series-a .ct-area {
    fill-opacity: 0.2;
    fill: url(#gradient);
}
.earnings .ct-series-a .ct-line,
.earnings .ct-series-a .ct-point {
    stroke: #2962ff;
    stroke-width: 2px;
}
.earnings .ct-series-b .ct-area {
    fill: #4fc3f7;
    fill-opacity: 0.1;
}
.earnings .ct-series-b .ct-line,
.earnings .ct-series-b .ct-point {
    stroke: #4fc3f7;
    stroke-width: 2px;
}
.earnings .ct-series-a .ct-point,
.earnings .ct-series-b .ct-point {
    stroke-width: 6px;
}
.ct-grid {
    stroke-width: 0.3px;
    stroke-dasharray: 0;
}
@media (max-width: 1023px) {
    .gredient-info-bg .info {
        margin-top: 15px;
    }
}
.ct-series-a .ct-bar {
    stroke: #2962ff;
}
.ct-series-b .ct-bar {
    stroke: #4fc3f7;
}
.auth-wrapper {
    min-height: 100vh;
    position: relative;
}
.auth-wrapper .auth-box {
    background: #fff;
    padding: 20px;
    box-shadow: 1px 0 20px rgba(0, 0, 0, 0.08);
    max-width: 400px;
    width: 90%;
    margin: 10% 0;
}
.auth-wrapper .auth-box .logo {
    text-align: center;
}
.auth-wrapper .auth-box.on-sidebar {
    top: 0;
    right: 0;
    height: 100%;
    margin: 0;
    position: absolute;
}
.auth-wrapper .auth-sidebar {
    position: fixed;
    height: 100%;
    right: 0;
    overflow: auto;
    margin: 0;
    top: 0;
}
@media (max-width: 767px) {
    .ct-bar {
        stroke-width: 10px !important;
    }
    .auth-wrapper .auth-sidebar {
        position: relative;
        max-width: 100%;
        width: 100%;
        margin: 40px 0 60px;
    }
    .auth-wrapper .demo-text {
        margin-top: 30px;
    }
    .email-table {
        min-width: 500px;
    }
}
.email-app {
    position: relative;
}
.email-app .list-group .list-group-item {
    padding: 0;
    background: 0 0;
    border: none;
}
.email-app .list-group .list-group-item .list-group-item-action {
    padding: 12px 15px;
    display: block;
    color: #3e5569;
}
.email-app .list-group .list-group-item .list-group-item-action .mdi {
    font-size: 18px;
    vertical-align: middle;
    margin-right: 5px;
}
.email-app .list-group .list-group-item .list-group-item-action.active,
.email-app .list-group .list-group-item .list-group-item-action:hover {
    background: rgba(0, 0, 0, 0.03);
}
.email-app .email-table {
    table-layout: fixed;
}
.email-app .email-table .selected {
    background: #fff8e1;
}
.email-app .email-table .max-texts,
.email-app .email-table .user-name h6 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.email-app .email-table .max-texts {
    padding: 1rem 5px;
}
.email-app .email-table .chb {
    width: 50px;
}
.email-app .email-table .time {
    width: 100px;
    text-align: right;
}
.email-app .email-table .clip,
.email-app .email-table .starred {
    width: 25px;
    padding: 1rem 5px;
}
.email-app .email-table .user-image {
    width: 45px;
    padding: 1rem 5px;
}
.email-app .email-table .user-name {
    width: 130px;
    padding: 1rem 5px;
}
.email-app .email-table .user-name .m-b-0 {
    font-weight: 300;
}
.email-app .email-table .unread .m-b-0,
.email-app .email-table .unread .max-texts {
    font-weight: 600;
}
.lobilists .lobilist {
    box-shadow: none;
}
.lobilists .lobilist.lobilist-primary {
    border-color: #e9ecef;
}
.lobilists .lobilist.lobilist-primary .lobilist-footer,
.lobilists .lobilist.lobilist-primary .lobilist-form-footer,
.lobilists .lobilist.lobilist-primary .lobilist-header {
    border-color: #7460ee;
    background-color: #7460ee;
}
.lobilists .lobilist.lobilist-danger {
    border-color: #e9ecef;
}
.lobilists .lobilist.lobilist-danger .lobilist-footer,
.lobilists .lobilist.lobilist-danger .lobilist-form-footer,
.lobilists .lobilist.lobilist-danger .lobilist-header {
    border-color: #f62d51;
    background-color: #f62d51;
}
.lobilists .lobilist.lobilist-info {
    border-color: #e9ecef;
}
.lobilists .lobilist.lobilist-info .lobilist-footer,
.lobilists .lobilist.lobilist-info .lobilist-form-footer,
.lobilists .lobilist.lobilist-info .lobilist-header {
    border-color: #2962ff;
    background-color: #2962ff;
}
.lobilists .lobilist.lobilist-success {
    border-color: #e9ecef;
}
.lobilists .lobilist.lobilist-success .lobilist-footer,
.lobilists .lobilist.lobilist-success .lobilist-header {
    border-color: #36bea6;
    background-color: #36bea6;
}
.lobilists .lobilist-footer,
.lobilists .lobilist-form-footer {
    border-color: #e9ecef !important;
    background: rgba(0, 0, 0, 0.02) !important;
}
.lobilists .btn-link {
    background: 0 0;
    border: 0;
    box-shadow: none;
    color: #343a40 !important;
}
.lobilists.single-line {
    height: 500px;
}
.lobilist,
.lobilists {
    position: relative;
}
.lobilists [type="checkbox"]:checked,
.lobilists [type="checkbox"]:not(:checked) {
    opacity: 1;
    position: relative;
}
.lobilists .lobilist-item {
    margin-bottom: 16px;
    padding-top: 5px;
}
.lobilists .lobilist-item-title {
    color: #343a40;
}
.lobilists .lobilist-item-description {
    font-style: normal;
    font-size: 13px;
}
.lobilists .lobilist-check {
    top: 10px;
}
.lobilists .lobilist-item-duedate {
    position: relative;
    font-size: 12px;
    left: 0;
}
.lobilists .lobilist-actions {
    top: 0;
}
.lobilist-actions .btn i {
    font-size: 15px;
    vertical-align: middle;
}
.twitter-typeahead {
    width: 100%;
}
.twitter-typeahead .tt-menu {
    width: 100%;
    background: #fff;
    border: 1px solid #f8f9fa;
    border-radius: 5px;
    padding: 0.75rem 0;
}
.twitter-typeahead .tt-menu .tt-suggestion {
    padding: 0.25rem 0.75rem;
    cursor: pointer;
}
.twitter-typeahead .tt-menu .tt-suggestion:hover {
    background-color: #7460ee;
    color: #fff;
}
.twitter-typeahead .empty-message {
    padding: 5px 10px;
    text-align: center;
}
.twitter-typeahead .rtl-typeahead .tt-menu {
    text-align: right;
}
.twitter-typeahead .league-name {
    margin: 0 10px 5px;
    padding: 7px 5px 10px;
    border-bottom: 1px solid #e9ecef;
}
.scrollable-dropdown .twitter-typeahead .tt-menu {
    max-height: 80px;
    overflow-y: auto;
}
.bootstrap-maxlength {
    margin-top: 0.5rem;
}
.el-element-overlay .white-box {
    padding: 0;
}
.el-element-overlay .el-card-item {
    position: relative;
    padding-bottom: 20px;
}
.el-element-overlay .el-card-item .el-card-avatar {
    margin-bottom: 20px;
}
.el-element-overlay .el-card-item .el-card-content {
    text-align: center;
}
.el-element-overlay .el-card-item .el-overlay-1 {
    width: 100%;
    overflow: hidden;
    position: relative;
    text-align: center;
    cursor: default;
}
.el-element-overlay .el-card-item .el-overlay-1 img {
    display: block;
    position: relative;
    -webkit-transition: all 0.4s linear;
    transition: all 0.4s linear;
    width: 100%;
    height: auto;
}
.el-element-overlay .el-card-item .el-overlay-1:hover img {
    -ms-transform: scale(1.2) translateZ(0);
    -webkit-transform: scale(1.2) translateZ(0);
}
.el-element-overlay .el-card-item .el-overlay-1 .el-info {
    text-decoration: none;
    display: inline-block;
    text-transform: uppercase;
    color: #fff;
    background-color: transparent;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    padding: 0;
    margin: auto;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%) translateZ(0);
    -webkit-transform: translateY(-50%) translateZ(0);
    -ms-transform: translateY(-50%) translateZ(0);
}
.el-element-overlay .el-card-item .el-overlay-1 .el-info .el-item {
    list-style: none;
    display: inline-block;
    margin: 0 3px;
}
.el-element-overlay .el-card-item .el-overlay-1 .el-info .el-item .el-link {
    border-color: #fff;
    color: #fff;
    padding: 12px 15px 10px;
}
.el-element-overlay .el-card-item .el-overlay-1 .el-info .el-item .el-link:hover {
    background: #2962ff;
    border-color: #2962ff;
}
.gmaps-overlay_arrow.above,
.gmaps-overlay_arrow.below {
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
}
.el-element-overlay .el-card-item .el-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
    top: 0;
    left: 0;
    opacity: 0;
    background-color: rgba(0, 0, 0, 0.7);
    -webkit-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
}
.el-element-overlay .el-card-item .el-overlay-1:hover .el-overlay {
    opacity: 1;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
}
.el-element-overlay .el-card-item .el-overlay-1 .scrl-dwn {
    top: -100%;
}
.el-element-overlay .el-card-item .el-overlay-1 .scrl-up {
    top: 100%;
    height: 0;
}
.el-element-overlay .el-card-item .el-overlay-1:hover .scrl-dwn {
    top: 0;
}
.el-element-overlay .el-card-item .el-overlay-1:hover .scrl-up {
    top: 0;
    height: 100%;
}
.gmaps,
.gmaps-panaroma {
    height: 300px;
    background: #e9ecef;
    border-radius: 2px;
}
.gmaps-overlay {
    display: block;
    text-align: center;
    color: #fff;
    font-size: 16px;
    line-height: 40px;
    background: #2962ff;
    border-radius: 4px;
    padding: 10px 20px;
}
.gmaps-overlay_arrow {
    left: 50%;
    margin-left: -16px;
    width: 0;
    height: 0;
    position: absolute;
}
.gmaps-overlay_arrow.above {
    bottom: -15px;
    border-top: 16px solid #2962ff;
}
.gmaps-overlay_arrow.below {
    top: -15px;
    border-bottom: 16px solid #2962ff;
}
.jvectormap-zoomin,
.jvectormap-zoomout {
    width: 10px;
    height: 10px;
    line-height: 10px;
}
.jvectormap-zoomout {
    top: 40px;
}
.timeline {
    position: relative;
    padding: 20px 0;
    list-style: none;
    max-width: 1200px;
    margin: 0 auto;
}
.timeline:before {
    content: " ";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 3px;
    margin-left: -1.5px;
    background-color: #e9ecef;
}
.timeline > .timeline-item {
    position: relative;
    margin-bottom: 20px;
}
.timeline > .timeline-item:after,
.timeline > .timeline-item:before {
    content: " ";
    display: table;
}
.timeline > .timeline-item:after {
    clear: both;
}
.timeline > .timeline-item > .timeline-panel {
    float: left;
    position: relative;
    width: 46%;
    padding: 20px;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}
.timeline > .timeline-item > .timeline-panel:before {
    content: " ";
    display: inline-block;
    position: absolute;
    top: 26px;
    right: -8px;
    border-top: 8px solid transparent;
    border-right: 0 solid #e9ecef;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #e9ecef;
}
.timeline > .timeline-item > .timeline-panel:after {
    content: " ";
    display: inline-block;
    position: absolute;
    top: 27px;
    right: -7px;
    border-top: 7px solid transparent;
    border-right: 0 solid #fff;
    border-bottom: 7px solid transparent;
    border-left: 7px solid #fff;
}
.timeline > .timeline-item > .timeline-badge {
    z-index: 10;
    position: absolute;
    top: 16px;
    left: 50%;
    width: 50px;
    height: 50px;
    margin-left: -25px;
    border-radius: 50%;
    text-align: center;
    font-size: 1.4em;
    line-height: 50px;
    color: #fff;
    overflow: hidden;
}
.timeline-left > .timeline-item > .timeline-panel,
.timeline-right > .timeline-item > .timeline-panel {
    width: calc(100% - 80px);
}
.timeline > .timeline-item.timeline-inverted > .timeline-panel {
    float: right;
}
.timeline > .timeline-item.timeline-inverted > .timeline-panel:before {
    right: auto;
    left: -8px;
    border-right-width: 8px;
    border-left-width: 0;
}
.timeline > .timeline-item.timeline-inverted > .timeline-panel:after {
    right: auto;
    left: -7px;
    border-right-width: 7px;
    border-left-width: 0;
}
.timeline-badge.primary {
    background-color: #7460ee;
}
.timeline-badge.success {
    background-color: #36bea6;
}
.timeline-badge.warning {
    background-color: #ffbc34;
}
.timeline-badge.danger {
    background-color: #f62d51;
}
.timeline-badge.info {
    background-color: #2962ff;
}
.timeline-title {
    margin-top: 0;
    color: inherit;
    font-weight: 400;
}
.dd-handle,
.dd-item > button {
    font-weight: 700;
    margin: 5px 0;
}
.timeline-body > p,
.timeline-body > ul {
    margin-bottom: 0;
}
.timeline-left:before {
    left: 30px;
}
.timeline-left > .timeline-item > .timeline-badge {
    left: 30px;
    top: 9px;
}
.timeline-right:before {
    right: 30px;
    left: auto;
}
.timeline-right > .timeline-item > .timeline-badge {
    right: 5px;
    top: 9px;
    left: auto;
}
.cd-horizontal-timeline .events a {
    padding-bottom: 6px;
    color: #2962ff;
}
.dd,
.dd-list {
    display: block;
    padding: 0;
    list-style: none;
}
.cd-horizontal-timeline .events a.selected::after,
.cd-horizontal-timeline .filling-line {
    background: #2962ff;
}
.cd-horizontal-timeline .events a.selected::after {
    border-color: #2962ff;
}
.cd-horizontal-timeline .m-t-40 {
    margin-top: 40px !important;
}
.dd,
.dd-list {
    margin: 0;
    position: relative;
}
.search-box .app-search .form-control,
.topbar .mega-dropdown .dropdown-menu {
    border-radius: 0;
}
.topbar .dropdown-menu .with-arrow > span {
    transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
}
.dd {
    max-width: 600px;
    font-size: 13px;
    line-height: 20px;
}
.dd-list .dd-list {
    padding-left: 30px;
}
.dd-collapsed .dd-list {
    display: none;
}
.dd-empty,
.dd-item,
.dd-placeholder {
    display: block;
    position: relative;
    margin: 0;
    padding: 0;
    min-height: 20px;
    font-size: 13px;
    line-height: 20px;
}
.dd-handle {
    display: block;
    height: 30px;
    padding: 5px 10px;
    cursor: move;
    color: #979898;
    text-decoration: none;
    border: 1px solid #e5e5e5;
    background: #fafafa;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}
.dd-handle:hover {
    color: #317eeb;
    background: #fff;
}
.dd-item > button {
    display: block;
    position: relative;
    cursor: pointer;
    float: left;
    width: 25px;
    height: 20px;
    padding: 0;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    border: 0;
    background: 0 0;
    font-size: 12px;
    line-height: 1;
    text-align: center;
}
.dd-item > button:before {
    content: "+";
    display: block;
    position: absolute;
    width: 100%;
    text-align: center;
    text-indent: 0;
}
.dd-item > button[data-action="collapse"]:before {
    content: "-";
}
.dd-empty,
.dd-placeholder {
    margin: 5px 0;
    padding: 0;
    min-height: 30px;
    background: #f5f5f5;
    border: 1px dashed #b6bcbf;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}
.dd-empty {
    border: 1px dashed #bbb;
    min-height: 100px;
    background-color: #e5e5e5;
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
}
.dd-dragel {
    position: absolute;
    z-index: 9999;
}
.dd-dragel > .dd-item .dd-handle {
    margin-top: 0;
}
.dd-dragel .dd-handle {
    -webkit-box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);
}
.dd3-content {
    display: block;
    height: 30px;
    margin: 5px 0;
    padding: 5px 10px 5px 40px;
    color: #979898;
    text-decoration: none;
    font-weight: 700;
    border: 1px solid #e5e5e5;
    background: #fafafa;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}
.dd3-content:hover {
    color: #317eeb;
    background: #fff;
}
.dd-dragel > .dd3-item > .dd3-content {
    margin: 0;
}
.dd3-item > button {
    margin-left: 30px;
}
.dd3-handle {
    position: absolute;
    margin: 0;
    left: 0;
    top: 0;
    cursor: pointer;
    width: 30px;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    border: 1px solid #ccc;
    background: #d5d5d5;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.dd3-handle:before {
    content: "\f0c9";
    font-family: "Font Awesome 5 Free";
    display: block;
    position: absolute;
    left: 0;
    width: 100%;
    text-align: center;
    text-indent: 0;
    font-size: 14px;
    font-weight: 900;
    color: #67757c;
    top: 7px;
}
.chat-list .chat-item.odd .chat-time,
.chat-list .chat-item.reverse {
    text-align: right;
}
.dd3-handle:hover {
    background: #404040;
    border: 1px solid #404040;
}
.myadmin-dd .dd-list .dd-item .dd-handle {
    background: #fff;
    border: 1px solid rgba(120, 130, 140, 0.13);
    padding: 8px 16px;
    height: auto;
    font-family: Montserrat, sans-serif;
    font-weight: 400;
    border-radius: 0;
}
.myadmin-dd-empty .dd-list .dd3-content {
    height: auto;
    border: 1px solid rgba(120, 130, 140, 0.13);
    padding: 8px 16px 8px 46px;
    background: #fff;
    font-weight: 400;
}
.myadmin-dd-empty .dd-list .dd3-handle {
    border: 1px solid rgba(120, 130, 140, 0.13);
    border-bottom: 0;
    background: #fff;
    height: 36px;
    width: 36px;
}
.mailbox .drop-title {
    font-weight: 600;
    padding: 11px 20px 15px;
    border-radius: 2px 2px 0 0;
    position: relative;
}
.mailbox .drop-title:after {
    content: "";
    position: absolute;
    background: url(../assets/images/background/img5.png) no-repeat;
    opacity: 0.2;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-size: cover;
}
.mailbox .nav-link {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    color: #3e5569;
}
.mailbox .message-center {
    height: 317px;
    overflow: auto;
    position: relative;
}
.mailbox .message-center .message-item {
    border-bottom: 1px solid #e9ecef;
    display: block;
    text-decoration: none;
    padding: 9px 15px;
}
.mailbox .message-center .message-item:hover {
    background: #f8f9fa;
}
.mailbox .message-center .message-item .message-title {
    color: #212529;
}
.mailbox .message-center .message-item .user-img {
    width: 40px;
    position: relative;
    display: inline-block;
    margin: 0 0 15px;
}
.mailbox .message-center .message-item .user-img img {
    width: 100%;
}
.mailbox .message-center .message-item .user-img .profile-status {
    border: 2px solid #fff;
    border-radius: 50%;
    display: inline-block;
    height: 10px;
    left: 30px;
    position: absolute;
    top: 1px;
    width: 10px;
}
.mailbox .message-center .message-item .user-img .online {
    background: #36bea6;
}
.mailbox .message-center .message-item .user-img .busy {
    background: #f62d51;
}
.mailbox .message-center .message-item .user-img .away,
.mailbox .message-center .message-item .user-img .offline {
    background: #ffbc34;
}
.mailbox .message-center .message-item .mail-contnet {
    display: inline-block;
    width: 75%;
    padding-left: 10px;
    vertical-align: middle;
}
.mailbox .message-center .message-item .mail-contnet .message-title {
    margin: 5px 0 0;
}
.mailbox .message-center .message-item .mail-contnet .mail-desc,
.mailbox .message-center .message-item .mail-contnet .time {
    font-size: 12px;
    display: block;
    margin: 1px 0;
    text-overflow: ellipsis;
    overflow: hidden;
    color: #a1aab2;
    white-space: nowrap;
}
.comment-widgets {
    position: relative;
    margin-bottom: 10px;
}
.comment-widgets .comment-row {
    border-bottom: 1px solid transparent;
    padding: 14px;
    display: flex;
    margin: 10px 0;
}
.comment-widgets .comment-row:last-child {
    border-bottom: 0;
}
.comment-widgets .comment-row.active,
.comment-widgets .comment-row:hover {
    background: rgba(0, 0, 0, 0.05);
}
.comment-text {
    padding-left: 15px;
    width: 100%;
}
.comment-text.active .comment-footer .action-icons,
.comment-text:hover .comment-footer .action-icons {
    visibility: visible;
}
.comment-text p {
    max-height: 65px;
    width: 100%;
    overflow: hidden;
}
.comment-footer .action-icons {
    visibility: hidden;
}
.comment-footer .action-icons a {
    padding-left: 7px;
    vertical-align: middle;
    color: #a1aab2;
}
.comment-footer .action-icons a.active,
.comment-footer .action-icons a:hover {
    color: #2962ff;
}
.chat-box {
    overflow: auto;
}
.chat-list .chat-item {
    list-style: none;
    margin-top: 30px;
}
.chat-list .chat-item .chat-img {
    display: inline-block;
    width: 45px;
    vertical-align: top;
}
.chat-list .chat-item .chat-img img {
    width: 45px;
    border-radius: 100%;
}
.chat-list .chat-item .chat-content {
    width: calc(100% - 50px);
    display: inline-block;
    padding-left: 15px;
}
.chat-list .chat-item .chat-content .box {
    display: inline-block;
    padding: 10px;
    margin-bottom: 3px;
    color: #343a40;
    background: #f8f9fa;
}
.chat-list .chat-item .chat-time {
    display: block;
    font-size: 10px;
    color: #4f5467;
    margin: 5px 0 15px 65px;
}
.chat-list .chat-item.odd .chat-content {
    text-align: right;
    width: calc(100% - 0px);
}
.chat-list .chat-item.odd .box {
    clear: both;
    color: #fff;
    background: #2962ff;
}
.chat-list .chat-item.odd + .odd {
    margin-top: 0;
}
.chat-list .chat-item.reverse .chat-time {
    text-align: left;
}
.chat-list .chat-item.reverse .chat-content {
    padding-left: 0;
    padding-right: 15px;
}
.chat-windows {
    position: fixed;
    bottom: 0;
    right: 300px;
    height: 350px;
    max-height: 350px;
    z-index: 9999;
    transition: 0.6s;
}
.chat-windows.show-chat {
    bottom: 0;
}
.chat-windows.hide-chat {
    bottom: -350px;
}
.chat-windows .user-chat {
    min-width: 300px;
    min-height: 350px;
    background-color: #fff;
    box-shadow: 1px 0 20px rgba(0, 0, 0, 0.08);
    float: right;
    margin: 0 2px 0 0;
}
.chat-windows .user-chat.mini-chat {
    min-width: 40px;
    min-height: 40px;
    bottom: 0;
    top: 310px;
    position: relative;
}
.chat-windows .user-chat.mini-chat .chat-body,
.chat-windows .user-chat.mini-chat .chat-footer,
.chat-windows .user-chat.mini-chat .chat-head .name,
.chat-windows .user-chat.mini-chat .chat-head i {
    display: none;
}
.chat-windows .user-chat.mini-chat .chat-head img {
    margin: 0;
}
.chat-windows .user-chat.mini-chat .chat-head .status {
    left: 28px;
    top: 10px;
}
.chat-windows .user-chat .chat-head {
    height: 40px;
    padding: 10px;
    background-color: #2962ff;
    color: #fff;
    position: relative;
}
.chat-windows .user-chat .chat-head img {
    border-radius: 50%;
    height: 25px;
    width: 25px;
    vertical-align: middle;
    margin: -4px 8px 0 0;
    display: inline-block;
    cursor: pointer;
}
.chat-windows .user-chat .chat-head .status {
    border: 1px solid #fff;
    border-radius: 50%;
    position: absolute;
    height: 8px;
    width: 8px;
    left: 28px;
    top: 7px;
}
.chat-windows .user-chat .chat-head .online {
    background-color: #36bea6;
}
.chat-windows .user-chat .chat-head .busy {
    background-color: #f62d51;
}
.chat-windows .user-chat .chat-head .away {
    background-color: #fb8c00;
}
.chat-windows .user-chat .chat-head .offline {
    background-color: #ffbc34;
}
.chat-windows .user-chat .chat-head i {
    font-size: 14px;
    float: right;
    color: #fff;
    margin: 3px 0 0 5px;
    cursor: pointer;
}
.chat-windows .user-chat .chat-footer {
    padding: 0;
    border-top: 1px solid #e9ecef;
}
.chat-windows .user-chat .chat-footer .form-control {
    background-color: transparent;
    color: #3e5569;
    font-size: 14px;
    border: 0;
    width: 100%;
    height: 40px;
}
.chat-windows .user-chat .chat-footer .form-control:focus,
.chat-windows .user-chat .chat-footer .form-control:hover {
    border: 0;
}
.chat-windows .user-chat .chat-body {
    height: 270px;
    padding: 15px 15px 0;
    background-color: #191821;
    overflow: hidden;
    max-width: 300px;
    position: relative;
}
.profiletimeline .sl-left img,
.steamline .sl-left img {
    max-width: 40px;
}
.chat-list {
    margin: 0;
    padding: 0;
}
.chat-list .msg_receive,
.chat-list .msg_sent {
    list-style: none;
    margin-top: 30px;
}
.chat-list .msg_receive .chat-img,
.chat-list .msg_sent .chat-img {
    display: inline-block;
    width: 45px;
    vertical-align: top;
}
.chat-list .msg_receive .chat-img img,
.chat-list .msg_sent .chat-img img {
    width: 100%;
    border-radius: 100%;
}
.chat-list .msg_receive .chat-content,
.chat-list .msg_sent .chat-content {
    width: calc(100% - 50px);
    display: inline-block;
    padding-left: 15px;
}
.chat-list .msg_receive .chat-content .box,
.chat-list .msg_sent .chat-content .box {
    display: inline-block;
    padding: 10px;
    margin-bottom: 3px;
    background: #fff;
}
.chat-list .msg_receive .chat-time,
.chat-list .msg_sent .chat-time {
    display: block;
    font-size: 10px;
    color: #a1aab2;
    margin: 5px 0 15px 15px;
}
.chat-list .msg_receive.odd .chat-content,
.chat-list .msg_sent.odd .chat-content {
    text-align: right;
    width: calc(100% - 0px);
}
.chat-list .msg_receive.odd .chat-content .box,
.chat-list .msg_sent.odd .chat-content .box {
    clear: both;
    color: #fff;
    background: #2962ff;
}
.chat-list .msg_receive .reverse,
.chat-list .msg_receive.odd .chat-time,
.chat-list .msg_sent .reverse,
.chat-list .msg_sent.odd .chat-time {
    text-align: right;
}
.chat-list .msg_receive.odd + .odd,
.chat-list .msg_sent.odd + .odd {
    margin-top: 0;
}
.chat-list .msg_receive.reverse .chat-content,
.chat-list .msg_sent.reverse .chat-content {
    padding-left: 0;
    padding-right: 15px;
}
.chat-list .msg_receive.reverse .chat-time,
.chat-list .msg_sent.reverse .chat-time {
    text-align: left;
}
.mailbox .chat-scroll {
    height: calc(100vh - 100px);
}
.steamline {
    position: relative;
    border-left: 1px solid #e9ecef;
    margin-left: 20px;
}
.steamline .sl-left {
    float: left;
    margin-left: -20px;
    z-index: 1;
    width: 40px;
    line-height: 40px;
    text-align: center;
    height: 40px;
    border-radius: 100%;
    color: #fff;
    margin-right: 15px;
}
.steamline .sl-right {
    padding-left: 30px;
}
.steamline .sl-right .desc,
.steamline .sl-right .inline-photos {
    margin-bottom: 21px;
}
.steamline .sl-item {
    border-bottom: 1px solid #e9ecef;
    margin: 20px 0;
}
.steamline .sl-item:last-child {
    border-bottom: none;
}
.sl-date {
    font-size: 10px;
    color: #a1aab2;
}
.time-item {
    border-color: #e9ecef;
    padding-bottom: 1px;
    position: relative;
}
.time-item:before {
    content: " ";
    display: table;
}
.time-item:after {
    background-color: #fff;
    border-color: #e9ecef;
    border-radius: 10px;
    border-style: solid;
    border-width: 2px;
    bottom: 0;
    content: "";
    height: 14px;
    left: 0;
    margin-left: -8px;
    position: absolute;
    top: 5px;
    width: 14px;
}
.time-item-item:after {
    content: " ";
    display: table;
}
.item-info {
    margin-bottom: 15px;
    margin-left: 15px;
}
.item-info p {
    margin-bottom: 10px !important;
}
.feed-widget .feed-body .feed-item {
    padding: 12px 0;
    display: flex;
    align-items: center;
}
.feed-widget .feed-body .feed-item:hover {
    background: #f8f9fa;
}
.feed-widget .feed-body .feed-item > .feed-icon {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    border-radius: 100%;
    color: #fff;
}
.feed-widget .feed-body .feed-item > .feed-icon i {
    line-height: 40px;
}
.todo-widget .todo-list .todo-item {
    border: 0;
    margin-bottom: 0;
    padding: 20px 15px 15px 0;
}
.todo-widget .todo-list .todo-item .custom-checkbox {
    width: 100%;
}
.todo-widget .todo-list .todo-item .custom-checkbox .todo-label {
    font-weight: 400;
    width: 100%;
}
.todo-widget .todo-list .todo-item .assignedto {
    padding: 0 0 0 25px;
    margin: 0;
}
.todo-widget .todo-list .todo-item .assignedto .assignee {
    padding: 0;
    display: inline-block;
    border: 0;
    margin-right: 2px;
}
.todo-widget .todo-list .todo-item .assignedto .assignee .assignee-img {
    width: 30px;
    border-radius: 100%;
}
#marketking_dashboard_wrapper[data-layout="horizontal"][data-header-position="fixed"] .topbar,
#marketking_dashboard_wrapper[data-layout="horizontal"][data-sidebar-position="fixed"] .topbar {
    width: 100%;
    position: fixed;
}
.todo-widget .todo-list .todo-item .item-date {
    padding-left: 25px;
    font-size: 12px;
    margin-top: 5px;
    display: inline-block;
    color: #a1aab2;
}
.list-task .task-done .badge,
.list-task .task-done .todo-desc {
    text-decoration: line-through;
}
.profiletimeline {
    position: relative;
    padding-left: 40px;
    margin: 40px 10px 0 30px;
    border-left: 1px solid #e9ecef;
}
.profiletimeline .sl-left {
    float: left;
    margin-left: -60px;
    z-index: 1;
    margin-right: 15px;
}
.profiletimeline .sl-item {
    margin-top: 8px;
    margin-bottom: 30px;
}
.profiletimeline .sl-date {
    font-size: 12px;
    color: #a1aab2;
}
#marketking_dashboard_wrapper[data-layout="horizontal"][data-header-position="fixed"] .left-sidebar {
    padding-top: 64px;
}
#marketking_dashboard_wrapper[data-layout="horizontal"][data-sidebar-position="fixed"] .left-sidebar {
    padding-top: 64px;
    position: fixed;
}
#marketking_dashboard_wrapper[data-layout="horizontal"][data-sidebar-position="fixed"] .marketking_dashboard_page_wrapper {
    padding-top: 78px;
}
@media (min-width: 768px) {
    #marketking_dashboard_wrapper[data-layout="horizontal"] {
        background: #191821;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .topbar {
        transition: 0s;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"][data-boxed-layout="boxed"] .marketking_dashboard_page_wrapper,
    #marketking_dashboard_wrapper[data-layout="horizontal"][data-boxed-layout="boxed"] .scroll-sidebar,
    #marketking_dashboard_wrapper[data-layout="horizontal"][data-boxed-layout="boxed"] .top-navbar {
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"][data-boxed-layout="boxed"] .sidebar-nav #sidebarnav {
        flex-wrap: wrap;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"][data-boxed-layout="boxed"] .sidebar-nav #sidebarnav > .sidebar-item > .has-arrow:after {
        display: block;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"][data-boxed-layout="boxed"] .sidebar-nav .sidebar-item {
        flex: 1 1 0;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"][data-sidebar-position="fixed"] .marketking_dashboard_page_wrapper {
        padding-top: 128px;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .top-navbar .navbar-header {
        width: 200px;
        border-right: 1px solid rgba(0, 0, 0, 0.1);
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .topbar .sidebartoggler {
        display: none;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar {
        width: 100%;
        height: auto;
        position: relative;
        padding-top: 0;
        z-index: 45;
        transition: 0s;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .left-sidebar .scroll-sidebar {
        height: 54px;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav ul {
        display: flex;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .scroll-sidebar {
        height: auto;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav {
        display: flex;
        width: 100%;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav .sidebar-item {
        position: relative;
        width: auto;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item {
        border-right: 1px solid rgba(0, 0, 0, 0.1);
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item > .has-arrow:after {
        transform: rotate(-135deg) translate(0, -50%);
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item:last-child > .first-level {
        right: 0;
        left: auto;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item > .two-column + .first-level {
        width: 400px;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item > .two-column + .first-level > .sidebar-item {
        float: left;
        width: 50%;
        vertical-align: top;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item ul {
        position: absolute;
        left: 0;
        top: auto;
        width: 220px;
        padding-bottom: 0;
        z-index: 100;
        display: none;
        box-shadow: 5px 10px 20px rgba(0, 0, 0, 0.1);
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav ul.first-level > .sidebar-item:hover ul.second-level,
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item:hover ul.first-level,
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item:hover ul.first-level.collapse {
        display: block;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .mega-dropdown {
        position: static;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .mega-dropdown .first-level {
        width: 100%;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .mega-dropdown .first-level > li {
        width: 25%;
        float: left;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item:hover {
        background: rgba(0, 0, 0, 0.025);
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item:hover ul.first-level.collapse:after,
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item:hover ul.first-level:after {
        position: absolute;
        content: "";
        background: rgba(0, 0, 0, 0.025);
        top: 0;
        height: 100%;
        width: 100%;
        left: 0;
        z-index: -1;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item ul.second-level {
        left: 220px;
        top: 0;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item .first-level .right-side-dd ul.second-level,
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item:last-child > .first-level ul.second-level {
        right: 220px;
        top: 0;
        left: auto;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav .badge,
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav .nav-small-cap,
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav .sidebar-footer,
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav .user-pro,
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item > .has-arrow:after {
        display: none;
    }
}
@media (min-width: 768px) and (max-width: 1023px) {
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav > ul > .sidebar-item > .sidebar-link i {
        display: none;
    }
    #marketking_dashboard_wrapper[data-layout="horizontal"] .sidebar-nav #sidebarnav > .sidebar-item {
        flex: auto;
    }
    [data-sidebar-position="fixed"] .left-sidebar {
        overflow: auto;
    }
}
@media (min-width: 768px) {
    .bc-content {
        justify-content: flex-end;
    }
}
@media (max-width: 991.98px) {
    .do-block {
        display: block !important;
    }
}
