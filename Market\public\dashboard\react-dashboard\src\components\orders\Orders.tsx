import React from 'react';

const Orders: React.FC = () => {
  return (
    <div className="nk-content">
      <div className="nk-content-inner">
        <div className="nk-content-body">
          <div className="nk-block-head nk-block-head-sm">
            <div className="nk-block-between">
              <div className="nk-block-head-content">
                <h4 className="nk-block-title page-title"><PERSON><PERSON><PERSON><PERSON><PERSON></h4>
                <div className="nk-block-des text-soft">
                  <p>Siparişlerinizi görüntüleyin ve yönetin.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="nk-block">
            <div className="card card-stretch">
              <div className="card-inner-group">
                <div className="card-inner position-relative card-tools-toggle">
                  <div className="card-title-group">
                    <div className="card-tools">
                      <div className="form-inline flex-nowrap gx-3">
                        <div className="form-wrap w-150px">
                          <select className="form-select form-control-sm">
                            <option value="">Tüm Durumlar</option>
                            <option value="pending">Bekleyen</option>
                            <option value="processing">İşleniyor</option>
                            <option value="completed">Tamamlandı</option>
                            <option value="cancelled">İptal Edildi</option>
                          </select>
                        </div>
                        <div className="form-wrap flex-grow-1">
                          <div className="form-icon form-icon-right">
                            <em className="icon ni ni-search"></em>
                          </div>
                          <input 
                            type="text" 
                            className="form-control" 
                            placeholder="Sipariş ara..."
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="card-inner p-0">
                  <div className="nk-tb-list nk-tb-ulist">
                    <div className="nk-tb-item nk-tb-head">
                      <div className="nk-tb-col">
                        <span className="sub-text">Sipariş</span>
                      </div>
                      <div className="nk-tb-col tb-col-mb">
                        <span className="sub-text">Müşteri</span>
                      </div>
                      <div className="nk-tb-col tb-col-md">
                        <span className="sub-text">Tarih</span>
                      </div>
                      <div className="nk-tb-col">
                        <span className="sub-text">Tutar</span>
                      </div>
                      <div className="nk-tb-col tb-col-lg">
                        <span className="sub-text">Durum</span>
                      </div>
                      <div className="nk-tb-col nk-tb-col-tools text-right">
                        <span className="sub-text">İşlemler</span>
                      </div>
                    </div>
                    
                    {/* Sipariş listesi buraya gelecek */}
                    <div className="nk-tb-item">
                      <div className="nk-tb-col-12">
                        <div className="text-center py-4">
                          <em className="icon ni ni-bag" style={{ fontSize: '3rem', color: '#c4c4c4' }}></em>
                          <p className="text-soft mt-2">Henüz sipariş bulunmuyor</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Orders;
