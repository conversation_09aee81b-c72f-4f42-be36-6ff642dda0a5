import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useDashboard } from '../../contexts/DashboardContext';
import { MenuItem } from '../../types';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const location = useLocation();
  const { vendor, stats } = useDashboard();

  // Menü <PERSON> tanımla
  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'ni-dashboard-fill',
      path: '/',
    },
    {
      id: 'products',
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      icon: 'ni-package-fill',
      path: '/products',
    },
    {
      id: 'orders',
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      icon: 'ni-bag-fill',
      path: '/orders',
      badge: stats?.pending_orders || 0,
    },
    {
      id: 'earnings',
      label: 'Kazançlar',
      icon: 'ni-coins',
      path: '/earnings',
    },
    {
      id: 'payouts',
      label: '<PERSON>demeler',
      icon: 'ni-wallet-out',
      path: '/payouts',
    },
    {
      id: 'reviews',
      label: '<PERSON><PERSON><PERSON>lendirmeler',
      icon: 'ni-reports-alt',
      path: '/reviews',
    },
    {
      id: 'profile',
      label: 'Ayarlar',
      icon: 'ni-account-setting-fill',
      path: '/profile',
    },
  ];

  const isActiveRoute = (path: string): boolean => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="nk-sidebar-overlay" 
          onClick={onClose}
        ></div>
      )}
      
      <div className={`nk-sidebar nk-sidebar-fixed is-light ${isOpen ? 'nk-sidebar-active' : ''}`}>
        <div className="nk-sidebar-element nk-sidebar-head">
          <div className="nk-sidebar-brand">
            <Link to="/" className="logo-link nk-sidebar-logo">
              <img 
                className="logo-small logo-img logo-img-small" 
                src="/wp-content/plugins/marketking-core/public/dashboard/assets/images/logo-small.png" 
                alt="logo-small" 
              />
            </Link>
          </div>
          <div className="nk-menu-trigger mr-n2">
            <button 
              className="nk-nav-toggle nk-quick-nav-icon d-xl-none" 
              onClick={onClose}
            >
              <em className="icon ni ni-arrow-left"></em>
            </button>
          </div>
        </div>
        
        <div className="nk-sidebar-element">
          <div className="nk-sidebar-content">
            <div className="nk-sidebar-menu">
              <ul className="nk-menu">
                <li className="nk-menu-heading">
                  <h6 className="overline-title text-primary-alt">
                    {vendor?.store_name || 'Mağaza'}
                  </h6>
                </li>
                
                {menuItems.map((item) => (
                  <li 
                    key={item.id} 
                    className={`nk-menu-item ${isActiveRoute(item.path) ? 'active current-page' : ''}`}
                  >
                    <Link 
                      to={item.path} 
                      className="nk-menu-link"
                      onClick={() => window.innerWidth < 1200 && onClose()}
                    >
                      <span className="nk-menu-icon">
                        <em className={`icon ${item.icon}`}></em>
                      </span>
                      <span className="nk-menu-text">{item.label}</span>
                      {item.badge && item.badge > 0 && (
                        <span className="nk-menu-badge badge-danger">
                          {item.badge} Yeni
                        </span>
                      )}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
