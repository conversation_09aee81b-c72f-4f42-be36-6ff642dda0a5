import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { DashboardProvider } from './contexts/DashboardContext';
import Layout from './components/layout/Layout';
import Dashboard from './components/dashboard/Dashboard';
import Products from './components/products/Products';
import Orders from './components/orders/Orders';
import Earnings from './components/earnings/Earnings';
import Profile from './components/profile/Profile';
import './styles/main.scss';

const App: React.FC = () => {
  return (
    <DashboardProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/products" element={<Products />} />
            <Route path="/orders" element={<Orders />} />
            <Route path="/earnings" element={<Earnings />} />
            <Route path="/profile" element={<Profile />} />
          </Routes>
        </Layout>
      </Router>
    </DashboardProvider>
  );
};

export default App;
