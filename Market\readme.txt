=== MarketKing — Ultimate WooCommerce Multivendor Marketplace Solution ===
Plugin URI: https://woocommerce-multivendor.com/
Contributors: WebWizardsDev
Donate link: https://webwizards.dev
Tags: 	multivendor marketplace, multivendor, marketplace, woocommerce multivendor, woocommerce marketplace
Author URI: webwizards.dev
Author: WebWizards
Requires at least: 4.8
Tested up to: 6.8
Requires PHP: 5.6.20
Stable tag: 2.0.70
Version: 2.0.70
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
MarketKing is the modern, **next-gen multivendor marketplace platform.** Build your dream markeplace with stunning UX and powerful features.

== Description ==

MarketKing is the modern, **next-gen multivendor marketplace platform.** Build your dream markeplace with stunning UX and powerful features.

🏆 **Top Featured Marketplace Solution** – Featured by WPLift, Envato Market, BusinessBloomer, and many other top WP sites.

🚀 MarketKing is a **bestseller** and one of **fastest-growing WP plugins**. It is currently a weekly bestseller on CodeCanyon.

👉 [Live Demo (Free)](https://demobk1.wpbay.co/marketking-demos/ "Live Demo")

👉 [Documentation](https://woocommerce-multivendor.com/docs "Documentation")

👉 [Check out MarketKing Premium](https://kingsplugins.com/woocommerce-multivendor/marketking/ "Check out MarketKing Premium")

== MarketKing – #1 WooCommerce Multivendor Marketplace ==

Grow a thriving marketplace business, while earning commission and subscription fees. Whether you’re building a marketplace for fashion items, foods, arts, toys, jewelry, electronics or even digital software downloads, MarketKing can do it all!

There are **137+ features and modules**, plus dozens of integrations: a powerful seller dashboard, branded vendor invoices, a complex commissions system, seller registration, single product multiple vendors, badges, seller staff, earnings reports, and so much more!

[youtube https://www.youtube.com/watch?v=fTt_RmfOx7c]


== Free Plugin: Key Features ==

**VENDOR SIDE**

* **Modern vendor dashboard with stunning UX**
* Separate dedicated login page for vendors
* Order statistics and sales charts 
* Vendors create and manage products
* Vendors manage inventory, shipping, product categories, attributes, images, tags, and more.
* Order management
* Products and orders are downloadable as CSV / PDF lists
* Payouts panel with configuration and history
* Profile and vendor store setting configuration

**FRONT-END**

* **Unique modern split-cart system - cart is split by vendor - highest conversion rates**
* Configurable vendor store front for each vendor
* Stores page where all vendors can be searched and sorted
* Vendor information added to each product
* Translatable to any language
* Vendor registration system
* Compatible with all WooCommerce themes

**BACK-END - ADMIN**

* **Vendor account review and approval system**
* **Admin review and approval of vendor products**
* Admin dashboard with charts and statistics
* System to send payouts and notifications
* Backend management of vendors and profiles
* Commission configuration
* Custom dashboard logo and branding

== MarketKing Premium Features ==

== Stripe Connect Split Payments ==

* Vendors get paid directly and automatically in their Stripe accounts
* Supports 3D Secure 2 and latest Stripe features
* Full standalone solution and replacement for other Stripe plugins
* Payments are split automatically between vendors and admin (supports split-cart)
* Vendors can connect their Stripe accounts easily via dashboard
* Choose whether the vendor or admin pays processing fees
* Automatic refund system with no admin intervention

[youtube https://www.youtube.com/watch?v=PXLwKkCViHE]

== Shipping Tracking == 

* Vendors can enter tracking details for their shipments
* Customers are notified when shipments are sent
* Customers can easily track packages online
* Supports DHL, TNT, Fedex, UPS, DPD, USPS, Royal Mail and more
* Support custom shipping providers
* Customers can confirm if they received packages

== Elementor Integration == 

* You can use Elementor to design custom vendor store pages
* 8 native Elementor widgets to choose from
* You can use any other features in the Elementor ecosystem

== Complex Commissions System ==

* Configure flat, percentage and mixed commissions
* Set commissions by vendor, product category, or product tag
* Control tax and shipping fee recipient
* Reverse COD payments option

== Vendor Membership Packages & Subscriptions ==

* Vendors pay subscriptions to use the marketplace
* Vendors can pay to unlock features and options
* Pay to unlock product number limits, types, special badges, product upsells, cross-sells, product categories etc.
* Compatible with WooCommerce Subscriptions, YITH Subscriptions, Subscriptio, Sumo Subscriptions
* Beautiful built-in package designs

== Vendor Products Import & Export ==

* Add or edit products in bulk through built-in import system
* Export lists of products through exporter
* Control and edit products via CSV files
* Uses powerful native WooCommerce products importer

== Single Product Multiple Vendors ==

* Multiple vendors can sell the same product
* 'Other Offers' panel shown beneath each product
* Animated 'other offers' panel shows top products only
* Sort offers by price, vendor rating or stock qty
* Smart cache system automatically updates when stock or price changes
* Optionally vendors can be restricted to only selling store products
* Add to Store button allows vendors to sell any product easily

== Product & Vendor Inquiries ==

* Customers can ask questions easily under each product
* Customers can ask questions easily on each vendor page
* Inquiries open messaging panel and create conversations
* Vendors and customers can communicate back-and-forth through messaging system
* Configurable email notifications

== Marketplace Reports & Statistics ==

* View reports by vendor, or for the entire marketplace
* Beautiful visual charts and stats
* 3 separate charts track commissions, numbers of orders, and total sales
* Separate tracker for number of new vendors across time
* View charts by date and vendor

== Refund Requests == 

* Customers can request refunds via the order page
* Refund requests create conversations between vendor and customer
* Vendors receive refund requests and email notifications
* Vendors can approve or reject refunds, with no admin intervention
* Refunds can be processed manually or automatically with Stripe

== Vendor Badges & Achievements ==

* Configure custom badges to be awarded to vendors and shown on store pages
* Vendors can pay to obtain certain badges (badges can be set by package / group)
* Set badge conditions: total sales, order numbers, registration time
* Example: badge awarded automatically when vendor reaches $100,000 in sales
* Example: badge awarded automatically when vendor reaches 5th year since registration
* Smart cache refreshes badges once every 24 hrs for blazing fast performance.

== Registration System & Form Builder ==

* Configure custom vendor registration forms
* Choose from 9 types of custom form fields: text, dropdown, date, file upload, etc.
* Vendor manual or automatic approval system
* Set separate registration page, or use my account page
* Approval system with email notifications
* VAT Number VIES validation for vendor companies (optional)
* Compatible with any theme

== Vendor Product Management ==

* Variable products, grouped products, external products
* Linked products, upsells and crosssells
* Virtual and downloadable products support
* Supports custom product fields and options (uses native Woo system).
* Any product option available to admin can also be available to vendors.

== Digital Products Support ==

* Sell any kind of digital product: e-books, art, software, music, etc
* Uses native WooCommerce digital downloads system
* Vendors can upload files directly via the dashboard and connect them to products
* Customers receive automatic access when orders are finalized.

== Announcements System ==

* Vendors can make announcements for specific vendors or vendor groups
* Vendors can read announcements through their dashboard
* Ideal for news and for communicating with vendors en masse

== Vendor Earnings & Reports ==

* Vendors can view charts and statistics for their sales by month
* Each transaction and commission is tracked
* Transaction table can be downloaded as CSV or PDF

== Messaging Platform ==

* Powerful built-in communication system
* Admin can communicate with vendors to receive inquiries or send messages
* Vendors can communicate with customers
* Admin can communicate with customers
* Messages are accompanied by email notifications

== Vendor Vacations ==

* Vendors can pause store operations to go on vacation
* Close shop immediately or schedule close
* Show a custom notice while shop is closed
* Products are hidden from shop while on vacation

== Favorite Stores == 

* Customers can 'follow' specific stores
* Follow button on each vendor store page
* Customers can view a page with products from only their favorite stores
* Functions and hooks for customizations and implementations

== Store Reviews ==

* Customers can leave reviews for products, which will show on the vendor page
* Dedicated 'feedback' tab on each vendor's store page
* Vendors can respond to reviews
* Vendors can report reviews to the website admin, who can be the decision-maker

== Document Verification ==

* Admin can require documents from vendors to verify them
* Any document can be configured (e.g. ID, Business license, etc.)
* Vendors upload files via their dashboards
* Document log keeps a history of all uploads and files
* Email notifications when items are approved or rejeceted

== Store Notices == 

* Vendors can show a custom notification message on their store pages
* Perfect for situations like delays, important news, etc.
* Configurable easily by each vendor via their dashboard

== Seller Documentation & Knowledgebase ==

* Knowledgebase section in the vendor dashboard with articles and info
* Admin can write documentation articles for store vendors
* Vendors can read articles for easy access to info
* Perfect for onboarding vendor and for providing vendors with important info
* Perfect for FAQs and How To guides for vendors

== Payouts ==

* Admin can choose which payment methods to make available to vendors
* Vendors choose a method and enter their payment details
* Admin can pay vendors and enter details into the system
* Vendors can make withdrawal requests optionally
* Payouts log and history
* Payout notification emails

== Coupon Management ==

* Vendors can configure and manage coupons via their dashboard
* Set fixed discount coupons or percentage discounts
* Set coupon expiry dates
* Set usage restrictions and usage limits for coupons
* Set min and max spend, products excluded, etc.

== Order Management ==

* Dedicated order management pages in vendor dashboard
* View order billing and shipping info
* Add shipping tracking info
* Add order notes / send notes to customers

== Advanced Vendor Shipping ==

* Each vendor can configure their own shipping methods for each shipping zone
* Vendors can configure method cost, tax status, use shipping classes, etc.
* Customers can choose a shipping method from the ones enables by the vendor 

== Color Scheme Customization ==

* Configure color scheme and options for the vendor dashboard
* 3 Built-in color schemes

== Vendor Groups & Memberships ==

* Organize vendors into groups
* Make specific features and panels available to specific groups only
* Set limits by group: max product number, product types, etc.
* Example: Group A can sell 50 products, Group B can sell 100 products
* Example: Group A cannot set product upsells, Group B can.
* Example: Group A has access to SEO options, Group B does not.

== Group Transfer Rules ==

* Automatically change vendor groups based on conditions.
* Example: When vendors hit $50,000 in sales, they are moved from group 'Regular' (15% commission) to group 'VIP' (10% commission)
* Example: When vendors hit $1,000,000 in sales, they are moved to the group 'Legendary', which is associated with the 'Legendary' special badge. The badge then becomes available on vendor store pages automatically.

== Store Support - 3 Support Methods ==

* Vendors can provide support through 3 methods:
* - MarketKing's own messaging module can be used
* - External URL (e.g. ticketing system, forum) can be used
* - Dedicated support email can be used
* Support requests can be sent by customers using product and order pages.
* Automated email notifications

== Vendor Teams & Staff ==

* Vendors can create accounts for their team members
* Team members can help with store management tasks, handling orders, products, etc.
* Permissions system allows vendors to control precisely which features team members can use.
* Example: 1 team member is in charge of refunds and can only view the orders and refunds panels
* Example: 1 team member is in charge of support and can only access the messages panel

== Invoices, Packing Slips, Shipping Labels ==

* Compatible with most popular invoice and labels plugins.
* Vendors can configure their store name, address, logo and info to be on invoices
* Customers can download invoices via their account panels
* Vendors can download invoices and packing slips as PDF files

== Store SEO ==

* Vendors can configure a title, description and keywords for their store pages
* Vendors can use this to personalize store page appearance in search

== Store Policies == 

* Vendors can configure their own terms and conditions
* Selected HTML tags are supported for formatting
* Dedicated policies tab is displayed on the vendor's store page

== Abuse Reports for Products & Reviews ==

* Customers can report specific products and provide a reason
* Ideal to combat abusive / spam / infringent products
* Admin can review all reports easily in the site backend
* Help maintain a clean and safe marketplace

== Wholesale & B2B – B2BKing Integration ==

* Integration with the best-selling B2BKing B2B and Wholesale plugin
* Vendors can configure quantity discounts and wholesale prices
* Vendors can configure product visibility
* Vendors can set minimum and max order rules
* Hide products from guests or set order conditions
* Receive and respond to quote requests
* Vendors can create offers and set special prices for specific users
* Set package (box / carton) quantities on products
* Tiered pricing table and custom information table
* Limit coupon usage by b2c / b2b
* and more...

== New Features ==

* Product Bundles
* Social Media Sharing
* Bookings Management - Reservations, Appointments, Accommodation
* Subscriptions - Recurring Revenue
* Advertising - Vendors Purchase Ads
* Many new features added constantly


== AND MUCH MORE… ==

* **Many more features** and integrations across features
* **New features constantly added. Get in touch with us for pre-sales questions.**

👉 [Get MarketKing - The Ultimate WooCommerce Multivendor Marketplace Plugin](https://woocommerce-multivendor.com "Get MarketKing - The Ultimate WooCommerce Multivendor Marketplace Plugin")

== Plugin Presentation ==
[youtube https://www.youtube.com/watch?v=TWL1cWEyjsI]

== Reviews ==
⭐⭐⭐⭐⭐


🎉 *"An excellent plugin. You can find all your needs. I would definitely recommend."*

🎉 *"The level of everything is awesome! I predict this will be the "go to" marketplace platform in the next 6+ months..!"*

🎉 *"Bought it for $139, but it's worth $1390."*

🎉 *"This is one of the best multi vendor plugin, if not the best. I was using WCFM Ultimate plugin on my site - it's a great plugin but it is cluttered. It also needs a lot of plugins for it to be fully functional. Plus, you will pay an annual fee to keep it going. WCFM also has a MASSIVE DATABASE FOOTPRINT - Creates about 36 tables on the database. So I moved to MarketKing - So far I am ecstatic with the plugin features and support from the WebWizardsDev Team"*

🎉 *"Envato is asking me to select one reason before rating but actually this plugin is 5 stars in every criteria. Dozens of modules are available to customize or enhance the features of this multi vendor plugin, the UX/UI is amazing and it just works. The support is superior. The speed of development or bug fixing is super fast. Forget about the others like Dokan, WC Vendors and what else is existing. They lack in every criteria I described above since I used and tested each of them. This plugin is done thoroughly and with perfection in mind. I hope this plugin gets the attention and support it deserves."*

🎉 *"Awesome plugins! The customer support team is very supportive. I have purchased both MarketKing and B2BKing. I think this is the best deal to get an alternate of Dookan etc. All plugins are translatable and have a decent vendor dashboard. I will keep updating this review."*

🎉 *"I run a membership site that is complicated. I've been looking for a Multi Vendor plugin for almost 2 years and I had my eyes set on Dokan Pro (Business level). I've compared a lot of the available Multi Vendor Plugins and I must say you get A LOT for your money with this plugin. I was lucky enough to come across this when it was on sale, what a bargain! - Love it and would highly recommend it to everyone.*


== Installation ==
1. Go to: Plugins > Add New > Upload > select the plugin zip and click "Install Now"
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to "MarketKing" in the left side menu
4. Go to "Settings -> Main Settings" and configure the plugin

== Upgrade Notice ==

= 1.1.0 =
Changelog: https://woocommerce-multivendor.com/changelog/

= 1.0.0 =
* Initial Release

== Screenshots ==
1. Vendor Dashboard
2. Frontend Storefront Example
3. Plugin Modules
4. Vendor Dashboard
5. Single Product Multiple Vendors (Other Offers)
6. Split Cart Functionality
7. Vendor Membership & Subscription Options
8. Vendor Earnings Panel
9. Vendor Order Management - Order Details Page
10. Vendor Dashboard Login Panel
11. Payouts Panel
12. Split Cart System


== Changelog ==

Changelog: https://woocommerce-multivendor.com/changelog 

== Frequently Asked Questions ==

= Is WooCommerce necessary for this plugin to work? =

Yes. This plugin requires WooCommerce

