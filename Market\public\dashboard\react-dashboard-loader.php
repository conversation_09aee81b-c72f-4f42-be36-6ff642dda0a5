<?php

if (!defined('ABSPATH')) { exit; }

/**
 * MarketKing React Dashboard Loader
 * React tabanlı dashboard'u yüklemek için gerekli fonksiyonlar
 */

class MarketKing_React_Dashboard_Loader {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_react_assets'));
        add_action('wp_ajax_marketking_react_api', array($this, 'handle_react_api'));
        add_action('wp_ajax_nopriv_marketking_react_api', array($this, 'handle_react_api'));
    }
    
    /**
     * React dashboard asset'lerini yükle
     */
    public function enqueue_react_assets() {
        // Sadece dashboard sayfasında yükle
        if (!$this->is_dashboard_page()) {
            return;
        }
        
        $react_dir = MARKETKINGCORE_URL . 'public/dashboard/react-dashboard/dist/';
        $react_path = MARKETKINGCORE_DIR . 'public/dashboard/react-dashboard/dist/';
        
        // Manifest dosyasını kontrol et
        $manifest_file = $react_path . 'asset-manifest.json';
        if (file_exists($manifest_file)) {
            $manifest = json_decode(file_get_contents($manifest_file), true);
            
            // CSS dosyalarını yükle
            if (isset($manifest['files']['main.css'])) {
                wp_enqueue_style(
                    'marketking-react-dashboard',
                    $react_dir . ltrim($manifest['files']['main.css'], '/'),
                    array(),
                    MARKETKINGCORE_VERSION
                );
            }
            
            // JS dosyalarını yükle
            if (isset($manifest['files']['main.js'])) {
                wp_enqueue_script(
                    'marketking-react-dashboard',
                    $react_dir . ltrim($manifest['files']['main.js'], '/'),
                    array('wp-api'),
                    MARKETKINGCORE_VERSION,
                    true
                );
            }
            
            // Vendor chunk'ı varsa yükle
            if (isset($manifest['files']['vendors.js'])) {
                wp_enqueue_script(
                    'marketking-react-vendors',
                    $react_dir . ltrim($manifest['files']['vendors.js'], '/'),
                    array(),
                    MARKETKINGCORE_VERSION,
                    true
                );
            }
        } else {
            // Development modunda basit yükleme
            wp_enqueue_style(
                'marketking-react-dashboard',
                $react_dir . 'main.css',
                array(),
                MARKETKINGCORE_VERSION
            );
            
            wp_enqueue_script(
                'marketking-react-dashboard',
                $react_dir . 'main.js',
                array('wp-api'),
                MARKETKINGCORE_VERSION,
                true
            );
        }
        
        // React için gerekli verileri localize et
        wp_localize_script('marketking-react-dashboard', 'marketkingReactData', array(
            'apiUrl' => rest_url('marketking/v1'),
            'nonce' => wp_create_nonce('wp_rest'),
            'userId' => get_current_user_id(),
            'isVendor' => marketking()->is_vendor(get_current_user_id()),
            'vendorId' => get_current_user_id(),
            'dashboardUrl' => get_page_link(get_option('marketking_vendordash_page_setting')),
            'translations' => array(
                'loading' => __('Yükleniyor...', 'marketking-multivendor-marketplace-for-woocommerce'),
                'error' => __('Bir hata oluştu', 'marketking-multivendor-marketplace-for-woocommerce'),
                'noData' => __('Veri bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'),
            )
        ));
    }
    
    /**
     * Dashboard sayfası kontrolü
     */
    private function is_dashboard_page() {
        global $post;
        
        if (!$post) {
            return false;
        }
        
        $dashboard_page_id = get_option('marketking_vendordash_page_setting');
        return $post->ID == $dashboard_page_id;
    }
    
    /**
     * React API isteklerini handle et
     */
    public function handle_react_api() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_REQUEST['nonce'], 'wp_rest')) {
            wp_die('Security check failed');
        }
        
        // Vendor kontrolü
        $user_id = get_current_user_id();
        if (!marketking()->is_vendor($user_id)) {
            wp_die('Unauthorized');
        }
        
        $action = sanitize_text_field($_REQUEST['react_action']);
        
        switch ($action) {
            case 'get_vendor_stats':
                $this->get_vendor_stats($user_id);
                break;
                
            case 'get_vendor_orders':
                $this->get_vendor_orders($user_id);
                break;
                
            case 'get_earnings_chart':
                $this->get_earnings_chart($user_id);
                break;
                
            case 'get_orders_chart':
                $this->get_orders_chart($user_id);
                break;
                
            default:
                wp_send_json_error('Invalid action');
        }
    }
    
    /**
     * Vendor istatistiklerini getir
     */
    private function get_vendor_stats($user_id) {
        $stats = array(
            'total_orders' => $this->get_vendor_total_orders($user_id),
            'total_customers' => $this->get_vendor_total_customers($user_id),
            'total_products' => $this->get_vendor_total_products($user_id),
            'total_earnings' => $this->get_vendor_total_earnings($user_id),
            'monthly_earnings' => $this->get_vendor_monthly_earnings($user_id),
            'pending_orders' => $this->get_vendor_pending_orders($user_id),
            'completed_orders' => $this->get_vendor_completed_orders($user_id),
            'cancelled_orders' => $this->get_vendor_cancelled_orders($user_id),
        );
        
        wp_send_json_success($stats);
    }
    
    /**
     * Vendor siparişlerini getir
     */
    private function get_vendor_orders($user_id) {
        $limit = isset($_REQUEST['limit']) ? intval($_REQUEST['limit']) : 10;
        $offset = isset($_REQUEST['offset']) ? intval($_REQUEST['offset']) : 0;
        
        $args = array(
            'limit' => $limit,
            'offset' => $offset,
            'type' => 'shop_order',
            'meta_key' => '_post_author',
            'meta_value' => $user_id,
            'meta_compare' => '=',
            'orderby' => 'date',
            'order' => 'DESC',
        );
        
        $orders = wc_get_orders($args);
        $formatted_orders = array();
        
        foreach ($orders as $order) {
            $formatted_orders[] = array(
                'id' => $order->get_id(),
                'order_number' => $order->get_order_number(),
                'customer_name' => $order->get_formatted_billing_full_name(),
                'customer_email' => $order->get_billing_email(),
                'date' => $order->get_date_created()->format('Y-m-d H:i:s'),
                'status' => $order->get_status(),
                'total' => $order->get_total(),
                'currency' => $order->get_currency(),
            );
        }
        
        wp_send_json_success($formatted_orders);
    }
    
    // Yardımcı fonksiyonlar (basit implementasyon)
    private function get_vendor_total_orders($user_id) {
        $args = array(
            'limit' => -1,
            'type' => 'shop_order',
            'meta_key' => '_post_author',
            'meta_value' => $user_id,
            'meta_compare' => '=',
            'return' => 'ids',
        );
        return count(wc_get_orders($args));
    }
    
    private function get_vendor_total_customers($user_id) {
        // Basit implementasyon - gerçek projede daha detaylı olmalı
        return 0;
    }
    
    private function get_vendor_total_products($user_id) {
        $args = array(
            'post_type' => 'product',
            'author' => $user_id,
            'post_status' => 'any',
            'posts_per_page' => -1,
            'fields' => 'ids',
        );
        $products = get_posts($args);
        return count($products);
    }
    
    private function get_vendor_total_earnings($user_id) {
        $balance = get_user_meta($user_id, 'marketking_outstanding_earnings', true);
        return floatval($balance);
    }
    
    private function get_vendor_monthly_earnings($user_id) {
        // Basit implementasyon - gerçek projede ay bazında hesaplama yapılmalı
        return 0;
    }
    
    private function get_vendor_pending_orders($user_id) {
        $args = array(
            'status' => array('wc-processing'),
            'limit' => -1,
            'type' => 'shop_order',
            'meta_key' => '_post_author',
            'meta_value' => $user_id,
            'meta_compare' => '=',
            'return' => 'ids',
        );
        return count(wc_get_orders($args));
    }
    
    private function get_vendor_completed_orders($user_id) {
        $args = array(
            'status' => array('wc-completed'),
            'limit' => -1,
            'type' => 'shop_order',
            'meta_key' => '_post_author',
            'meta_value' => $user_id,
            'meta_compare' => '=',
            'return' => 'ids',
        );
        return count(wc_get_orders($args));
    }
    
    private function get_vendor_cancelled_orders($user_id) {
        $args = array(
            'status' => array('wc-cancelled'),
            'limit' => -1,
            'type' => 'shop_order',
            'meta_key' => '_post_author',
            'meta_value' => $user_id,
            'meta_compare' => '=',
            'return' => 'ids',
        );
        return count(wc_get_orders($args));
    }
}

// Instance oluştur
MarketKing_React_Dashboard_Loader::get_instance();
